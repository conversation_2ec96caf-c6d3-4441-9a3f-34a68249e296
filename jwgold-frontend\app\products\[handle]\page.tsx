'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { motion } from 'framer-motion';
import Image from 'next/image';
import { shopifyHelpers } from '@/lib/shopify';
import { useCart } from '@/context/CartContext';
import { StarIcon, HeartIcon, ShieldCheckIcon, TruckIcon } from '@heroicons/react/24/outline';
import { HeartIcon as HeartIconSolid } from '@heroicons/react/24/solid';

export default function ProductPage() {
  const params = useParams();
  const handle = params.handle as string;
  const [product, setProduct] = useState<any>(null);
  const [selectedVariant, setSelectedVariant] = useState<any>(null);
  const [selectedImage, setSelectedImage] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [isWishlisted, setIsWishlisted] = useState(false);
  const [loading, setLoading] = useState(true);
  const { addToCart } = useCart();

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        setLoading(true);
        const fetchedProduct = await shopifyHelpers.fetchProductByHandle(handle);
        if (fetchedProduct) {
          setProduct(fetchedProduct);
          setSelectedVariant(fetchedProduct.variants?.[0] || null);
        }
      } catch (error) {
        console.error('Error fetching product:', error);
      } finally {
        setLoading(false);
      }
    };

    if (handle) {
      fetchProduct();
    }
  }, [handle]);

  const handleAddToCart = async () => {
    if (!selectedVariant || !product) return;

    try {
      await addToCart({
        variantId: selectedVariant.id,
        quantity,
        title: product.title,
        price: selectedVariant.price,
        image: product.images?.[0] ? {
          src: product.images[0].src,
          altText: product.images[0].altText || product.title,
        } : undefined,
        handle: product.handle,
      });
      alert('Added to cart!');
    } catch (error) {
      console.error('Error adding to cart:', error);
      alert('Error adding to cart. Please try again.');
    }
  };

  if (loading) {
    return (
      <div className="bg-white">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
          <div className="animate-pulse">
            <div className="lg:grid lg:grid-cols-2 lg:gap-x-8">
              <div className="aspect-square bg-gray-200 rounded-lg"></div>
              <div className="mt-8 lg:mt-0 space-y-4">
                <div className="h-8 bg-gray-200 rounded w-3/4"></div>
                <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                <div className="h-20 bg-gray-200 rounded"></div>
                <div className="h-12 bg-gray-200 rounded w-1/3"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="bg-white">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16 text-center">
          <h1 className="text-2xl font-bold text-gray-900">Product not found</h1>
          <p className="mt-4 text-gray-600">The product you're looking for doesn't exist.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
        <div className="lg:grid lg:grid-cols-2 lg:gap-x-8 lg:items-start">
          {/* Image gallery */}
          <div className="flex flex-col">
            <div className="aspect-square w-full overflow-hidden rounded-lg bg-gray-200">
              {product.images && product.images.length > 0 ? (
                <Image
                  src={product.images[selectedImage]?.src || product.images[0].src}
                  alt={product.images[selectedImage]?.altText || product.title}
                  width={600}
                  height={600}
                  className="h-full w-full object-cover object-center"
                />
              ) : (
                <div className="flex items-center justify-center h-full">
                  <span className="text-gray-400">No image available</span>
                </div>
              )}
            </div>

            {/* Image thumbnails */}
            {product.images && product.images.length > 1 && (
              <div className="mt-4 flex gap-2 overflow-x-auto">
                {product.images.map((image: any, index: number) => (
                  <button
                    key={image.id}
                    onClick={() => setSelectedImage(index)}
                    className={`flex-shrink-0 aspect-square w-20 overflow-hidden rounded-md ${
                      selectedImage === index ? 'ring-2 ring-gold' : ''
                    }`}
                  >
                    <Image
                      src={image.src}
                      alt={image.altText || product.title}
                      width={80}
                      height={80}
                      className="h-full w-full object-cover object-center"
                    />
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Product info */}
          <div className="mt-10 px-4 sm:px-0 sm:mt-16 lg:mt-0">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h1 className="text-3xl font-bold font-playfair text-gray-900">{product.title}</h1>
              
              <div className="mt-3">
                <p className="text-3xl text-gray-900">
                  {selectedVariant ? shopifyHelpers.formatPrice(selectedVariant.price) : 'Price unavailable'}
                </p>
                {selectedVariant?.compareAtPrice && (
                  <p className="text-lg text-gray-500 line-through">
                    {shopifyHelpers.formatPrice(selectedVariant.compareAtPrice)}
                  </p>
                )}
              </div>

              {/* Reviews */}
              <div className="mt-3 flex items-center">
                <div className="flex items-center">
                  {[0, 1, 2, 3, 4].map((rating) => (
                    <StarIcon
                      key={rating}
                      className="h-5 w-5 text-gold fill-current"
                      aria-hidden="true"
                    />
                  ))}
                </div>
                <p className="ml-3 text-sm text-gray-600">5.0 out of 5 stars (24 reviews)</p>
              </div>

              <div className="mt-6">
                <h3 className="sr-only">Description</h3>
                <div className="text-base text-gray-700 space-y-6">
                  <p>{product.description}</p>
                </div>
              </div>

              {/* Options */}
              {product.options && product.options.length > 0 && (
                <div className="mt-8">
                  {product.options.map((option: any) => (
                    <div key={option.id} className="mb-6">
                      <h3 className="text-sm font-medium text-gray-900">{option.name}</h3>
                      <div className="mt-2 flex flex-wrap gap-2">
                        {option.values.map((value: string) => (
                          <button
                            key={value}
                            className="px-4 py-2 text-sm border border-gray-300 rounded-md hover:border-gold focus:outline-none focus:ring-2 focus:ring-gold"
                          >
                            {value}
                          </button>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* Quantity and Add to Cart */}
              <div className="mt-8">
                <div className="flex items-center space-x-4">
                  <div>
                    <label htmlFor="quantity" className="block text-sm font-medium text-gray-700">
                      Quantity
                    </label>
                    <select
                      id="quantity"
                      value={quantity}
                      onChange={(e) => setQuantity(parseInt(e.target.value))}
                      className="mt-1 block w-20 rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-gold focus:outline-none focus:ring-gold"
                    >
                      {[1, 2, 3, 4, 5].map((num) => (
                        <option key={num} value={num}>
                          {num}
                        </option>
                      ))}
                    </select>
                  </div>

                  <button
                    onClick={handleAddToCart}
                    disabled={!selectedVariant?.available}
                    className="flex-1 btn-gold py-3 px-8 text-lg font-semibold rounded-md transition-all duration-300 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {selectedVariant?.available ? 'Add to Cart' : 'Sold Out'}
                  </button>

                  <button
                    onClick={() => setIsWishlisted(!isWishlisted)}
                    className="p-3 border border-gray-300 rounded-md hover:border-gold"
                  >
                    {isWishlisted ? (
                      <HeartIconSolid className="h-6 w-6 text-red-500" />
                    ) : (
                      <HeartIcon className="h-6 w-6 text-gray-400" />
                    )}
                  </button>
                </div>
              </div>

              {/* Features */}
              <div className="mt-8 border-t border-gray-200 pt-8">
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <div className="flex items-center">
                    <ShieldCheckIcon className="h-5 w-5 text-gold mr-2" />
                    <span className="text-sm text-gray-600">Lifetime Warranty</span>
                  </div>
                  <div className="flex items-center">
                    <TruckIcon className="h-5 w-5 text-gold mr-2" />
                    <span className="text-sm text-gray-600">Free Shipping</span>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
}
