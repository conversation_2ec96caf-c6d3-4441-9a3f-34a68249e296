@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --gold: #d4af37;
  --gold-light: #f4e4a6;
  --gold-dark: #b8941f;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-inter);
  --font-serif: var(--font-playfair);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-inter), system-ui, sans-serif;
}

/* Custom jewelry-themed styles */
.font-playfair {
  font-family: var(--font-playfair), serif;
}

.font-inter {
  font-family: var(--font-inter), system-ui, sans-serif;
}

/* Gold accent colors */
.text-gold {
  color: var(--gold);
}

.bg-gold {
  background-color: var(--gold);
}

.border-gold {
  border-color: var(--gold);
}

/* Smooth transitions for jewelry showcase */
.transition-all {
  transition: all 0.3s ease;
}

/* Custom scrollbar for cart */
.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Image hover effects for product cards */
.product-image {
  transition: transform 0.3s ease;
}

.product-image:hover {
  transform: scale(1.05);
}

/* Elegant button styles */
.btn-gold {
  background: linear-gradient(135deg, var(--gold) 0%, var(--gold-dark) 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
}

.btn-gold:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
}
