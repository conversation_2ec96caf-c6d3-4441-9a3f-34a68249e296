{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/components/ProductCard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { motion } from 'framer-motion';\nimport { useCart } from '@/context/CartContext';\nimport { shopifyHelpers } from '@/lib/shopify';\nimport { HeartIcon, ShoppingBagIcon } from '@heroicons/react/24/outline';\nimport { HeartIcon as HeartIconSolid } from '@heroicons/react/24/solid';\n\ninterface ProductCardProps {\n  product: any; // Shopify product type\n}\n\nexport default function ProductCard({ product }: ProductCardProps) {\n  const [isWishlisted, setIsWishlisted] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const { addToCart } = useCart();\n\n  const handleAddToCart = async (e: React.MouseEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    \n    if (!product.variants || product.variants.length === 0) return;\n    \n    setIsLoading(true);\n    \n    const variant = product.variants[0]; // Use first variant for quick add\n    \n    try {\n      await addToCart({\n        variantId: variant.id,\n        quantity: 1,\n        title: product.title,\n        price: variant.price,\n        image: product.images?.[0] ? {\n          src: product.images[0].src,\n          altText: product.images[0].altText || product.title,\n        } : undefined,\n        handle: product.handle,\n      });\n    } catch (error) {\n      console.error('Error adding to cart:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleWishlist = (e: React.MouseEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsWishlisted(!isWishlisted);\n  };\n\n  const primaryImage = product.images?.[0];\n  const secondaryImage = product.images?.[1];\n  const minPrice = product.priceRange?.minVariantPrice;\n  const maxPrice = product.priceRange?.maxVariantPrice;\n  const hasVariantPricing = minPrice && maxPrice && minPrice.amount !== maxPrice.amount;\n\n  return (\n    <motion.div\n      whileHover={{ y: -5 }}\n      transition={{ duration: 0.3 }}\n      className=\"group relative bg-white rounded-lg shadow-sm hover:shadow-lg transition-shadow duration-300\"\n    >\n      <Link href={`/products/${product.handle}`}>\n        <div className=\"relative aspect-square w-full overflow-hidden rounded-t-lg bg-gray-200\">\n          {primaryImage ? (\n            <>\n              <Image\n                src={primaryImage.src}\n                alt={primaryImage.altText || product.title}\n                fill\n                className=\"object-cover object-center group-hover:scale-105 transition-transform duration-300\"\n                sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw\"\n              />\n              {secondaryImage && (\n                <Image\n                  src={secondaryImage.src}\n                  alt={secondaryImage.altText || product.title}\n                  fill\n                  className=\"object-cover object-center opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                  sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw\"\n                />\n              )}\n            </>\n          ) : (\n            <div className=\"w-full h-full bg-gray-200 flex items-center justify-center\">\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 mx-auto mb-2 bg-gray-300 rounded-full flex items-center justify-center\">\n                  <svg className=\"w-8 h-8 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z\" />\n                  </svg>\n                </div>\n                <span className=\"text-gray-400 text-sm\">No Image</span>\n              </div>\n            </div>\n          )}\n\n          {/* Overlay buttons */}\n          <div className=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300\">\n            <div className=\"absolute top-4 right-4 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n              <button\n                onClick={handleWishlist}\n                className=\"p-2 bg-white rounded-full shadow-md hover:bg-gray-50 transition-colors duration-200\"\n              >\n                {isWishlisted ? (\n                  <HeartIconSolid className=\"h-5 w-5 text-red-500\" />\n                ) : (\n                  <HeartIcon className=\"h-5 w-5 text-gray-600\" />\n                )}\n              </button>\n            </div>\n\n            <div className=\"absolute bottom-4 left-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n              <button\n                onClick={handleAddToCart}\n                disabled={isLoading || !product.variants?.[0]?.available}\n                className=\"w-full bg-white text-gray-900 py-2 px-4 rounded-md font-medium hover:bg-gray-50 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2\"\n              >\n                {isLoading ? (\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900\"></div>\n                ) : (\n                  <>\n                    <ShoppingBagIcon className=\"h-4 w-4\" />\n                    {product.variants?.[0]?.available ? 'Quick Add' : 'Sold Out'}\n                  </>\n                )}\n              </button>\n            </div>\n          </div>\n\n          {/* Sale badge */}\n          {product.variants?.[0]?.compareAtPrice && (\n            <div className=\"absolute top-4 left-4\">\n              <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800\">\n                Sale\n              </span>\n            </div>\n          )}\n        </div>\n\n        <div className=\"p-4\">\n          <div className=\"mb-2\">\n            <h3 className=\"text-sm font-medium text-gray-900 line-clamp-2 group-hover:text-gold transition-colors duration-200\">\n              {product.title}\n            </h3>\n            {product.vendor && (\n              <p className=\"text-xs text-gray-500 mt-1\">{product.vendor}</p>\n            )}\n          </div>\n\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex flex-col\">\n              {hasVariantPricing ? (\n                <span className=\"text-sm font-medium text-gray-900\">\n                  {shopifyHelpers.formatPrice(minPrice)} - {shopifyHelpers.formatPrice(maxPrice)}\n                </span>\n              ) : minPrice ? (\n                <div className=\"flex items-center gap-2\">\n                  <span className=\"text-sm font-medium text-gray-900\">\n                    {shopifyHelpers.formatPrice(minPrice)}\n                  </span>\n                  {product.variants?.[0]?.compareAtPrice && (\n                    <span className=\"text-xs text-gray-500 line-through\">\n                      {shopifyHelpers.formatPrice(product.variants[0].compareAtPrice)}\n                    </span>\n                  )}\n                </div>\n              ) : (\n                <span className=\"text-sm text-gray-500\">Price unavailable</span>\n              )}\n            </div>\n\n            {/* Rating stars placeholder */}\n            <div className=\"flex items-center\">\n              {[...Array(5)].map((_, i) => (\n                <svg\n                  key={i}\n                  className=\"h-3 w-3 text-gold\"\n                  fill=\"currentColor\"\n                  viewBox=\"0 0 20 20\"\n                >\n                  <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n                </svg>\n              ))}\n            </div>\n          </div>\n        </div>\n      </Link>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AATA;;;;;;;;;;AAee,SAAS,YAAY,EAAE,OAAO,EAAoB;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IAE5B,MAAM,kBAAkB,OAAO;QAC7B,EAAE,cAAc;QAChB,EAAE,eAAe;QAEjB,IAAI,CAAC,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,MAAM,KAAK,GAAG;QAExD,aAAa;QAEb,MAAM,UAAU,QAAQ,QAAQ,CAAC,EAAE,EAAE,kCAAkC;QAEvE,IAAI;YACF,MAAM,UAAU;gBACd,WAAW,QAAQ,EAAE;gBACrB,UAAU;gBACV,OAAO,QAAQ,KAAK;gBACpB,OAAO,QAAQ,KAAK;gBACpB,OAAO,QAAQ,MAAM,EAAE,CAAC,EAAE,GAAG;oBAC3B,KAAK,QAAQ,MAAM,CAAC,EAAE,CAAC,GAAG;oBAC1B,SAAS,QAAQ,MAAM,CAAC,EAAE,CAAC,OAAO,IAAI,QAAQ,KAAK;gBACrD,IAAI;gBACJ,QAAQ,QAAQ,MAAM;YACxB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,gBAAgB,CAAC;IACnB;IAEA,MAAM,eAAe,QAAQ,MAAM,EAAE,CAAC,EAAE;IACxC,MAAM,iBAAiB,QAAQ,MAAM,EAAE,CAAC,EAAE;IAC1C,MAAM,WAAW,QAAQ,UAAU,EAAE;IACrC,MAAM,WAAW,QAAQ,UAAU,EAAE;IACrC,MAAM,oBAAoB,YAAY,YAAY,SAAS,MAAM,KAAK,SAAS,MAAM;IAErF,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,YAAY;YAAE,GAAG,CAAC;QAAE;QACpB,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAU;kBAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;YAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,MAAM,EAAE;;8BACvC,8OAAC;oBAAI,WAAU;;wBACZ,6BACC;;8CACE,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAK,aAAa,GAAG;oCACrB,KAAK,aAAa,OAAO,IAAI,QAAQ,KAAK;oCAC1C,IAAI;oCACJ,WAAU;oCACV,OAAM;;;;;;gCAEP,gCACC,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAK,eAAe,GAAG;oCACvB,KAAK,eAAe,OAAO,IAAI,QAAQ,KAAK;oCAC5C,IAAI;oCACJ,WAAU;oCACV,OAAM;;;;;;;yDAKZ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;4CAAwB,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDAC/E,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAGzE,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;;;;;;;sCAM9C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,SAAS;wCACT,WAAU;kDAET,6BACC,8OAAC,+MAAA,CAAA,YAAc;4CAAC,WAAU;;;;;iEAE1B,8OAAC,iNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAK3B,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,SAAS;wCACT,UAAU,aAAa,CAAC,QAAQ,QAAQ,EAAE,CAAC,EAAE,EAAE;wCAC/C,WAAU;kDAET,0BACC,8OAAC;4CAAI,WAAU;;;;;iEAEf;;8DACE,8OAAC,6NAAA,CAAA,kBAAe;oDAAC,WAAU;;;;;;gDAC1B,QAAQ,QAAQ,EAAE,CAAC,EAAE,EAAE,YAAY,cAAc;;;;;;;;;;;;;;;;;;;wBAQ3D,QAAQ,QAAQ,EAAE,CAAC,EAAE,EAAE,gCACtB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CAA8F;;;;;;;;;;;;;;;;;8BAOpH,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;gCAEf,QAAQ,MAAM,kBACb,8OAAC;oCAAE,WAAU;8CAA8B,QAAQ,MAAM;;;;;;;;;;;;sCAI7D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACZ,kCACC,8OAAC;wCAAK,WAAU;;4CACb,8GAAA,CAAA,iBAAc,CAAC,WAAW,CAAC;4CAAU;4CAAI,8GAAA,CAAA,iBAAc,CAAC,WAAW,CAAC;;;;;;+CAErE,yBACF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DACb,8GAAA,CAAA,iBAAc,CAAC,WAAW,CAAC;;;;;;4CAE7B,QAAQ,QAAQ,EAAE,CAAC,EAAE,EAAE,gCACtB,8OAAC;gDAAK,WAAU;0DACb,8GAAA,CAAA,iBAAc,CAAC,WAAW,CAAC,QAAQ,QAAQ,CAAC,EAAE,CAAC,cAAc;;;;;;;;;;;6DAKpE,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;8CAK5C,8OAAC;oCAAI,WAAU;8CACZ;2CAAI,MAAM;qCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;4CAEC,WAAU;4CACV,MAAK;4CACL,SAAQ;sDAER,cAAA,8OAAC;gDAAK,GAAE;;;;;;2CALH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcvB", "debugId": null}}, {"offset": {"line": 382, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport Link from 'next/link';\n\nexport default function <PERSON>() {\n  return (\n    <div className=\"relative bg-gray-900 overflow-hidden\">\n      {/* Background Video */}\n      <div className=\"absolute inset-0\">\n        <video\n          autoPlay\n          muted\n          loop\n          playsInline\n          className=\"w-full h-full object-cover\"\n        >\n          <source src=\"/Jewelry_GIF_for_Website_Hero.mp4\" type=\"video/mp4\" />\n        </video>\n        {/* Video overlay for better text readability */}\n        <div className=\"absolute inset-0 bg-black bg-opacity-50\"></div>\n      </div>\n\n      <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 sm:py-32 lg:py-40\">\n        <div className=\"text-center\">\n          <motion.h1\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            className=\"text-4xl sm:text-5xl lg:text-6xl font-bold font-playfair text-white leading-tight\"\n          >\n            Exquisite Jewelry\n            <br />\n            <span className=\"text-gold\">Crafted with Love</span>\n          </motion.h1>\n\n          <motion.p\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            className=\"mt-6 text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed\"\n          >\n            Discover our collection of handcrafted jewelry pieces, where traditional artistry meets contemporary elegance.\n            Each piece tells a unique story of passion, precision, and timeless beauty.\n          </motion.p>\n\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n            className=\"mt-10 flex flex-col sm:flex-row gap-4 justify-center items-center\"\n          >\n            <Link\n              href=\"/collections\"\n              className=\"btn-gold px-8 py-4 text-lg font-semibold rounded-lg transition-all duration-300 hover:scale-105\"\n            >\n              Shop Collection\n            </Link>\n            <Link\n              href=\"/about\"\n              className=\"px-8 py-4 text-lg font-semibold text-white border-2 border-white rounded-lg hover:bg-white hover:text-gray-900 transition-all duration-300\"\n            >\n              Our Story\n            </Link>\n          </motion.div>\n\n          {/* Features */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.6 }}\n            className=\"mt-16 grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-4xl mx-auto\"\n          >\n            <div className=\"text-center\">\n              <div className=\"inline-flex items-center justify-center w-12 h-12 bg-gold rounded-full mb-4\">\n                <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-lg font-semibold text-white mb-2\">Premium Quality</h3>\n              <p className=\"text-gray-300\">Only the finest materials and gemstones</p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"inline-flex items-center justify-center w-12 h-12 bg-gold rounded-full mb-4\">\n                <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4\" />\n                </svg>\n              </div>\n              <h3 className=\"text-lg font-semibold text-white mb-2\">Handcrafted</h3>\n              <p className=\"text-gray-300\">Meticulously crafted by skilled artisans</p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"inline-flex items-center justify-center w-12 h-12 bg-gold rounded-full mb-4\">\n                <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-lg font-semibold text-white mb-2\">Lifetime Warranty</h3>\n              <p className=\"text-gray-300\">Quality guaranteed for generations</p>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Decorative elements */}\n      <div className=\"absolute top-1/4 left-10 w-20 h-20 border border-gold opacity-20 rounded-full animate-pulse\"></div>\n      <div className=\"absolute bottom-1/4 right-10 w-16 h-16 border border-gold opacity-20 rounded-full animate-pulse\" style={{ animationDelay: '1s' }}></div>\n      <div className=\"absolute top-1/2 right-1/4 w-12 h-12 border border-gold opacity-20 rounded-full animate-pulse\" style={{ animationDelay: '2s' }}></div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,QAAQ;wBACR,KAAK;wBACL,IAAI;wBACJ,WAAW;wBACX,WAAU;kCAEV,cAAA,8OAAC;4BAAO,KAAI;4BAAoC,MAAK;;;;;;;;;;;kCAGvD,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;;gCACX;8CAEC,8OAAC;;;;;8CACD,8OAAC;oCAAK,WAAU;8CAAY;;;;;;;;;;;;sCAG9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCACX;;;;;;sCAKD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAqB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC5E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAG/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAqB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC5E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAG/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAqB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC5E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOrC,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;gBAAkG,OAAO;oBAAE,gBAAgB;gBAAK;;;;;;0BAC/I,8OAAC;gBAAI,WAAU;gBAAgG,OAAO;oBAAE,gBAAgB;gBAAK;;;;;;;;;;;;AAGnJ", "debugId": null}}, {"offset": {"line": 755, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/components/FeaturedCollections.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport Link from 'next/link';\n\nconst collections = [\n  {\n    name: 'Rings',\n    description: 'Elegant rings for every occasion',\n    href: '/collections/rings',\n    imageSrc: '/api/placeholder/400/500',\n    imageAlt: 'Beautiful gold and diamond rings',\n  },\n  {\n    name: 'Necklaces',\n    description: 'Stunning necklaces to complement your style',\n    href: '/collections/necklaces',\n    imageSrc: '/api/placeholder/400/500',\n    imageAlt: 'Elegant gold and silver necklaces',\n  },\n  {\n    name: 'Earrings',\n    description: 'Exquisite earrings for the perfect finish',\n    href: '/collections/earrings',\n    imageSrc: '/api/placeholder/400/500',\n    imageAlt: 'Beautiful diamond and gold earrings',\n  },\n  {\n    name: 'Bracelets',\n    description: 'Sophisticated bracelets for any wrist',\n    href: '/collections/bracelets',\n    imageSrc: '/api/placeholder/400/500',\n    imageAlt: 'Luxury gold and silver bracelets',\n  },\n];\n\nexport default function FeaturedCollections() {\n  return (\n    <section className=\"py-16 sm:py-24 bg-white\">\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center\">\n          <motion.h2\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            className=\"text-3xl font-bold font-playfair text-gray-900 sm:text-4xl\"\n          >\n            Our Collections\n          </motion.h2>\n          <motion.p\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.1 }}\n            className=\"mt-4 text-lg text-gray-600 max-w-2xl mx-auto\"\n          >\n            Explore our carefully curated collections, each designed to capture the essence of elegance and sophistication.\n          </motion.p>\n        </div>\n\n        <div className=\"mt-12 grid grid-cols-1 gap-y-10 gap-x-6 sm:grid-cols-2 lg:grid-cols-4 xl:gap-x-8\">\n          {collections.map((collection, index) => (\n            <motion.div\n              key={collection.name}\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              className=\"group relative\"\n            >\n              <Link href={collection.href}>\n                <div className=\"relative aspect-[4/5] w-full overflow-hidden rounded-lg bg-gray-200 group-hover:opacity-75 transition-opacity duration-300\">\n                  {/* Placeholder for collection image */}\n                  <div className=\"absolute inset-0 bg-gradient-to-br from-gray-100 to-gray-300 flex items-center justify-center\">\n                    <div className=\"text-center\">\n                      <div className=\"w-16 h-16 mx-auto mb-4 bg-gold rounded-full flex items-center justify-center\">\n                        {collection.name === 'Rings' && (\n                          <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4\" />\n                          </svg>\n                        )}\n                        {collection.name === 'Necklaces' && (\n                          <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z\" />\n                          </svg>\n                        )}\n                        {collection.name === 'Earrings' && (\n                          <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\" />\n                          </svg>\n                        )}\n                        {collection.name === 'Bracelets' && (\n                          <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z\" />\n                          </svg>\n                        )}\n                      </div>\n                      <span className=\"text-gray-500 text-sm\">{collection.name}</span>\n                    </div>\n                  </div>\n                  \n                  {/* Overlay */}\n                  <div className=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center\">\n                    <div className=\"opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                      <span className=\"inline-flex items-center px-4 py-2 border border-white text-sm font-medium rounded-md text-white hover:bg-white hover:text-gray-900 transition-colors duration-200\">\n                        Shop Now\n                      </span>\n                    </div>\n                  </div>\n                </div>\n                \n                <div className=\"mt-4 text-center\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 group-hover:text-gold transition-colors duration-200\">\n                    {collection.name}\n                  </h3>\n                  <p className=\"mt-1 text-sm text-gray-600\">\n                    {collection.description}\n                  </p>\n                </div>\n              </Link>\n            </motion.div>\n          ))}\n        </div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          className=\"mt-12 text-center\"\n        >\n          <Link\n            href=\"/collections\"\n            className=\"inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200\"\n          >\n            View All Collections\n          </Link>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,cAAc;IAClB;QACE,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,UAAU;IACZ;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,UAAU;IACZ;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,UAAU;IACZ;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,UAAU;IACZ;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;sCACX;;;;;;sCAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCACX;;;;;;;;;;;;8BAKH,8OAAC;oBAAI,WAAU;8BACZ,YAAY,GAAG,CAAC,CAAC,YAAY,sBAC5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,WAAU;sCAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAM,WAAW,IAAI;;kDACzB,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;gEACZ,WAAW,IAAI,KAAK,yBACnB,8OAAC;oEAAI,WAAU;oEAAqB,MAAK;oEAAO,QAAO;oEAAe,SAAQ;8EAC5E,cAAA,8OAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;gEAGxE,WAAW,IAAI,KAAK,6BACnB,8OAAC;oEAAI,WAAU;oEAAqB,MAAK;oEAAO,QAAO;oEAAe,SAAQ;8EAC5E,cAAA,8OAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;gEAGxE,WAAW,IAAI,KAAK,4BACnB,8OAAC;oEAAI,WAAU;oEAAqB,MAAK;oEAAO,QAAO;oEAAe,SAAQ;8EAC5E,cAAA,8OAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;gEAGxE,WAAW,IAAI,KAAK,6BACnB,8OAAC;oEAAI,WAAU;oEAAqB,MAAK;oEAAO,QAAO;oEAAe,SAAQ;8EAC5E,cAAA,8OAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;;;;;;;sEAI3E,8OAAC;4DAAK,WAAU;sEAAyB,WAAW,IAAI;;;;;;;;;;;;;;;;;0DAK5D,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAqK;;;;;;;;;;;;;;;;;;;;;;kDAO3L,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DACX,WAAW,IAAI;;;;;;0DAElB,8OAAC;gDAAE,WAAU;0DACV,WAAW,WAAW;;;;;;;;;;;;;;;;;;2BApDxB,WAAW,IAAI;;;;;;;;;;8BA4D1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 1097, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { motion } from 'framer-motion';\nimport { shopifyHelpers } from '@/lib/shopify';\nimport ProductCard from '@/components/ProductCard';\nimport Hero from '@/components/Hero';\nimport FeaturedCollections from '@/components/FeaturedCollections';\n\nexport default function Home() {\n  const [featuredProducts, setFeaturedProducts] = useState<any[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchFeaturedProducts = async () => {\n      try {\n        const products = await shopifyHelpers.fetchProducts(8);\n        setFeaturedProducts(products);\n      } catch (error) {\n        console.error('Error fetching featured products:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchFeaturedProducts();\n  }, []);\n\n  return (\n    <div className=\"bg-white\">\n      {/* Hero Section */}\n      <Hero />\n\n      {/* Featured Collections */}\n      <FeaturedCollections />\n\n      {/* Featured Products */}\n      <section className=\"py-16 sm:py-24\">\n        <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <motion.h2\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              className=\"text-3xl font-bold font-playfair text-gray-900 sm:text-4xl\"\n            >\n              Featured Jewelry\n            </motion.h2>\n            <motion.p\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.1 }}\n              className=\"mt-4 text-lg text-gray-600 max-w-2xl mx-auto\"\n            >\n              Discover our handpicked selection of exquisite pieces, crafted with precision and passion.\n            </motion.p>\n          </div>\n\n          {loading ? (\n            <div className=\"mt-12 grid grid-cols-1 gap-y-10 gap-x-6 sm:grid-cols-2 lg:grid-cols-4 xl:gap-x-8\">\n              {[...Array(8)].map((_, i) => (\n                <div key={i} className=\"animate-pulse\">\n                  <div className=\"aspect-square w-full bg-gray-200 rounded-lg\"></div>\n                  <div className=\"mt-4 space-y-2\">\n                    <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n                    <div className=\"h-4 bg-gray-200 rounded w-1/2\"></div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <motion.div\n              initial={{ opacity: 0 }}\n              whileInView={{ opacity: 1 }}\n              transition={{ duration: 0.6, delay: 0.2 }}\n              className=\"mt-12 grid grid-cols-1 gap-y-10 gap-x-6 sm:grid-cols-2 lg:grid-cols-4 xl:gap-x-8\"\n            >\n              {featuredProducts.map((product, index) => (\n                <motion.div\n                  key={product.id}\n                  initial={{ opacity: 0, y: 20 }}\n                  whileInView={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                >\n                  <ProductCard product={product} />\n                </motion.div>\n              ))}\n            </motion.div>\n          )}\n\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.3 }}\n            className=\"mt-12 text-center\"\n          >\n            <Link\n              href=\"/collections\"\n              className=\"inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-gray-900 hover:bg-gray-800 transition-colors duration-200\"\n            >\n              View All Products\n            </Link>\n          </motion.div>\n        </div>\n      </section>\n\n      {/* About Section */}\n      <section className=\"py-16 sm:py-24 bg-gray-50\">\n        <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n          <div className=\"lg:grid lg:grid-cols-2 lg:gap-8 lg:items-center\">\n            <motion.div\n              initial={{ opacity: 0, x: -20 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.6 }}\n            >\n              <h2 className=\"text-3xl font-bold font-playfair text-gray-900 sm:text-4xl\">\n                Crafted with Passion\n              </h2>\n              <p className=\"mt-4 text-lg text-gray-600\">\n                At JW GOLD, every piece tells a story. Our master craftsmen combine traditional techniques\n                with modern design to create jewelry that celebrates life's most precious moments.\n              </p>\n              <div className=\"mt-8 space-y-4\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"flex items-center justify-center h-8 w-8 rounded-md bg-gold text-white\">\n                      ✓\n                    </div>\n                  </div>\n                  <div className=\"ml-4\">\n                    <p className=\"text-lg font-medium text-gray-900\">Premium Materials</p>\n                    <p className=\"text-gray-600\">Only the finest gold, silver, and gemstones</p>\n                  </div>\n                </div>\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"flex items-center justify-center h-8 w-8 rounded-md bg-gold text-white\">\n                      ✓\n                    </div>\n                  </div>\n                  <div className=\"ml-4\">\n                    <p className=\"text-lg font-medium text-gray-900\">Expert Craftsmanship</p>\n                    <p className=\"text-gray-600\">Handcrafted by skilled artisans</p>\n                  </div>\n                </div>\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"flex items-center justify-center h-8 w-8 rounded-md bg-gold text-white\">\n                      ✓\n                    </div>\n                  </div>\n                  <div className=\"ml-4\">\n                    <p className=\"text-lg font-medium text-gray-900\">Lifetime Warranty</p>\n                    <p className=\"text-gray-600\">Quality guaranteed for life</p>\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n            <motion.div\n              initial={{ opacity: 0, x: 20 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.6, delay: 0.2 }}\n              className=\"mt-10 lg:mt-0\"\n            >\n              <div className=\"aspect-square rounded-lg overflow-hidden bg-gray-200\">\n                <div className=\"w-full h-full flex items-center justify-center text-gray-400\">\n                  <span className=\"text-lg\">Jewelry Craftsmanship Image</span>\n                </div>\n              </div>\n            </motion.div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;AAWe,SAAS;IACtB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,wBAAwB;YAC5B,IAAI;gBACF,MAAM,WAAW,MAAM,8GAAA,CAAA,iBAAc,CAAC,aAAa,CAAC;gBACpD,oBAAoB;YACtB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qCAAqC;YACrD,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,mHAAA,CAAA,UAAI;;;;;0BAGL,8OAAC,kIAAA,CAAA,UAAmB;;;;;0BAGpB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oCACR,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,WAAU;8CACX;;;;;;8CAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;8CACX;;;;;;;;;;;;wBAKF,wBACC,8OAAC;4BAAI,WAAU;sCACZ;mCAAI,MAAM;6BAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;oCAAY,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;mCAJT;;;;;;;;;iDAUd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;4BAAE;4BACtB,aAAa;gCAAE,SAAS;4BAAE;4BAC1B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCAET,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;8CAEhD,cAAA,8OAAC,0HAAA,CAAA,UAAW;wCAAC,SAAS;;;;;;mCALjB,QAAQ,EAAE;;;;;;;;;;sCAWvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;;kDAE5B,8OAAC;wCAAG,WAAU;kDAA6D;;;;;;kDAG3E,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAI1C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;sEAAyE;;;;;;;;;;;kEAI1F,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAoC;;;;;;0EACjD,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;0DAGjC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;sEAAyE;;;;;;;;;;;kEAI1F,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAoC;;;;;;0EACjD,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;0DAGjC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;sEAAyE;;;;;;;;;;;kEAI1F,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAoC;;;;;;0EACjD,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAKrC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5C", "debugId": null}}]}