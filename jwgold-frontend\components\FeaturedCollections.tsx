'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';

const collections = [
  {
    name: 'Rings',
    description: 'Elegant rings for every occasion',
    href: '/collections/rings',
    imageSrc: 'https://images.unsplash.com/photo-1605100804763-247f67b3557e?w=400&h=500&fit=crop&crop=center',
    imageAlt: 'Beautiful gold and diamond rings',
  },
  {
    name: 'Necklaces',
    description: 'Stunning necklaces to complement your style',
    href: '/collections/necklaces',
    imageSrc: 'https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=400&h=500&fit=crop&crop=center',
    imageAlt: 'Elegant gold and silver necklaces',
  },
  {
    name: 'Earrings',
    description: 'Exquisite earrings for the perfect finish',
    href: '/collections/earrings',
    imageSrc: 'https://images.unsplash.com/photo-1535632066927-ab7c9ab60908?w=400&h=500&fit=crop&crop=center',
    imageAlt: 'Beautiful diamond and gold earrings',
  },
  {
    name: 'Bracelets',
    description: 'Sophisticated bracelets for any wrist',
    href: '/collections/bracelets',
    imageSrc: 'https://images.unsplash.com/photo-1611591437281-460bfbe1220a?w=400&h=500&fit=crop&crop=center',
    imageAlt: 'Luxury gold and silver bracelets',
  },
];

export default function FeaturedCollections() {
  return (
    <section className="py-16 sm:py-24 bg-white">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl font-bold font-playfair text-gray-900 sm:text-4xl"
          >
            Our Collections
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="mt-4 text-lg text-gray-600 max-w-2xl mx-auto"
          >
            Explore our carefully curated collections, each designed to capture the essence of elegance and sophistication.
          </motion.p>
        </div>

        <div className="mt-12 grid grid-cols-1 gap-y-10 gap-x-6 sm:grid-cols-2 lg:grid-cols-4 xl:gap-x-8">
          {collections.map((collection, index) => (
            <motion.div
              key={collection.name}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="group relative"
            >
              <Link href={collection.href}>
                <div className="relative aspect-[4/5] w-full overflow-hidden rounded-lg bg-gray-200 group-hover:opacity-75 transition-opacity duration-300">
                  <Image
                    src={collection.imageSrc}
                    alt={collection.imageAlt}
                    fill
                    className="object-cover object-center group-hover:scale-105 transition-transform duration-300"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                  />

                  {/* Overlay */}
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <span className="inline-flex items-center px-4 py-2 border border-white text-sm font-medium rounded-md text-white hover:bg-white hover:text-gray-900 transition-colors duration-200">
                        Shop Now
                      </span>
                    </div>
                  </div>
                </div>

                <div className="mt-4 text-center">
                  <h3 className="text-lg font-semibold text-gray-900 group-hover:text-gold transition-colors duration-200">
                    {collection.name}
                  </h3>
                  <p className="mt-1 text-sm text-gray-600">
                    {collection.description}
                  </p>
                </div>
              </Link>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mt-12 text-center"
        >
          <Link
            href="/collections"
            className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200"
          >
            View All Collections
          </Link>
        </motion.div>
      </div>
    </section>
  );
}
