'use client';

import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { shopifyHelpers } from '@/lib/shopify';
import Cookies from 'js-cookie';

interface CartItem {
  id: string;
  variantId: string;
  quantity: number;
  title: string;
  price: {
    amount: string;
    currencyCode: string;
  };
  image?: {
    src: string;
    altText?: string;
  };
  handle: string;
}

interface CartState {
  items: CartItem[];
  checkoutId: string | null;
  checkoutUrl: string | null;
  isLoading: boolean;
  totalPrice: {
    amount: string;
    currencyCode: string;
  };
  totalQuantity: number;
}

type CartAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_CHECKOUT'; payload: { checkoutId: string; checkoutUrl: string } }
  | { type: 'ADD_ITEM'; payload: CartItem }
  | { type: 'UPDATE_ITEM'; payload: { id: string; quantity: number } }
  | { type: 'REMOVE_ITEM'; payload: string }
  | { type: 'CLEAR_CART' }
  | { type: 'SET_CART'; payload: CartItem[] }
  | { type: 'UPDATE_TOTALS'; payload: { totalPrice: { amount: string; currencyCode: string }; totalQuantity: number } };

const initialState: CartState = {
  items: [],
  checkoutId: null,
  checkoutUrl: null,
  isLoading: false,
  totalPrice: { amount: '0', currencyCode: 'USD' },
  totalQuantity: 0,
};

function cartReducer(state: CartState, action: CartAction): CartState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    
    case 'SET_CHECKOUT':
      return {
        ...state,
        checkoutId: action.payload.checkoutId,
        checkoutUrl: action.payload.checkoutUrl,
      };
    
    case 'ADD_ITEM': {
      const existingItem = state.items.find(item => item.variantId === action.payload.variantId);
      if (existingItem) {
        return {
          ...state,
          items: state.items.map(item =>
            item.variantId === action.payload.variantId
              ? { ...item, quantity: item.quantity + action.payload.quantity }
              : item
          ),
        };
      }
      return { ...state, items: [...state.items, action.payload] };
    }
    
    case 'UPDATE_ITEM':
      return {
        ...state,
        items: state.items.map(item =>
          item.id === action.payload.id
            ? { ...item, quantity: action.payload.quantity }
            : item
        ).filter(item => item.quantity > 0),
      };
    
    case 'REMOVE_ITEM':
      return {
        ...state,
        items: state.items.filter(item => item.id !== action.payload),
      };
    
    case 'CLEAR_CART':
      return { ...state, items: [] };
    
    case 'SET_CART':
      return { ...state, items: action.payload };
    
    case 'UPDATE_TOTALS':
      return {
        ...state,
        totalPrice: action.payload.totalPrice,
        totalQuantity: action.payload.totalQuantity,
      };
    
    default:
      return state;
  }
}

interface CartContextType extends CartState {
  addToCart: (item: Omit<CartItem, 'id'>) => Promise<void>;
  updateCartItem: (id: string, quantity: number) => Promise<void>;
  removeFromCart: (id: string) => Promise<void>;
  clearCart: () => void;
  initializeCheckout: () => Promise<void>;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export function CartProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(cartReducer, initialState);

  // Initialize checkout on mount
  useEffect(() => {
    initializeCheckout();
  }, []);

  // Update totals when items change
  useEffect(() => {
    const totalQuantity = state.items.reduce((sum, item) => sum + item.quantity, 0);
    const totalAmount = state.items.reduce((sum, item) => {
      return sum + (parseFloat(item.price.amount) * item.quantity);
    }, 0);
    
    dispatch({
      type: 'UPDATE_TOTALS',
      payload: {
        totalPrice: {
          amount: totalAmount.toString(),
          currencyCode: state.items[0]?.price.currencyCode || 'USD',
        },
        totalQuantity,
      },
    });
  }, [state.items]);

  // Save cart to cookies when items change
  useEffect(() => {
    if (state.items.length > 0) {
      Cookies.set('cart', JSON.stringify(state.items), { expires: 7 });
    } else {
      Cookies.remove('cart');
    }
  }, [state.items]);

  // Load cart from cookies on mount
  useEffect(() => {
    const savedCart = Cookies.get('cart');
    if (savedCart) {
      try {
        const cartItems = JSON.parse(savedCart);
        dispatch({ type: 'SET_CART', payload: cartItems });
      } catch (error) {
        console.error('Error loading cart from cookies:', error);
      }
    }
  }, []);

  const initializeCheckout = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      const checkout = await shopifyHelpers.createCheckout();
      if (checkout) {
        dispatch({
          type: 'SET_CHECKOUT',
          payload: {
            checkoutId: checkout.id,
            checkoutUrl: checkout.webUrl,
          },
        });
        Cookies.set('checkoutId', checkout.id, { expires: 7 });
      }
    } catch (error) {
      console.error('Error initializing checkout:', error);
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const addToCart = async (item: Omit<CartItem, 'id'>) => {
    const cartItem: CartItem = {
      ...item,
      id: `${item.variantId}-${Date.now()}`,
    };
    
    dispatch({ type: 'ADD_ITEM', payload: cartItem });
    
    // Update Shopify checkout
    if (state.checkoutId) {
      try {
        const lineItemsToAdd = [{
          variantId: item.variantId,
          quantity: item.quantity,
        }];
        await shopifyHelpers.addToCheckout(state.checkoutId, lineItemsToAdd);
      } catch (error) {
        console.error('Error adding to Shopify checkout:', error);
      }
    }
  };

  const updateCartItem = async (id: string, quantity: number) => {
    dispatch({ type: 'UPDATE_ITEM', payload: { id, quantity } });
  };

  const removeFromCart = async (id: string) => {
    dispatch({ type: 'REMOVE_ITEM', payload: id });
  };

  const clearCart = () => {
    dispatch({ type: 'CLEAR_CART' });
    Cookies.remove('cart');
  };

  return (
    <CartContext.Provider
      value={{
        ...state,
        addToCart,
        updateCartItem,
        removeFromCart,
        clearCart,
        initializeCheckout,
      }}
    >
      {children}
    </CartContext.Provider>
  );
}

export function useCart() {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
}
