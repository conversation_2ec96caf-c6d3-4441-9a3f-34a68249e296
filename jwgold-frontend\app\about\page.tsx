'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';

export default function AboutPage() {
  return (
    <div className="bg-white">
      {/* Hero Section */}
      <div className="relative bg-gray-900 py-24 sm:py-32">
        <div className="absolute inset-0">
          <Image
            src="https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=1200&h=600&fit=crop&crop=center"
            alt="Jewelry craftsmanship"
            fill
            className="object-cover opacity-40"
          />
        </div>
        <div className="relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl font-bold font-playfair text-white sm:text-5xl lg:text-6xl">
              Our Story
            </h1>
            <p className="mt-6 text-xl text-gray-300 max-w-3xl mx-auto">
              Crafting exceptional jewelry with passion, precision, and timeless elegance since our founding.
            </p>
          </motion.div>
        </div>
      </div>

      {/* Main Content */}
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16 sm:py-24">
        <div className="lg:grid lg:grid-cols-2 lg:gap-16 lg:items-center">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl font-bold font-playfair text-gray-900 sm:text-4xl">
              The JW GOLD Legacy
            </h2>
            <p className="mt-4 text-lg text-gray-600">
              Founded with a vision to create jewelry that tells stories, JW GOLD has been at the forefront 
              of luxury jewelry design for years. Our commitment to excellence and attention to detail has 
              made us a trusted name in the industry.
            </p>
            <p className="mt-4 text-lg text-gray-600">
              Every piece in our collection is meticulously crafted by skilled artisans who share our 
              passion for perfection. We believe that jewelry is more than just an accessory – it's a 
              reflection of your personality, a celebration of life's precious moments, and a legacy 
              to be treasured for generations.
            </p>
          </motion.div>
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="mt-10 lg:mt-0"
          >
            <div className="aspect-square rounded-lg overflow-hidden">
              <Image
                src="https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=600&h=600&fit=crop&crop=center"
                alt="Jewelry craftsmanship"
                width={600}
                height={600}
                className="object-cover"
              />
            </div>
          </motion.div>
        </div>

        {/* Values Section */}
        <div className="mt-20">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <h2 className="text-3xl font-bold font-playfair text-gray-900 sm:text-4xl">
              Our Values
            </h2>
            <p className="mt-4 text-lg text-gray-600 max-w-2xl mx-auto">
              These core principles guide everything we do at JW GOLD
            </p>
          </motion.div>

          <div className="mt-12 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
            {[
              {
                title: 'Quality Craftsmanship',
                description: 'Every piece is handcrafted with meticulous attention to detail using only the finest materials.',
                icon: '✨',
              },
              {
                title: 'Timeless Design',
                description: 'Our designs blend classic elegance with contemporary style to create pieces that transcend trends.',
                icon: '💎',
              },
              {
                title: 'Customer Excellence',
                description: 'We are committed to providing exceptional service and creating lasting relationships with our clients.',
                icon: '🤝',
              },
              {
                title: 'Ethical Sourcing',
                description: 'We responsibly source our materials and support ethical practices throughout our supply chain.',
                icon: '🌱',
              },
              {
                title: 'Innovation',
                description: 'We continuously explore new techniques and technologies to enhance our craft and customer experience.',
                icon: '🔬',
              },
              {
                title: 'Heritage',
                description: 'We honor traditional jewelry-making techniques while embracing modern innovations.',
                icon: '🏛️',
              },
            ].map((value, index) => (
              <motion.div
                key={value.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center"
              >
                <div className="text-4xl mb-4">{value.icon}</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">{value.title}</h3>
                <p className="text-gray-600">{value.description}</p>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Team Section */}
        <div className="mt-20">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <h2 className="text-3xl font-bold font-playfair text-gray-900 sm:text-4xl">
              Meet Our Artisans
            </h2>
            <p className="mt-4 text-lg text-gray-600 max-w-2xl mx-auto">
              The talented individuals behind every JW GOLD masterpiece
            </p>
          </motion.div>

          <div className="mt-12 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
            {[
              {
                name: 'Master Craftsman John',
                role: 'Lead Designer',
                image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300&h=300&fit=crop&crop=face',
                description: 'With over 20 years of experience, John leads our design team with passion and precision.',
              },
              {
                name: 'Sarah Williams',
                role: 'Gemstone Specialist',
                image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=300&h=300&fit=crop&crop=face',
                description: 'Sarah ensures every gemstone meets our exacting standards for quality and beauty.',
              },
              {
                name: 'Michael Chen',
                role: 'Master Goldsmith',
                image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop&crop=face',
                description: 'Michael brings traditional goldsmithing techniques to life in every piece he creates.',
              },
            ].map((member, index) => (
              <motion.div
                key={member.name}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center"
              >
                <div className="aspect-square w-48 mx-auto rounded-full overflow-hidden mb-4">
                  <Image
                    src={member.image}
                    alt={member.name}
                    width={192}
                    height={192}
                    className="object-cover"
                  />
                </div>
                <h3 className="text-xl font-semibold text-gray-900">{member.name}</h3>
                <p className="text-gold font-medium">{member.role}</p>
                <p className="mt-2 text-gray-600">{member.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
