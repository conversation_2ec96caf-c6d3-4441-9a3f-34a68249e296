import Client from 'shopify-buy';

// Initialize the Shopify client
const client = Client.buildClient({
  domain: process.env.NEXT_PUBLIC_SHOPIFY_STORE_DOMAIN!,
  storefrontAccessToken: process.env.NEXT_PUBLIC_SHOPIFY_STOREFRONT_ACCESS_TOKEN!,
});

export default client;

// Types for better TypeScript support
export interface ShopifyProduct {
  id: string;
  title: string;
  handle: string;
  description: string;
  images: Array<{
    id: string;
    src: string;
    altText?: string;
  }>;
  variants: Array<{
    id: string;
    title: string;
    price: {
      amount: string;
      currencyCode: string;
    };
    compareAtPrice?: {
      amount: string;
      currencyCode: string;
    };
    available: boolean;
    selectedOptions: Array<{
      name: string;
      value: string;
    }>;
  }>;
  options: Array<{
    id: string;
    name: string;
    values: string[];
  }>;
  priceRange: {
    minVariantPrice: {
      amount: string;
      currencyCode: string;
    };
    maxVariantPrice: {
      amount: string;
      currencyCode: string;
    };
  };
  tags: string[];
  productType: string;
  vendor: string;
}

export interface ShopifyCollection {
  id: string;
  title: string;
  handle: string;
  description: string;
  image?: {
    id: string;
    src: string;
    altText?: string;
  };
}

export interface CartItem {
  id: string;
  quantity: number;
  variant: {
    id: string;
    title: string;
    price: {
      amount: string;
      currencyCode: string;
    };
    product: {
      id: string;
      title: string;
      handle: string;
      images: Array<{
        id: string;
        src: string;
        altText?: string;
      }>;
    };
  };
}

// Helper functions for Shopify operations
export const shopifyHelpers = {
  // Fetch all products
  async fetchProducts(limit = 20) {
    try {
      const products = await client.product.fetchAll(limit);
      return products;
    } catch (error) {
      console.error('Error fetching products:', error);
      return [];
    }
  },

  // Fetch a single product by handle
  async fetchProductByHandle(handle: string) {
    try {
      const products = await client.product.fetchAll();
      return products.find((product: any) => product.handle === handle);
    } catch (error) {
      console.error('Error fetching product:', error);
      return null;
    }
  },

  // Fetch collections
  async fetchCollections(limit = 10) {
    try {
      const collections = await client.collection.fetchAll(limit);
      return collections;
    } catch (error) {
      console.error('Error fetching collections:', error);
      return [];
    }
  },

  // Create checkout
  async createCheckout() {
    try {
      const checkout = await client.checkout.create();
      return checkout;
    } catch (error) {
      console.error('Error creating checkout:', error);
      return null;
    }
  },

  // Add items to checkout
  async addToCheckout(checkoutId: string, lineItemsToAdd: any[]) {
    try {
      const checkout = await client.checkout.addLineItems(checkoutId, lineItemsToAdd);
      return checkout;
    } catch (error) {
      console.error('Error adding to checkout:', error);
      return null;
    }
  },

  // Update checkout
  async updateCheckout(checkoutId: string, lineItemsToUpdate: any[]) {
    try {
      const checkout = await client.checkout.updateLineItems(checkoutId, lineItemsToUpdate);
      return checkout;
    } catch (error) {
      console.error('Error updating checkout:', error);
      return null;
    }
  },

  // Remove from checkout
  async removeFromCheckout(checkoutId: string, lineItemIds: string[]) {
    try {
      const checkout = await client.checkout.removeLineItems(checkoutId, lineItemIds);
      return checkout;
    } catch (error) {
      console.error('Error removing from checkout:', error);
      return null;
    }
  },

  // Format price
  formatPrice(price: { amount: string; currencyCode: string }) {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: price.currencyCode,
    }).format(parseFloat(price.amount));
  },
};
