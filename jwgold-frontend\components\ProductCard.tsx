'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { useCart } from '@/context/CartContext';
import { shopifyHelpers } from '@/lib/shopify';
import { HeartIcon, ShoppingBagIcon } from '@heroicons/react/24/outline';
import { HeartIcon as HeartIconSolid } from '@heroicons/react/24/solid';

interface ProductCardProps {
  product: any; // Shopify product type
}

export default function ProductCard({ product }: ProductCardProps) {
  const [isWishlisted, setIsWishlisted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { addToCart } = useCart();

  const handleAddToCart = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (!product.variants || product.variants.length === 0) return;
    
    setIsLoading(true);
    
    const variant = product.variants[0]; // Use first variant for quick add
    
    try {
      await addToCart({
        variantId: variant.id,
        quantity: 1,
        title: product.title,
        price: variant.price,
        image: product.images?.[0] ? {
          src: product.images[0].src,
          altText: product.images[0].altText || product.title,
        } : undefined,
        handle: product.handle,
      });
    } catch (error) {
      console.error('Error adding to cart:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleWishlist = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsWishlisted(!isWishlisted);
  };

  const primaryImage = product.images?.[0];
  const secondaryImage = product.images?.[1];
  const minPrice = product.priceRange?.minVariantPrice;
  const maxPrice = product.priceRange?.maxVariantPrice;
  const hasVariantPricing = minPrice && maxPrice && minPrice.amount !== maxPrice.amount;

  return (
    <motion.div
      whileHover={{ y: -5 }}
      transition={{ duration: 0.3 }}
      className="group relative bg-white rounded-lg shadow-sm hover:shadow-lg transition-shadow duration-300"
    >
      <Link href={`/products/${product.handle}`}>
        <div className="relative aspect-square w-full overflow-hidden rounded-t-lg bg-gray-200">
          {primaryImage ? (
            <>
              <Image
                src={primaryImage.src}
                alt={primaryImage.altText || product.title}
                fill
                className="object-cover object-center group-hover:scale-105 transition-transform duration-300"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
              />
              {secondaryImage && (
                <Image
                  src={secondaryImage.src}
                  alt={secondaryImage.altText || product.title}
                  fill
                  className="object-cover object-center opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                />
              )}
            </>
          ) : (
            <div className="w-full h-full bg-gray-200 flex items-center justify-center">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-2 bg-gray-300 rounded-full flex items-center justify-center">
                  <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                  </svg>
                </div>
                <span className="text-gray-400 text-sm">No Image</span>
              </div>
            </div>
          )}

          {/* Overlay buttons */}
          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300">
            <div className="absolute top-4 right-4 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <button
                onClick={handleWishlist}
                className="p-2 bg-white rounded-full shadow-md hover:bg-gray-50 transition-colors duration-200"
              >
                {isWishlisted ? (
                  <HeartIconSolid className="h-5 w-5 text-red-500" />
                ) : (
                  <HeartIcon className="h-5 w-5 text-gray-600" />
                )}
              </button>
            </div>

            <div className="absolute bottom-4 left-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <button
                onClick={handleAddToCart}
                disabled={isLoading || !product.variants?.[0]?.available}
                className="w-full bg-white text-gray-900 py-2 px-4 rounded-md font-medium hover:bg-gray-50 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
              >
                {isLoading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900"></div>
                ) : (
                  <>
                    <ShoppingBagIcon className="h-4 w-4" />
                    {product.variants?.[0]?.available ? 'Quick Add' : 'Sold Out'}
                  </>
                )}
              </button>
            </div>
          </div>

          {/* Sale badge */}
          {product.variants?.[0]?.compareAtPrice && (
            <div className="absolute top-4 left-4">
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                Sale
              </span>
            </div>
          )}
        </div>

        <div className="p-4">
          <div className="mb-2">
            <h3 className="text-sm font-medium text-gray-900 line-clamp-2 group-hover:text-gold transition-colors duration-200">
              {product.title}
            </h3>
            {product.vendor && (
              <p className="text-xs text-gray-500 mt-1">{product.vendor}</p>
            )}
          </div>

          <div className="flex items-center justify-between">
            <div className="flex flex-col">
              {hasVariantPricing ? (
                <span className="text-sm font-medium text-gray-900">
                  {shopifyHelpers.formatPrice(minPrice)} - {shopifyHelpers.formatPrice(maxPrice)}
                </span>
              ) : minPrice ? (
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium text-gray-900">
                    {shopifyHelpers.formatPrice(minPrice)}
                  </span>
                  {product.variants?.[0]?.compareAtPrice && (
                    <span className="text-xs text-gray-500 line-through">
                      {shopifyHelpers.formatPrice(product.variants[0].compareAtPrice)}
                    </span>
                  )}
                </div>
              ) : (
                <span className="text-sm text-gray-500">Price unavailable</span>
              )}
            </div>

            {/* Rating stars placeholder */}
            <div className="flex items-center">
              {[...Array(5)].map((_, i) => (
                <svg
                  key={i}
                  className="h-3 w-3 text-gold"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              ))}
            </div>
          </div>
        </div>
      </Link>
    </motion.div>
  );
}
