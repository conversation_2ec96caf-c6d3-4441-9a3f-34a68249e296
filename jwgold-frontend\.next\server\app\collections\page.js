(()=>{var a={};a.id=116,a.ids=[116],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},711:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(5239),e=c(8088),f=c(7220),g=c(1289),h=c(6191),i=c(4823),j=c(1998),k=c(2603),l=c(4649),m=c(2781),n=c(2602),o=c(1268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(6713),u=c(3365),v=c(1454),w=c(7778),x=c(6143),y=c(9105),z=c(8171),A=c(6439),B=c(6133),C=c.n(B),D=c(893),E=c(2836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["collections",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,8455)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\jwgold\\jwgold-frontend\\app\\collections\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,6055))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,2007)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\jwgold\\jwgold-frontend\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,6133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,9868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,9615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,6055))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\OneDrive\\Desktop\\jwgold\\jwgold-frontend\\app\\collections\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/collections/page",pathname:"/collections",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/collections/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},3033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:a=>{"use strict";a.exports=require("path")},4704:(a,b,c)=>{Promise.resolve().then(c.bind(c,8455))},6439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},8080:(a,b,c)=>{"use strict";c.d(b,{A:()=>o});var d=c(687),e=c(3210),f=c(5814),g=c.n(f),h=c(474),i=c(1012),j=c(1423),k=c(7158),l=c(4917),m=c(6719),n=c(6264);function o({product:a}){let[b,c]=(0,e.useState)(!1),[f,o]=(0,e.useState)(!1),{addToCart:p}=(0,j._)(),q=async b=>{if(b.preventDefault(),b.stopPropagation(),!a.variants||0===a.variants.length)return;o(!0);let c=a.variants[0];try{await p({variantId:c.id,quantity:1,title:a.title,price:c.price,image:a.images?.[0]?{src:a.images[0].src,altText:a.images[0].altText||a.title}:void 0,handle:a.handle})}catch(a){console.error("Error adding to cart:",a)}finally{o(!1)}},r=a.images?.[0],s=a.images?.[1],t=a.priceRange?.minVariantPrice,u=a.priceRange?.maxVariantPrice,v=t&&u&&t.amount!==u.amount;return(0,d.jsx)(i.P.div,{whileHover:{y:-5},transition:{duration:.3},className:"group relative bg-white rounded-lg shadow-sm hover:shadow-lg transition-shadow duration-300",children:(0,d.jsxs)(g(),{href:`/products/${a.handle}`,children:[(0,d.jsxs)("div",{className:"relative aspect-square w-full overflow-hidden rounded-t-lg bg-gray-200",children:[r?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(h.default,{src:r.src,alt:r.altText||a.title,fill:!0,className:"object-cover object-center group-hover:scale-105 transition-transform duration-300",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"}),s&&(0,d.jsx)(h.default,{src:s.src,alt:s.altText||a.title,fill:!0,className:"object-cover object-center opacity-0 group-hover:opacity-100 transition-opacity duration-300",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"})]}):(0,d.jsx)("div",{className:"w-full h-full bg-gray-200 flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 mx-auto mb-2 bg-gray-300 rounded-full flex items-center justify-center",children:(0,d.jsx)("svg",{className:"w-8 h-8 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"})})}),(0,d.jsx)("span",{className:"text-gray-400 text-sm",children:"No Image"})]})}),(0,d.jsxs)("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300",children:[(0,d.jsx)("div",{className:"absolute top-4 right-4 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:(0,d.jsx)("button",{onClick:a=>{a.preventDefault(),a.stopPropagation(),c(!b)},className:"p-2 bg-white rounded-full shadow-md hover:bg-gray-50 transition-colors duration-200",children:b?(0,d.jsx)(n.A,{className:"h-5 w-5 text-red-500"}):(0,d.jsx)(l.A,{className:"h-5 w-5 text-gray-600"})})}),(0,d.jsx)("div",{className:"absolute bottom-4 left-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:(0,d.jsx)("button",{onClick:q,disabled:f||!a.variants?.[0]?.available,className:"w-full bg-white text-gray-900 py-2 px-4 rounded-md font-medium hover:bg-gray-50 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2",children:f?(0,d.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900"}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(m.A,{className:"h-4 w-4"}),a.variants?.[0]?.available?"Quick Add":"Sold Out"]})})})]}),a.variants?.[0]?.compareAtPrice&&(0,d.jsx)("div",{className:"absolute top-4 left-4",children:(0,d.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800",children:"Sale"})})]}),(0,d.jsxs)("div",{className:"p-4",children:[(0,d.jsxs)("div",{className:"mb-2",children:[(0,d.jsx)("h3",{className:"text-sm font-medium text-gray-900 line-clamp-2 group-hover:text-gold transition-colors duration-200",children:a.title}),a.vendor&&(0,d.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:a.vendor})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("div",{className:"flex flex-col",children:v?(0,d.jsxs)("span",{className:"text-sm font-medium text-gray-900",children:[k.h.formatPrice(t)," - ",k.h.formatPrice(u)]}):t?(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-900",children:k.h.formatPrice(t)}),a.variants?.[0]?.compareAtPrice&&(0,d.jsx)("span",{className:"text-xs text-gray-500 line-through",children:k.h.formatPrice(a.variants[0].compareAtPrice)})]}):(0,d.jsx)("span",{className:"text-sm text-gray-500",children:"Price unavailable"})}),(0,d.jsx)("div",{className:"flex items-center",children:[void 0,void 0,void 0,void 0,void 0].map((a,b)=>(0,d.jsx)("svg",{className:"h-3 w-3 text-gold",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})},b))})]})]})]})})}},8201:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>m});var d=c(687),e=c(3210),f=c(1012);c(7158);var g=c(8080);let h=e.forwardRef(function({title:a,titleId:b,...c},d){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:d,"aria-labelledby":b},c),a?e.createElement("title",{id:b},a):null,e.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z"}))}),i=e.forwardRef(function({title:a,titleId:b,...c},d){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:d,"aria-labelledby":b},c),a?e.createElement("title",{id:b},a):null,e.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6A2.25 2.25 0 0 1 6 3.75h2.25A2.25 2.25 0 0 1 10.5 6v2.25a2.25 2.25 0 0 1-2.25 2.25H6a2.25 2.25 0 0 1-2.25-2.25V6ZM3.75 15.75A2.25 2.25 0 0 1 6 13.5h2.25a2.25 2.25 0 0 1 2.25 2.25V18a2.25 2.25 0 0 1-2.25 2.25H6A2.25 2.25 0 0 1 3.75 18v-2.25ZM13.5 6a2.25 2.25 0 0 1 2.25-2.25H18A2.25 2.25 0 0 1 20.25 6v2.25A2.25 2.25 0 0 1 18 10.5h-2.25a2.25 2.25 0 0 1-2.25-2.25V6ZM13.5 15.75a2.25 2.25 0 0 1 2.25-2.25H18a2.25 2.25 0 0 1 2.25 2.25V18A2.25 2.25 0 0 1 18 20.25h-2.25A2.25 2.25 0 0 1 13.5 18v-2.25Z"}))});var j=c(6510);let k=[{name:"Most Popular",value:"popular"},{name:"Best Rating",value:"rating"},{name:"Newest",value:"newest"},{name:"Price: Low to High",value:"price-asc"},{name:"Price: High to Low",value:"price-desc"}],l=[{id:"category",name:"Category",options:[{value:"rings",label:"Rings"},{value:"necklaces",label:"Necklaces"},{value:"earrings",label:"Earrings"},{value:"bracelets",label:"Bracelets"}]},{id:"material",name:"Material",options:[{value:"gold",label:"Gold"},{value:"silver",label:"Silver"},{value:"platinum",label:"Platinum"},{value:"diamond",label:"Diamond"}]},{id:"price",name:"Price Range",options:[{value:"0-100",label:"Under $100"},{value:"100-500",label:"$100 - $500"},{value:"500-1000",label:"$500 - $1,000"},{value:"1000+",label:"Over $1,000"}]}];function m(){let[a,b]=(0,e.useState)([]),[c,m]=(0,e.useState)(!0),[n,o]=(0,e.useState)("grid"),[p,q]=(0,e.useState)("popular"),[r,s]=(0,e.useState)({}),[t,u]=(0,e.useState)(!1),v=()=>{s({})};return(0,d.jsx)("div",{className:"bg-white",children:(0,d.jsxs)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8",children:[(0,d.jsxs)("div",{className:"border-b border-gray-200 pb-6",children:[(0,d.jsx)(f.P.h1,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"text-4xl font-bold font-playfair text-gray-900",children:"All Collections"}),(0,d.jsx)(f.P.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1},className:"mt-4 text-lg text-gray-600",children:"Discover our complete range of exquisite jewelry pieces"})]}),(0,d.jsxs)("div",{className:"pt-6 lg:grid lg:grid-cols-4 lg:gap-x-8",children:[(0,d.jsxs)("aside",{className:"hidden lg:block",children:[(0,d.jsx)("h2",{className:"sr-only",children:"Filters"}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Filters"}),(0,d.jsx)("button",{onClick:v,className:"text-sm text-indigo-600 hover:text-indigo-500",children:"Clear all"})]}),l.map(a=>(0,d.jsxs)("div",{className:"border-b border-gray-200 pb-6",children:[(0,d.jsx)("h3",{className:"text-sm font-medium text-gray-900 mb-4",children:a.name}),(0,d.jsx)("div",{className:"space-y-3",children:a.options.map(b=>(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("input",{id:`${a.id}-${b.value}`,type:"checkbox",checked:r[a.id]?.includes(b.value)||!1,onChange:()=>{var c,d;return c=a.id,d=b.value,void s(a=>{let b=a[c]||[];return b.includes(d)?{...a,[c]:b.filter(a=>a!==d)}:{...a,[c]:[...b,d]}})},className:"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"}),(0,d.jsx)("label",{htmlFor:`${a.id}-${b.value}`,className:"ml-3 text-sm text-gray-600",children:b.label})]},b.value))})]},a.id))]})]}),(0,d.jsxs)("div",{className:"lg:col-span-3",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between border-b border-gray-200 pb-6",children:[(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("button",{onClick:()=>u(!0),className:"lg:hidden p-2 text-gray-400 hover:text-gray-500",children:(0,d.jsx)(h,{className:"h-5 w-5"})}),(0,d.jsx)("p",{className:"text-sm text-gray-700",children:c?"Loading...":`${a.length} products`})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("label",{htmlFor:"sort",className:"sr-only",children:"Sort"}),(0,d.jsx)("select",{id:"sort",value:p,onChange:a=>q(a.target.value),className:"block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm",children:k.map(a=>(0,d.jsx)("option",{value:a.value,children:a.name},a.value))})]}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("button",{onClick:()=>o("grid"),className:`p-2 ${"grid"===n?"text-indigo-600":"text-gray-400"} hover:text-gray-500`,children:(0,d.jsx)(i,{className:"h-5 w-5"})}),(0,d.jsx)("button",{onClick:()=>o("list"),className:`p-2 ${"list"===n?"text-indigo-600":"text-gray-400"} hover:text-gray-500`,children:(0,d.jsx)(j.A,{className:"h-5 w-5"})})]})]})]}),c?(0,d.jsx)("div",{className:"mt-8 grid grid-cols-1 gap-y-10 gap-x-6 sm:grid-cols-2 lg:grid-cols-3 xl:gap-x-8",children:[...Array(9)].map((a,b)=>(0,d.jsxs)("div",{className:"animate-pulse",children:[(0,d.jsx)("div",{className:"aspect-square w-full bg-gray-200 rounded-lg"}),(0,d.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"})]})]},b))}):0===a.length?(0,d.jsxs)("div",{className:"mt-8 text-center",children:[(0,d.jsx)("p",{className:"text-gray-500",children:"No products found matching your criteria."}),(0,d.jsx)("button",{onClick:v,className:"mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-600 bg-indigo-100 hover:bg-indigo-200",children:"Clear filters"})]}):(0,d.jsx)(f.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.6},className:`mt-8 grid gap-y-10 gap-x-6 xl:gap-x-8 ${"grid"===n?"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3":"grid-cols-1"}`,children:a.map((a,b)=>(0,d.jsx)(f.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.05*b},children:(0,d.jsx)(g.A,{product:a})},a.id))})]})]})]})})}},8256:(a,b,c)=>{Promise.resolve().then(c.bind(c,8201))},8354:a=>{"use strict";a.exports=require("util")},8455:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\jwgold\\\\jwgold-frontend\\\\app\\\\collections\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\jwgold\\jwgold-frontend\\app\\collections\\page.tsx","default")},9121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,930,52,131],()=>b(b.s=711));module.exports=c})();