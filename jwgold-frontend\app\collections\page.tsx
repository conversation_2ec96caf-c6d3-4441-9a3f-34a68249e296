'use client';

import { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { shopifyHelpers } from '@/lib/shopify';
import ProductCard from '@/components/ProductCard';
import { FunnelIcon, Squares2X2Icon, Bars3Icon } from '@heroicons/react/24/outline';

const sortOptions = [
  { name: 'Most Popular', value: 'popular' },
  { name: 'Best Rating', value: 'rating' },
  { name: 'Newest', value: 'newest' },
  { name: 'Price: Low to High', value: 'price-asc' },
  { name: 'Price: High to Low', value: 'price-desc' },
];

const filters = [
  {
    id: 'category',
    name: 'Category',
    options: [
      { value: 'rings', label: 'Rings' },
      { value: 'necklaces', label: 'Necklaces' },
      { value: 'earrings', label: 'Earrings' },
      { value: 'bracelets', label: 'Bracelets' },
    ],
  },
  {
    id: 'material',
    name: 'Material',
    options: [
      { value: 'gold', label: 'Gold' },
      { value: 'silver', label: 'Silver' },
      { value: 'platinum', label: 'Platinum' },
      { value: 'diamond', label: 'Diamond' },
    ],
  },
  {
    id: 'price',
    name: 'Price Range',
    options: [
      { value: '0-100', label: 'Under $100' },
      { value: '100-500', label: '$100 - $500' },
      { value: '500-1000', label: '$500 - $1,000' },
      { value: '1000+', label: 'Over $1,000' },
    ],
  },
];

export default function CollectionsPage() {
  const [products, setProducts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('popular');
  const [selectedFilters, setSelectedFilters] = useState<Record<string, string[]>>({});
  const [mobileFiltersOpen, setMobileFiltersOpen] = useState(false);

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        const fetchedProducts = await shopifyHelpers.fetchProducts(20);
        setProducts(fetchedProducts);
      } catch (error) {
        console.error('Error fetching products:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  const handleFilterChange = (filterId: string, value: string) => {
    setSelectedFilters(prev => {
      const currentFilters = prev[filterId] || [];
      const isSelected = currentFilters.includes(value);
      
      if (isSelected) {
        return {
          ...prev,
          [filterId]: currentFilters.filter(v => v !== value)
        };
      } else {
        return {
          ...prev,
          [filterId]: [...currentFilters, value]
        };
      }
    });
  };

  const clearFilters = () => {
    setSelectedFilters({});
  };

  const filteredProducts = products; // TODO: Implement actual filtering logic

  return (
    <div className="bg-white">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="border-b border-gray-200 pb-6">
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-4xl font-bold font-playfair text-gray-900"
          >
            All Collections
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="mt-4 text-lg text-gray-600"
          >
            Discover our complete range of exquisite jewelry pieces
          </motion.p>
        </div>

        <div className="pt-6 lg:grid lg:grid-cols-4 lg:gap-x-8">
          {/* Filters */}
          <aside className="hidden lg:block">
            <h2 className="sr-only">Filters</h2>

            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">Filters</h3>
                <button
                  onClick={clearFilters}
                  className="text-sm text-indigo-600 hover:text-indigo-500"
                >
                  Clear all
                </button>
              </div>

              {filters.map((section) => (
                <div key={section.id} className="border-b border-gray-200 pb-6">
                  <h3 className="text-sm font-medium text-gray-900 mb-4">
                    {section.name}
                  </h3>
                  <div className="space-y-3">
                    {section.options.map((option) => (
                      <div key={option.value} className="flex items-center">
                        <input
                          id={`${section.id}-${option.value}`}
                          type="checkbox"
                          checked={selectedFilters[section.id]?.includes(option.value) || false}
                          onChange={() => handleFilterChange(section.id, option.value)}
                          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                        />
                        <label
                          htmlFor={`${section.id}-${option.value}`}
                          className="ml-3 text-sm text-gray-600"
                        >
                          {option.label}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </aside>

          {/* Product grid */}
          <div className="lg:col-span-3">
            {/* Sort and view options */}
            <div className="flex items-center justify-between border-b border-gray-200 pb-6">
              <div className="flex items-center">
                <button
                  onClick={() => setMobileFiltersOpen(true)}
                  className="lg:hidden p-2 text-gray-400 hover:text-gray-500"
                >
                  <FunnelIcon className="h-5 w-5" />
                </button>
                <p className="text-sm text-gray-700">
                  {loading ? 'Loading...' : `${filteredProducts.length} products`}
                </p>
              </div>

              <div className="flex items-center space-x-4">
                <div className="flex items-center">
                  <label htmlFor="sort" className="sr-only">
                    Sort
                  </label>
                  <select
                    id="sort"
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                  >
                    {sortOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="flex items-center">
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`p-2 ${viewMode === 'grid' ? 'text-indigo-600' : 'text-gray-400'} hover:text-gray-500`}
                  >
                    <Squares2X2Icon className="h-5 w-5" />
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`p-2 ${viewMode === 'list' ? 'text-indigo-600' : 'text-gray-400'} hover:text-gray-500`}
                  >
                    <Bars3Icon className="h-5 w-5" />
                  </button>
                </div>
              </div>
            </div>

            {/* Products */}
            {loading ? (
              <div className="mt-8 grid grid-cols-1 gap-y-10 gap-x-6 sm:grid-cols-2 lg:grid-cols-3 xl:gap-x-8">
                {[...Array(9)].map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="aspect-square w-full bg-gray-200 rounded-lg"></div>
                    <div className="mt-4 space-y-2">
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : filteredProducts.length === 0 ? (
              <div className="mt-8 text-center">
                <p className="text-gray-500">No products found matching your criteria.</p>
                <button
                  onClick={clearFilters}
                  className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-600 bg-indigo-100 hover:bg-indigo-200"
                >
                  Clear filters
                </button>
              </div>
            ) : (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.6 }}
                className={`mt-8 grid gap-y-10 gap-x-6 xl:gap-x-8 ${
                  viewMode === 'grid'
                    ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3'
                    : 'grid-cols-1'
                }`}
              >
                {filteredProducts.map((product, index) => (
                  <motion.div
                    key={product.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.05 }}
                  >
                    <ProductCard product={product} />
                  </motion.div>
                ))}
              </motion.div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
