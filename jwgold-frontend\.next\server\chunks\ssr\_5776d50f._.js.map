{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/components/ProductCard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { motion } from 'framer-motion';\nimport { useCart } from '@/context/CartContext';\nimport { shopifyHelpers } from '@/lib/shopify';\nimport { HeartIcon, ShoppingBagIcon } from '@heroicons/react/24/outline';\nimport { HeartIcon as HeartIconSolid } from '@heroicons/react/24/solid';\n\ninterface ProductCardProps {\n  product: any; // Shopify product type\n}\n\nexport default function ProductCard({ product }: ProductCardProps) {\n  const [isWishlisted, setIsWishlisted] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const { addToCart } = useCart();\n\n  const handleAddToCart = async (e: React.MouseEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    \n    if (!product.variants || product.variants.length === 0) return;\n    \n    setIsLoading(true);\n    \n    const variant = product.variants[0]; // Use first variant for quick add\n    \n    try {\n      await addToCart({\n        variantId: variant.id,\n        quantity: 1,\n        title: product.title,\n        price: variant.price,\n        image: product.images?.[0] ? {\n          src: product.images[0].src,\n          altText: product.images[0].altText || product.title,\n        } : undefined,\n        handle: product.handle,\n      });\n    } catch (error) {\n      console.error('Error adding to cart:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleWishlist = (e: React.MouseEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsWishlisted(!isWishlisted);\n  };\n\n  const primaryImage = product.images?.[0];\n  const secondaryImage = product.images?.[1];\n  const minPrice = product.priceRange?.minVariantPrice;\n  const maxPrice = product.priceRange?.maxVariantPrice;\n  const hasVariantPricing = minPrice && maxPrice && minPrice.amount !== maxPrice.amount;\n\n  return (\n    <motion.div\n      whileHover={{ y: -5 }}\n      transition={{ duration: 0.3 }}\n      className=\"group relative bg-white rounded-lg shadow-sm hover:shadow-lg transition-shadow duration-300\"\n    >\n      <Link href={`/products/${product.handle}`}>\n        <div className=\"relative aspect-square w-full overflow-hidden rounded-t-lg bg-gray-200\">\n          {primaryImage ? (\n            <>\n              <Image\n                src={primaryImage.src}\n                alt={primaryImage.altText || product.title}\n                fill\n                className=\"object-cover object-center group-hover:scale-105 transition-transform duration-300\"\n                sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw\"\n              />\n              {secondaryImage && (\n                <Image\n                  src={secondaryImage.src}\n                  alt={secondaryImage.altText || product.title}\n                  fill\n                  className=\"object-cover object-center opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                  sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw\"\n                />\n              )}\n            </>\n          ) : (\n            <div className=\"w-full h-full bg-gray-200 flex items-center justify-center\">\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 mx-auto mb-2 bg-gray-300 rounded-full flex items-center justify-center\">\n                  <svg className=\"w-8 h-8 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z\" />\n                  </svg>\n                </div>\n                <span className=\"text-gray-400 text-sm\">No Image</span>\n              </div>\n            </div>\n          )}\n\n          {/* Overlay buttons */}\n          <div className=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300\">\n            <div className=\"absolute top-4 right-4 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n              <button\n                onClick={handleWishlist}\n                className=\"p-2 bg-white rounded-full shadow-md hover:bg-gray-50 transition-colors duration-200\"\n              >\n                {isWishlisted ? (\n                  <HeartIconSolid className=\"h-5 w-5 text-red-500\" />\n                ) : (\n                  <HeartIcon className=\"h-5 w-5 text-gray-600\" />\n                )}\n              </button>\n            </div>\n\n            <div className=\"absolute bottom-4 left-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n              <button\n                onClick={handleAddToCart}\n                disabled={isLoading || !product.variants?.[0]?.available}\n                className=\"w-full bg-white text-gray-900 py-2 px-4 rounded-md font-medium hover:bg-gray-50 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2\"\n              >\n                {isLoading ? (\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900\"></div>\n                ) : (\n                  <>\n                    <ShoppingBagIcon className=\"h-4 w-4\" />\n                    {product.variants?.[0]?.available ? 'Quick Add' : 'Sold Out'}\n                  </>\n                )}\n              </button>\n            </div>\n          </div>\n\n          {/* Sale badge */}\n          {product.variants?.[0]?.compareAtPrice && (\n            <div className=\"absolute top-4 left-4\">\n              <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800\">\n                Sale\n              </span>\n            </div>\n          )}\n        </div>\n\n        <div className=\"p-4\">\n          <div className=\"mb-2\">\n            <h3 className=\"text-sm font-medium text-gray-900 line-clamp-2 group-hover:text-gold transition-colors duration-200\">\n              {product.title}\n            </h3>\n            {product.vendor && (\n              <p className=\"text-xs text-gray-500 mt-1\">{product.vendor}</p>\n            )}\n          </div>\n\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex flex-col\">\n              {hasVariantPricing ? (\n                <span className=\"text-sm font-medium text-gray-900\">\n                  {shopifyHelpers.formatPrice(minPrice)} - {shopifyHelpers.formatPrice(maxPrice)}\n                </span>\n              ) : minPrice ? (\n                <div className=\"flex items-center gap-2\">\n                  <span className=\"text-sm font-medium text-gray-900\">\n                    {shopifyHelpers.formatPrice(minPrice)}\n                  </span>\n                  {product.variants?.[0]?.compareAtPrice && (\n                    <span className=\"text-xs text-gray-500 line-through\">\n                      {shopifyHelpers.formatPrice(product.variants[0].compareAtPrice)}\n                    </span>\n                  )}\n                </div>\n              ) : (\n                <span className=\"text-sm text-gray-500\">Price unavailable</span>\n              )}\n            </div>\n\n            {/* Rating stars placeholder */}\n            <div className=\"flex items-center\">\n              {[...Array(5)].map((_, i) => (\n                <svg\n                  key={i}\n                  className=\"h-3 w-3 text-gold\"\n                  fill=\"currentColor\"\n                  viewBox=\"0 0 20 20\"\n                >\n                  <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n                </svg>\n              ))}\n            </div>\n          </div>\n        </div>\n      </Link>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AATA;;;;;;;;;;AAee,SAAS,YAAY,EAAE,OAAO,EAAoB;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IAE5B,MAAM,kBAAkB,OAAO;QAC7B,EAAE,cAAc;QAChB,EAAE,eAAe;QAEjB,IAAI,CAAC,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,MAAM,KAAK,GAAG;QAExD,aAAa;QAEb,MAAM,UAAU,QAAQ,QAAQ,CAAC,EAAE,EAAE,kCAAkC;QAEvE,IAAI;YACF,MAAM,UAAU;gBACd,WAAW,QAAQ,EAAE;gBACrB,UAAU;gBACV,OAAO,QAAQ,KAAK;gBACpB,OAAO,QAAQ,KAAK;gBACpB,OAAO,QAAQ,MAAM,EAAE,CAAC,EAAE,GAAG;oBAC3B,KAAK,QAAQ,MAAM,CAAC,EAAE,CAAC,GAAG;oBAC1B,SAAS,QAAQ,MAAM,CAAC,EAAE,CAAC,OAAO,IAAI,QAAQ,KAAK;gBACrD,IAAI;gBACJ,QAAQ,QAAQ,MAAM;YACxB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,gBAAgB,CAAC;IACnB;IAEA,MAAM,eAAe,QAAQ,MAAM,EAAE,CAAC,EAAE;IACxC,MAAM,iBAAiB,QAAQ,MAAM,EAAE,CAAC,EAAE;IAC1C,MAAM,WAAW,QAAQ,UAAU,EAAE;IACrC,MAAM,WAAW,QAAQ,UAAU,EAAE;IACrC,MAAM,oBAAoB,YAAY,YAAY,SAAS,MAAM,KAAK,SAAS,MAAM;IAErF,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,YAAY;YAAE,GAAG,CAAC;QAAE;QACpB,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAU;kBAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;YAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,MAAM,EAAE;;8BACvC,8OAAC;oBAAI,WAAU;;wBACZ,6BACC;;8CACE,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAK,aAAa,GAAG;oCACrB,KAAK,aAAa,OAAO,IAAI,QAAQ,KAAK;oCAC1C,IAAI;oCACJ,WAAU;oCACV,OAAM;;;;;;gCAEP,gCACC,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAK,eAAe,GAAG;oCACvB,KAAK,eAAe,OAAO,IAAI,QAAQ,KAAK;oCAC5C,IAAI;oCACJ,WAAU;oCACV,OAAM;;;;;;;yDAKZ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;4CAAwB,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDAC/E,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAGzE,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;;;;;;;sCAM9C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,SAAS;wCACT,WAAU;kDAET,6BACC,8OAAC,+MAAA,CAAA,YAAc;4CAAC,WAAU;;;;;iEAE1B,8OAAC,iNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAK3B,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,SAAS;wCACT,UAAU,aAAa,CAAC,QAAQ,QAAQ,EAAE,CAAC,EAAE,EAAE;wCAC/C,WAAU;kDAET,0BACC,8OAAC;4CAAI,WAAU;;;;;iEAEf;;8DACE,8OAAC,6NAAA,CAAA,kBAAe;oDAAC,WAAU;;;;;;gDAC1B,QAAQ,QAAQ,EAAE,CAAC,EAAE,EAAE,YAAY,cAAc;;;;;;;;;;;;;;;;;;;wBAQ3D,QAAQ,QAAQ,EAAE,CAAC,EAAE,EAAE,gCACtB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CAA8F;;;;;;;;;;;;;;;;;8BAOpH,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;gCAEf,QAAQ,MAAM,kBACb,8OAAC;oCAAE,WAAU;8CAA8B,QAAQ,MAAM;;;;;;;;;;;;sCAI7D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACZ,kCACC,8OAAC;wCAAK,WAAU;;4CACb,8GAAA,CAAA,iBAAc,CAAC,WAAW,CAAC;4CAAU;4CAAI,8GAAA,CAAA,iBAAc,CAAC,WAAW,CAAC;;;;;;+CAErE,yBACF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DACb,8GAAA,CAAA,iBAAc,CAAC,WAAW,CAAC;;;;;;4CAE7B,QAAQ,QAAQ,EAAE,CAAC,EAAE,EAAE,gCACtB,8OAAC;gDAAK,WAAU;0DACb,8GAAA,CAAA,iBAAc,CAAC,WAAW,CAAC,QAAQ,QAAQ,CAAC,EAAE,CAAC,cAAc;;;;;;;;;;;6DAKpE,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;8CAK5C,8OAAC;oCAAI,WAAU;8CACZ;2CAAI,MAAM;qCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;4CAEC,WAAU;4CACV,MAAK;4CACL,SAAQ;sDAER,cAAA,8OAAC;gDAAK,GAAE;;;;;;2CALH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcvB", "debugId": null}}, {"offset": {"line": 382, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/app/collections/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { shopifyHelpers } from '@/lib/shopify';\nimport ProductCard from '@/components/ProductCard';\nimport { FunnelIcon, Squares2X2Icon, Bars3Icon } from '@heroicons/react/24/outline';\n\nconst sortOptions = [\n  { name: 'Most Popular', value: 'popular' },\n  { name: 'Best Rating', value: 'rating' },\n  { name: 'Newest', value: 'newest' },\n  { name: 'Price: Low to High', value: 'price-asc' },\n  { name: 'Price: High to Low', value: 'price-desc' },\n];\n\nconst filters = [\n  {\n    id: 'category',\n    name: 'Category',\n    options: [\n      { value: 'rings', label: 'Rings' },\n      { value: 'necklaces', label: 'Necklaces' },\n      { value: 'earrings', label: 'Earrings' },\n      { value: 'bracelets', label: 'Bracelets' },\n    ],\n  },\n  {\n    id: 'material',\n    name: 'Material',\n    options: [\n      { value: 'gold', label: 'Gold' },\n      { value: 'silver', label: 'Silver' },\n      { value: 'platinum', label: 'Platinum' },\n      { value: 'diamond', label: 'Diamond' },\n    ],\n  },\n  {\n    id: 'price',\n    name: 'Price Range',\n    options: [\n      { value: '0-100', label: 'Under $100' },\n      { value: '100-500', label: '$100 - $500' },\n      { value: '500-1000', label: '$500 - $1,000' },\n      { value: '1000+', label: 'Over $1,000' },\n    ],\n  },\n];\n\nexport default function CollectionsPage() {\n  const [products, setProducts] = useState<any[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');\n  const [sortBy, setSortBy] = useState('popular');\n  const [selectedFilters, setSelectedFilters] = useState<Record<string, string[]>>({});\n  const [mobileFiltersOpen, setMobileFiltersOpen] = useState(false);\n\n  useEffect(() => {\n    const fetchProducts = async () => {\n      try {\n        setLoading(true);\n        const fetchedProducts = await shopifyHelpers.fetchProducts(20);\n        setProducts(fetchedProducts);\n      } catch (error) {\n        console.error('Error fetching products:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchProducts();\n  }, []);\n\n  const handleFilterChange = (filterId: string, value: string) => {\n    setSelectedFilters(prev => {\n      const currentFilters = prev[filterId] || [];\n      const isSelected = currentFilters.includes(value);\n      \n      if (isSelected) {\n        return {\n          ...prev,\n          [filterId]: currentFilters.filter(v => v !== value)\n        };\n      } else {\n        return {\n          ...prev,\n          [filterId]: [...currentFilters, value]\n        };\n      }\n    });\n  };\n\n  const clearFilters = () => {\n    setSelectedFilters({});\n  };\n\n  const filteredProducts = products; // TODO: Implement actual filtering logic\n\n  return (\n    <div className=\"bg-white\">\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"border-b border-gray-200 pb-6\">\n          <motion.h1\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            className=\"text-4xl font-bold font-playfair text-gray-900\"\n          >\n            All Collections\n          </motion.h1>\n          <motion.p\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.1 }}\n            className=\"mt-4 text-lg text-gray-600\"\n          >\n            Discover our complete range of exquisite jewelry pieces\n          </motion.p>\n        </div>\n\n        <div className=\"pt-6 lg:grid lg:grid-cols-4 lg:gap-x-8\">\n          {/* Filters */}\n          <aside className=\"hidden lg:block\">\n            <h2 className=\"sr-only\">Filters</h2>\n\n            <div className=\"space-y-6\">\n              <div className=\"flex items-center justify-between\">\n                <h3 className=\"text-lg font-medium text-gray-900\">Filters</h3>\n                <button\n                  onClick={clearFilters}\n                  className=\"text-sm text-indigo-600 hover:text-indigo-500\"\n                >\n                  Clear all\n                </button>\n              </div>\n\n              {filters.map((section) => (\n                <div key={section.id} className=\"border-b border-gray-200 pb-6\">\n                  <h3 className=\"text-sm font-medium text-gray-900 mb-4\">\n                    {section.name}\n                  </h3>\n                  <div className=\"space-y-3\">\n                    {section.options.map((option) => (\n                      <div key={option.value} className=\"flex items-center\">\n                        <input\n                          id={`${section.id}-${option.value}`}\n                          type=\"checkbox\"\n                          checked={selectedFilters[section.id]?.includes(option.value) || false}\n                          onChange={() => handleFilterChange(section.id, option.value)}\n                          className=\"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded\"\n                        />\n                        <label\n                          htmlFor={`${section.id}-${option.value}`}\n                          className=\"ml-3 text-sm text-gray-600\"\n                        >\n                          {option.label}\n                        </label>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </aside>\n\n          {/* Product grid */}\n          <div className=\"lg:col-span-3\">\n            {/* Sort and view options */}\n            <div className=\"flex items-center justify-between border-b border-gray-200 pb-6\">\n              <div className=\"flex items-center\">\n                <button\n                  onClick={() => setMobileFiltersOpen(true)}\n                  className=\"lg:hidden p-2 text-gray-400 hover:text-gray-500\"\n                >\n                  <FunnelIcon className=\"h-5 w-5\" />\n                </button>\n                <p className=\"text-sm text-gray-700\">\n                  {loading ? 'Loading...' : `${filteredProducts.length} products`}\n                </p>\n              </div>\n\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"flex items-center\">\n                  <label htmlFor=\"sort\" className=\"sr-only\">\n                    Sort\n                  </label>\n                  <select\n                    id=\"sort\"\n                    value={sortBy}\n                    onChange={(e) => setSortBy(e.target.value)}\n                    className=\"block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm\"\n                  >\n                    {sortOptions.map((option) => (\n                      <option key={option.value} value={option.value}>\n                        {option.name}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n\n                <div className=\"flex items-center\">\n                  <button\n                    onClick={() => setViewMode('grid')}\n                    className={`p-2 ${viewMode === 'grid' ? 'text-indigo-600' : 'text-gray-400'} hover:text-gray-500`}\n                  >\n                    <Squares2X2Icon className=\"h-5 w-5\" />\n                  </button>\n                  <button\n                    onClick={() => setViewMode('list')}\n                    className={`p-2 ${viewMode === 'list' ? 'text-indigo-600' : 'text-gray-400'} hover:text-gray-500`}\n                  >\n                    <Bars3Icon className=\"h-5 w-5\" />\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            {/* Products */}\n            {loading ? (\n              <div className=\"mt-8 grid grid-cols-1 gap-y-10 gap-x-6 sm:grid-cols-2 lg:grid-cols-3 xl:gap-x-8\">\n                {[...Array(9)].map((_, i) => (\n                  <div key={i} className=\"animate-pulse\">\n                    <div className=\"aspect-square w-full bg-gray-200 rounded-lg\"></div>\n                    <div className=\"mt-4 space-y-2\">\n                      <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n                      <div className=\"h-4 bg-gray-200 rounded w-1/2\"></div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            ) : filteredProducts.length === 0 ? (\n              <div className=\"mt-8 text-center\">\n                <p className=\"text-gray-500\">No products found matching your criteria.</p>\n                <button\n                  onClick={clearFilters}\n                  className=\"mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-600 bg-indigo-100 hover:bg-indigo-200\"\n                >\n                  Clear filters\n                </button>\n              </div>\n            ) : (\n              <motion.div\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ duration: 0.6 }}\n                className={`mt-8 grid gap-y-10 gap-x-6 xl:gap-x-8 ${\n                  viewMode === 'grid'\n                    ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3'\n                    : 'grid-cols-1'\n                }`}\n              >\n                {filteredProducts.map((product, index) => (\n                  <motion.div\n                    key={product.id}\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ duration: 0.6, delay: index * 0.05 }}\n                  >\n                    <ProductCard product={product} />\n                  </motion.div>\n                ))}\n              </motion.div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AANA;;;;;;;AAQA,MAAM,cAAc;IAClB;QAAE,MAAM;QAAgB,OAAO;IAAU;IACzC;QAAE,MAAM;QAAe,OAAO;IAAS;IACvC;QAAE,MAAM;QAAU,OAAO;IAAS;IAClC;QAAE,MAAM;QAAsB,OAAO;IAAY;IACjD;QAAE,MAAM;QAAsB,OAAO;IAAa;CACnD;AAED,MAAM,UAAU;IACd;QACE,IAAI;QACJ,MAAM;QACN,SAAS;YACP;gBAAE,OAAO;gBAAS,OAAO;YAAQ;YACjC;gBAAE,OAAO;gBAAa,OAAO;YAAY;YACzC;gBAAE,OAAO;gBAAY,OAAO;YAAW;YACvC;gBAAE,OAAO;gBAAa,OAAO;YAAY;SAC1C;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;YACP;gBAAE,OAAO;gBAAQ,OAAO;YAAO;YAC/B;gBAAE,OAAO;gBAAU,OAAO;YAAS;YACnC;gBAAE,OAAO;gBAAY,OAAO;YAAW;YACvC;gBAAE,OAAO;gBAAW,OAAO;YAAU;SACtC;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;YACP;gBAAE,OAAO;gBAAS,OAAO;YAAa;YACtC;gBAAE,OAAO;gBAAW,OAAO;YAAc;YACzC;gBAAE,OAAO;gBAAY,OAAO;YAAgB;YAC5C;gBAAE,OAAO;gBAAS,OAAO;YAAc;SACxC;IACH;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B,CAAC;IAClF,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,IAAI;gBACF,WAAW;gBACX,MAAM,kBAAkB,MAAM,8GAAA,CAAA,iBAAc,CAAC,aAAa,CAAC;gBAC3D,YAAY;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;YAC5C,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,qBAAqB,CAAC,UAAkB;QAC5C,mBAAmB,CAAA;YACjB,MAAM,iBAAiB,IAAI,CAAC,SAAS,IAAI,EAAE;YAC3C,MAAM,aAAa,eAAe,QAAQ,CAAC;YAE3C,IAAI,YAAY;gBACd,OAAO;oBACL,GAAG,IAAI;oBACP,CAAC,SAAS,EAAE,eAAe,MAAM,CAAC,CAAA,IAAK,MAAM;gBAC/C;YACF,OAAO;gBACL,OAAO;oBACL,GAAG,IAAI;oBACP,CAAC,SAAS,EAAE;2BAAI;wBAAgB;qBAAM;gBACxC;YACF;QACF;IACF;IAEA,MAAM,eAAe;QACnB,mBAAmB,CAAC;IACtB;IAEA,MAAM,mBAAmB,UAAU,yCAAyC;IAE5E,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;sCACX;;;;;;sCAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCACX;;;;;;;;;;;;8BAKH,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCAAG,WAAU;8CAAU;;;;;;8CAExB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAoC;;;;;;8DAClD,8OAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;wCAKF,QAAQ,GAAG,CAAC,CAAC,wBACZ,8OAAC;gDAAqB,WAAU;;kEAC9B,8OAAC;wDAAG,WAAU;kEACX,QAAQ,IAAI;;;;;;kEAEf,8OAAC;wDAAI,WAAU;kEACZ,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,uBACpB,8OAAC;gEAAuB,WAAU;;kFAChC,8OAAC;wEACC,IAAI,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,OAAO,KAAK,EAAE;wEACnC,MAAK;wEACL,SAAS,eAAe,CAAC,QAAQ,EAAE,CAAC,EAAE,SAAS,OAAO,KAAK,KAAK;wEAChE,UAAU,IAAM,mBAAmB,QAAQ,EAAE,EAAE,OAAO,KAAK;wEAC3D,WAAU;;;;;;kFAEZ,8OAAC;wEACC,SAAS,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,OAAO,KAAK,EAAE;wEACxC,WAAU;kFAET,OAAO,KAAK;;;;;;;+DAZP,OAAO,KAAK;;;;;;;;;;;+CANlB,QAAQ,EAAE;;;;;;;;;;;;;;;;;sCA6B1B,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,qBAAqB;oDACpC,WAAU;8DAEV,cAAA,8OAAC,mNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;8DAExB,8OAAC;oDAAE,WAAU;8DACV,UAAU,eAAe,GAAG,iBAAiB,MAAM,CAAC,SAAS,CAAC;;;;;;;;;;;;sDAInE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,SAAQ;4DAAO,WAAU;sEAAU;;;;;;sEAG1C,8OAAC;4DACC,IAAG;4DACH,OAAO;4DACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;4DACzC,WAAU;sEAET,YAAY,GAAG,CAAC,CAAC,uBAChB,8OAAC;oEAA0B,OAAO,OAAO,KAAK;8EAC3C,OAAO,IAAI;mEADD,OAAO,KAAK;;;;;;;;;;;;;;;;8DAO/B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,SAAS,IAAM,YAAY;4DAC3B,WAAW,CAAC,IAAI,EAAE,aAAa,SAAS,oBAAoB,gBAAgB,oBAAoB,CAAC;sEAEjG,cAAA,8OAAC,2NAAA,CAAA,iBAAc;gEAAC,WAAU;;;;;;;;;;;sEAE5B,8OAAC;4DACC,SAAS,IAAM,YAAY;4DAC3B,WAAW,CAAC,IAAI,EAAE,aAAa,SAAS,oBAAoB,gBAAgB,oBAAoB,CAAC;sEAEjG,cAAA,8OAAC,iNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAO5B,wBACC,8OAAC;oCAAI,WAAU;8CACZ;2CAAI,MAAM;qCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;4CAAY,WAAU;;8DACrB,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;;;;;;;;2CAJT;;;;;;;;;2CASZ,iBAAiB,MAAM,KAAK,kBAC9B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;sDAC7B,8OAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;yDAKH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;oCAAE;oCACtB,SAAS;wCAAE,SAAS;oCAAE;oCACtB,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,WAAW,CAAC,sCAAsC,EAChD,aAAa,SACT,8CACA,eACJ;8CAED,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,UAAU;gDAAK,OAAO,QAAQ;4CAAK;sDAEjD,cAAA,8OAAC,0HAAA,CAAA,UAAW;gDAAC,SAAS;;;;;;2CALjB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAenC", "debugId": null}}]}