{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/components/keyboard.js"], "sourcesContent": ["var o=(r=>(r.<PERSON>=\" \",r.<PERSON><PERSON>=\"Enter\",r.<PERSON>=\"Escape\",r.<PERSON>space=\"Backspace\",r.Delete=\"Delete\",r.<PERSON>=\"ArrowLeft\",r.<PERSON>p=\"ArrowUp\",r.<PERSON>=\"ArrowRight\",r.<PERSON>=\"ArrowDown\",r.Home=\"Home\",r.End=\"End\",r.PageUp=\"PageUp\",r.PageDown=\"PageDown\",r.Tab=\"Tab\",r))(o||{});export{o as Keys};\n"], "names": [], "mappings": ";;;AAAA,IAAI,IAAE,CAAC,CAAA,IAAG,CAAC,EAAE,KAAK,GAAC,KAAI,EAAE,KAAK,GAAC,SAAQ,EAAE,MAAM,GAAC,UAAS,EAAE,SAAS,GAAC,aAAY,EAAE,MAAM,GAAC,UAAS,EAAE,SAAS,GAAC,aAAY,EAAE,OAAO,GAAC,WAAU,EAAE,UAAU,GAAC,cAAa,EAAE,SAAS,GAAC,aAAY,EAAE,IAAI,GAAC,QAAO,EAAE,GAAG,GAAC,OAAM,EAAE,MAAM,GAAC,UAAS,EAAE,QAAQ,GAAC,YAAW,EAAE,GAAG,GAAC,OAAM,CAAC,CAAC,EAAE,KAAG,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 14, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/utils/env.js"], "sourcesContent": ["var i=Object.defineProperty;var d=(t,e,n)=>e in t?i(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var r=(t,e,n)=>(d(t,typeof e!=\"symbol\"?e+\"\":e,n),n);class o{constructor(){r(this,\"current\",this.detect());r(this,\"handoffState\",\"pending\");r(this,\"currentId\",0)}set(e){this.current!==e&&(this.handoffState=\"pending\",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return this.current===\"server\"}get isClient(){return this.current===\"client\"}detect(){return typeof window==\"undefined\"||typeof document==\"undefined\"?\"server\":\"client\"}handoff(){this.handoffState===\"pending\"&&(this.handoffState=\"complete\")}get isHandoffComplete(){return this.handoffState===\"complete\"}}let s=new o;export{s as env};\n"], "names": [], "mappings": ";;;AAAA,IAAI,IAAE,OAAO,cAAc;AAAC,IAAI,IAAE,CAAC,GAAE,GAAE,IAAI,KAAK,IAAE,EAAE,GAAE,GAAE;QAAC,YAAW,CAAC;QAAE,cAAa,CAAC;QAAE,UAAS,CAAC;QAAE,OAAM;IAAC,KAAG,CAAC,CAAC,EAAE,GAAC;AAAE,IAAI,IAAE,CAAC,GAAE,GAAE,IAAI,CAAC,EAAE,GAAE,OAAO,KAAG,WAAS,IAAE,KAAG,GAAE,IAAG,CAAC;AAAE,MAAM;IAAE,aAAa;QAAC,EAAE,IAAI,EAAC,WAAU,IAAI,CAAC,MAAM;QAAI,EAAE,IAAI,EAAC,gBAAe;QAAW,EAAE,IAAI,EAAC,aAAY;IAAE;IAAC,IAAI,CAAC,EAAC;QAAC,IAAI,CAAC,OAAO,KAAG,KAAG,CAAC,IAAI,CAAC,YAAY,GAAC,WAAU,IAAI,CAAC,SAAS,GAAC,GAAE,IAAI,CAAC,OAAO,GAAC,CAAC;IAAC;IAAC,QAAO;QAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM;IAAG;IAAC,SAAQ;QAAC,OAAM,EAAE,IAAI,CAAC,SAAS;IAAA;IAAC,IAAI,WAAU;QAAC,OAAO,IAAI,CAAC,OAAO,KAAG;IAAQ;IAAC,IAAI,WAAU;QAAC,OAAO,IAAI,CAAC,OAAO,KAAG;IAAQ;IAAC,SAAQ;QAAC,OAAO,uCAAyD,WAAS;IAAQ;IAAC,UAAS;QAAC,IAAI,CAAC,YAAY,KAAG,aAAW,CAAC,IAAI,CAAC,YAAY,GAAC,UAAU;IAAC;IAAC,IAAI,oBAAmB;QAAC,OAAO,IAAI,CAAC,YAAY,KAAG;IAAU;AAAC;AAAC,IAAI,IAAE,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/hooks/use-iso-morphic-effect.js"], "sourcesContent": ["import{useEffect as f,useLayoutEffect as c}from\"react\";import{env as i}from'../utils/env.js';let n=(e,t)=>{i.isServer?f(e,t):c(e,t)};export{n as useIsoMorphicEffect};\n"], "names": [], "mappings": ";;;AAAA;AAAuD;;;AAAsC,IAAI,IAAE,CAAC,GAAE;IAAK,6JAAA,CAAA,MAAC,CAAC,QAAQ,GAAC,CAAA,GAAA,qMAAA,CAAA,YAAC,AAAD,EAAE,GAAE,KAAG,CAAA,GAAA,qMAAA,CAAA,kBAAC,AAAD,EAAE,GAAE;AAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 77, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/hooks/use-latest-value.js"], "sourcesContent": ["import{useRef as t}from\"react\";import{useIsoMorphicEffect as o}from'./use-iso-morphic-effect.js';function s(e){let r=t(e);return o(()=>{r.current=e},[e]),r}export{s as useLatestValue};\n"], "names": [], "mappings": ";;;AAAA;AAA+B;;;AAAkE,SAAS,EAAE,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,SAAC,AAAD,EAAE;IAAG,OAAO,CAAA,GAAA,yLAAA,CAAA,sBAAC,AAAD,EAAE;QAAK,EAAE,OAAO,GAAC;IAAC,GAAE;QAAC;KAAE,GAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/hooks/use-event-listener.js"], "sourcesContent": ["import{useEffect as d}from\"react\";import{useLatestValue as s}from'./use-latest-value.js';function E(n,e,a,t){let i=s(a);d(()=>{n=n!=null?n:window;function r(o){i.current(o)}return n.addEventListener(e,r,t),()=>n.removeEventListener(e,r,t)},[n,e,t])}export{E as useEventListener};\n"], "names": [], "mappings": ";;;AAAA;AAAkC;;;AAAuD,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,gLAAA,CAAA,iBAAC,AAAD,EAAE;IAAG,CAAA,GAAA,qMAAA,CAAA,YAAC,AAAD,EAAE;QAAK,IAAE,KAAG,OAAK,IAAE;QAAO,SAAS,EAAE,CAAC;YAAE,EAAE,OAAO,CAAC;QAAE;QAAC,OAAO,EAAE,gBAAgB,CAAC,GAAE,GAAE,IAAG,IAAI,EAAE,mBAAmB,CAAC,GAAE,GAAE;IAAE,GAAE;QAAC;QAAE;QAAE;KAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/utils/default-map.js"], "sourcesContent": ["class a extends Map{constructor(t){super();this.factory=t}get(t){let e=super.get(t);return e===void 0&&(e=this.factory(t),this.set(t,e)),e}}export{a as DefaultMap};\n"], "names": [], "mappings": ";;;AAAA,MAAM,UAAU;IAAI,YAAY,CAAC,CAAC;QAAC,KAAK;QAAG,IAAI,CAAC,OAAO,GAAC;IAAC;IAAC,IAAI,CAAC,EAAC;QAAC,IAAI,IAAE,KAAK,CAAC,IAAI;QAAG,OAAO,MAAI,KAAK,KAAG,CAAC,IAAE,IAAI,CAAC,OAAO,CAAC,IAAG,IAAI,CAAC,GAAG,CAAC,GAAE,EAAE,GAAE;IAAC;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 141, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/utils/micro-task.js"], "sourcesContent": ["function t(e){typeof queueMicrotask==\"function\"?queueMicrotask(e):Promise.resolve().then(e).catch(o=>setTimeout(()=>{throw o}))}export{t as microTask};\n"], "names": [], "mappings": ";;;AAAA,SAAS,EAAE,CAAC;IAAE,OAAO,kBAAgB,aAAW,eAAe,KAAG,QAAQ,OAAO,GAAG,IAAI,CAAC,GAAG,KAAK,CAAC,CAAA,IAAG,WAAW;YAAK,MAAM;QAAC;AAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/utils/disposables.js"], "sourcesContent": ["import{microTask as a}from'./micro-task.js';function o(){let s=[],r={addEventListener(e,t,n,i){return e.addEventListener(t,n,i),r.add(()=>e.removeEventListener(t,n,i))},requestAnimationFrame(...e){let t=requestAnimationFrame(...e);return r.add(()=>cancelAnimationFrame(t))},nextFrame(...e){return r.requestAnimationFrame(()=>r.requestAnimationFrame(...e))},setTimeout(...e){let t=setTimeout(...e);return r.add(()=>clearTimeout(t))},microTask(...e){let t={current:!0};return a(()=>{t.current&&e[0]()}),r.add(()=>{t.current=!1})},style(e,t,n){let i=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:n}),this.add(()=>{Object.assign(e.style,{[t]:i})})},group(e){let t=o();return e(t),this.add(()=>t.dispose())},add(e){return s.includes(e)||s.push(e),()=>{let t=s.indexOf(e);if(t>=0)for(let n of s.splice(t,1))n()}},dispose(){for(let e of s.splice(0))e()}};return r}export{o as disposables};\n"], "names": [], "mappings": ";;;AAAA;;AAA4C,SAAS;IAAI,IAAI,IAAE,EAAE,EAAC,IAAE;QAAC,kBAAiB,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,OAAO,EAAE,gBAAgB,CAAC,GAAE,GAAE,IAAG,EAAE,GAAG,CAAC,IAAI,EAAE,mBAAmB,CAAC,GAAE,GAAE;QAAG;QAAE,uBAAsB,GAAG,CAAC;YAAE,IAAI,IAAE,yBAAyB;YAAG,OAAO,EAAE,GAAG,CAAC,IAAI,qBAAqB;QAAG;QAAE,WAAU,GAAG,CAAC;YAAE,OAAO,EAAE,qBAAqB,CAAC,IAAI,EAAE,qBAAqB,IAAI;QAAG;QAAE,YAAW,GAAG,CAAC;YAAE,IAAI,IAAE,cAAc;YAAG,OAAO,EAAE,GAAG,CAAC,IAAI,aAAa;QAAG;QAAE,WAAU,GAAG,CAAC;YAAE,IAAI,IAAE;gBAAC,SAAQ,CAAC;YAAC;YAAE,OAAO,CAAA,GAAA,uKAAA,CAAA,YAAC,AAAD,EAAE;gBAAK,EAAE,OAAO,IAAE,CAAC,CAAC,EAAE;YAAE,IAAG,EAAE,GAAG,CAAC;gBAAK,EAAE,OAAO,GAAC,CAAC;YAAC;QAAE;QAAE,OAAM,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,KAAK,CAAC,gBAAgB,CAAC;YAAG,OAAO,OAAO,MAAM,CAAC,EAAE,KAAK,EAAC;gBAAC,CAAC,EAAE,EAAC;YAAC,IAAG,IAAI,CAAC,GAAG,CAAC;gBAAK,OAAO,MAAM,CAAC,EAAE,KAAK,EAAC;oBAAC,CAAC,EAAE,EAAC;gBAAC;YAAE;QAAE;QAAE,OAAM,CAAC;YAAE,IAAI,IAAE;YAAI,OAAO,EAAE,IAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO;QAAG;QAAE,KAAI,CAAC;YAAE,OAAO,EAAE,QAAQ,CAAC,MAAI,EAAE,IAAI,CAAC,IAAG;gBAAK,IAAI,IAAE,EAAE,OAAO,CAAC;gBAAG,IAAG,KAAG,GAAE,KAAI,IAAI,KAAK,EAAE,MAAM,CAAC,GAAE,GAAG;YAAG;QAAC;QAAE;YAAU,KAAI,IAAI,KAAK,EAAE,MAAM,CAAC,GAAG;QAAG;IAAC;IAAE,OAAO;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 216, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/machine.js"], "sourcesContent": ["var h=Object.defineProperty;var v=(t,e,r)=>e in t?h(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r;var S=(t,e,r)=>(v(t,typeof e!=\"symbol\"?e+\"\":e,r),r),b=(t,e,r)=>{if(!e.has(t))throw TypeError(\"Cannot \"+r)};var i=(t,e,r)=>(b(t,e,\"read from private field\"),r?r.call(t):e.get(t)),c=(t,e,r)=>{if(e.has(t))throw TypeError(\"Cannot add the same private member more than once\");e instanceof WeakSet?e.add(t):e.set(t,r)},u=(t,e,r,s)=>(b(t,e,\"write to private field\"),s?s.call(t,r):e.set(t,r),r);var n,a,o;import{DefaultMap as m}from'./utils/default-map.js';import{disposables as p}from'./utils/disposables.js';import{env as d}from'./utils/env.js';class x{constructor(e){c(this,n,{});c(this,a,new m(()=>new Set));c(this,o,new Set);S(this,\"disposables\",p());u(this,n,e),d.isServer&&this.disposables.microTask(()=>{this.dispose()})}dispose(){this.disposables.dispose()}get state(){return i(this,n)}subscribe(e,r){if(d.isServer)return()=>{};let s={selector:e,callback:r,current:e(i(this,n))};return i(this,o).add(s),this.disposables.add(()=>{i(this,o).delete(s)})}on(e,r){return d.isServer?()=>{}:(i(this,a).get(e).add(r),this.disposables.add(()=>{i(this,a).get(e).delete(r)}))}send(e){let r=this.reduce(i(this,n),e);if(r!==i(this,n)){u(this,n,r);for(let s of i(this,o)){let l=s.selector(i(this,n));j(s.current,l)||(s.current=l,s.callback(l))}for(let s of i(this,a).get(e.type))s(i(this,n),e)}}}n=new WeakMap,a=new WeakMap,o=new WeakMap;function j(t,e){return Object.is(t,e)?!0:typeof t!=\"object\"||t===null||typeof e!=\"object\"||e===null?!1:Array.isArray(t)&&Array.isArray(e)?t.length!==e.length?!1:f(t[Symbol.iterator](),e[Symbol.iterator]()):t instanceof Map&&e instanceof Map||t instanceof Set&&e instanceof Set?t.size!==e.size?!1:f(t.entries(),e.entries()):y(t)&&y(e)?f(Object.entries(t)[Symbol.iterator](),Object.entries(e)[Symbol.iterator]()):!1}function f(t,e){do{let r=t.next(),s=e.next();if(r.done&&s.done)return!0;if(r.done||s.done||!Object.is(r.value,s.value))return!1}while(!0)}function y(t){if(Object.prototype.toString.call(t)!==\"[object Object]\")return!1;let e=Object.getPrototypeOf(t);return e===null||Object.getPrototypeOf(e)===null}function R(t){let[e,r]=t(),s=p();return(...l)=>{e(...l),s.dispose(),s.microTask(r)}}export{x as Machine,R as batch,j as shallowEqual};\n"], "names": [], "mappings": ";;;;;AAAigB;AAAoD;AAAqD;AAA1mB,IAAI,IAAE,OAAO,cAAc;AAAC,IAAI,IAAE,CAAC,GAAE,GAAE,IAAI,KAAK,IAAE,EAAE,GAAE,GAAE;QAAC,YAAW,CAAC;QAAE,cAAa,CAAC;QAAE,UAAS,CAAC;QAAE,OAAM;IAAC,KAAG,CAAC,CAAC,EAAE,GAAC;AAAE,IAAI,IAAE,CAAC,GAAE,GAAE,IAAI,CAAC,EAAE,GAAE,OAAO,KAAG,WAAS,IAAE,KAAG,GAAE,IAAG,CAAC,GAAE,IAAE,CAAC,GAAE,GAAE;IAAK,IAAG,CAAC,EAAE,GAAG,CAAC,IAAG,MAAM,UAAU,YAAU;AAAE;AAAE,IAAI,IAAE,CAAC,GAAE,GAAE,IAAI,CAAC,EAAE,GAAE,GAAE,4BAA2B,IAAE,EAAE,IAAI,CAAC,KAAG,EAAE,GAAG,CAAC,EAAE,GAAE,IAAE,CAAC,GAAE,GAAE;IAAK,IAAG,EAAE,GAAG,CAAC,IAAG,MAAM,UAAU;IAAqD,aAAa,UAAQ,EAAE,GAAG,CAAC,KAAG,EAAE,GAAG,CAAC,GAAE;AAAE,GAAE,IAAE,CAAC,GAAE,GAAE,GAAE,IAAI,CAAC,EAAE,GAAE,GAAE,2BAA0B,IAAE,EAAE,IAAI,CAAC,GAAE,KAAG,EAAE,GAAG,CAAC,GAAE,IAAG,CAAC;AAAE,IAAI,GAAE,GAAE;;;;AAAgJ,MAAM;IAAE,YAAY,CAAC,CAAC;QAAC,EAAE,IAAI,EAAC,GAAE,CAAC;QAAG,EAAE,IAAI,EAAC,GAAE,IAAI,wKAAA,CAAA,aAAC,CAAC,IAAI,IAAI;QAAM,EAAE,IAAI,EAAC,GAAE,IAAI;QAAK,EAAE,IAAI,EAAC,eAAc,CAAA,GAAA,qKAAA,CAAA,cAAC,AAAD;QAAK,EAAE,IAAI,EAAC,GAAE,IAAG,6JAAA,CAAA,MAAC,CAAC,QAAQ,IAAE,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC;YAAK,IAAI,CAAC,OAAO;QAAE;IAAE;IAAC,UAAS;QAAC,IAAI,CAAC,WAAW,CAAC,OAAO;IAAE;IAAC,IAAI,QAAO;QAAC,OAAO,EAAE,IAAI,EAAC;IAAE;IAAC,UAAU,CAAC,EAAC,CAAC,EAAC;QAAC,IAAG,6JAAA,CAAA,MAAC,CAAC,QAAQ,EAAC,OAAM,KAAK;QAAE,IAAI,IAAE;YAAC,UAAS;YAAE,UAAS;YAAE,SAAQ,EAAE,EAAE,IAAI,EAAC;QAAG;QAAE,OAAO,EAAE,IAAI,EAAC,GAAG,GAAG,CAAC,IAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;YAAK,EAAE,IAAI,EAAC,GAAG,MAAM,CAAC;QAAE;IAAE;IAAC,GAAG,CAAC,EAAC,CAAC,EAAC;QAAC,OAAO,6JAAA,CAAA,MAAC,CAAC,QAAQ,GAAC,KAAK,IAAE,CAAC,EAAE,IAAI,EAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;YAAK,EAAE,IAAI,EAAC,GAAG,GAAG,CAAC,GAAG,MAAM,CAAC;QAAE,EAAE;IAAC;IAAC,KAAK,CAAC,EAAC;QAAC,IAAI,IAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,EAAC,IAAG;QAAG,IAAG,MAAI,EAAE,IAAI,EAAC,IAAG;YAAC,EAAE,IAAI,EAAC,GAAE;YAAG,KAAI,IAAI,KAAK,EAAE,IAAI,EAAC,GAAG;gBAAC,IAAI,IAAE,EAAE,QAAQ,CAAC,EAAE,IAAI,EAAC;gBAAI,EAAE,EAAE,OAAO,EAAC,MAAI,CAAC,EAAE,OAAO,GAAC,GAAE,EAAE,QAAQ,CAAC,EAAE;YAAC;YAAC,KAAI,IAAI,KAAK,EAAE,IAAI,EAAC,GAAG,GAAG,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAC,IAAG;QAAE;IAAC;AAAC;AAAC,IAAE,IAAI,SAAQ,IAAE,IAAI,SAAQ,IAAE,IAAI;AAAQ,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,OAAO,OAAO,EAAE,CAAC,GAAE,KAAG,CAAC,IAAE,OAAO,KAAG,YAAU,MAAI,QAAM,OAAO,KAAG,YAAU,MAAI,OAAK,CAAC,IAAE,MAAM,OAAO,CAAC,MAAI,MAAM,OAAO,CAAC,KAAG,EAAE,MAAM,KAAG,EAAE,MAAM,GAAC,CAAC,IAAE,EAAE,CAAC,CAAC,OAAO,QAAQ,CAAC,IAAG,CAAC,CAAC,OAAO,QAAQ,CAAC,MAAI,aAAa,OAAK,aAAa,OAAK,aAAa,OAAK,aAAa,MAAI,EAAE,IAAI,KAAG,EAAE,IAAI,GAAC,CAAC,IAAE,EAAE,EAAE,OAAO,IAAG,EAAE,OAAO,MAAI,EAAE,MAAI,EAAE,KAAG,EAAE,OAAO,OAAO,CAAC,EAAE,CAAC,OAAO,QAAQ,CAAC,IAAG,OAAO,OAAO,CAAC,EAAE,CAAC,OAAO,QAAQ,CAAC,MAAI,CAAC;AAAC;AAAC,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,GAAE;QAAC,IAAI,IAAE,EAAE,IAAI,IAAG,IAAE,EAAE,IAAI;QAAG,IAAG,EAAE,IAAI,IAAE,EAAE,IAAI,EAAC,OAAM,CAAC;QAAE,IAAG,EAAE,IAAI,IAAE,EAAE,IAAI,IAAE,CAAC,OAAO,EAAE,CAAC,EAAE,KAAK,EAAC,EAAE,KAAK,GAAE,OAAM,CAAC;IAAC,QAAO,CAAC,EAAE;AAAA;AAAC,SAAS,EAAE,CAAC;IAAE,IAAG,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAK,mBAAkB,OAAM,CAAC;IAAE,IAAI,IAAE,OAAO,cAAc,CAAC;IAAG,OAAO,MAAI,QAAM,OAAO,cAAc,CAAC,OAAK;AAAI;AAAC,SAAS,EAAE,CAAC;IAAE,IAAG,CAAC,GAAE,EAAE,GAAC,KAAI,IAAE,CAAA,GAAA,qKAAA,CAAA,cAAC,AAAD;IAAI,OAAM,CAAC,GAAG;QAAK,KAAK,IAAG,EAAE,OAAO,IAAG,EAAE,SAAS,CAAC;IAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 313, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/utils/match.js"], "sourcesContent": ["function u(r,n,...a){if(r in n){let e=n[r];return typeof e==\"function\"?e(...a):e}let t=new Error(`Tried to handle \"${r}\" but there is no handler defined. Only defined handlers are: ${Object.keys(n).map(e=>`\"${e}\"`).join(\", \")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,u),t}export{u as match};\n"], "names": [], "mappings": ";;;AAAA,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,GAAG,CAAC;IAAE,IAAG,KAAK,GAAE;QAAC,IAAI,IAAE,CAAC,CAAC,EAAE;QAAC,OAAO,OAAO,KAAG,aAAW,KAAK,KAAG;IAAC;IAAC,IAAI,IAAE,IAAI,MAAM,CAAC,iBAAiB,EAAE,EAAE,8DAA8D,EAAE,OAAO,IAAI,CAAC,GAAG,GAAG,CAAC,CAAA,IAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IAAE,MAAM,MAAM,iBAAiB,IAAE,MAAM,iBAAiB,CAAC,GAAE,IAAG;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 329, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/machines/stack-machine.js"], "sourcesContent": ["var a=Object.defineProperty;var r=(e,c,t)=>c in e?a(e,c,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[c]=t;var p=(e,c,t)=>(r(e,typeof c!=\"symbol\"?c+\"\":c,t),t);import{Machine as d}from'../machine.js';import{DefaultMap as l}from'../utils/default-map.js';import{match as u}from'../utils/match.js';var k=(t=>(t[t.Push=0]=\"Push\",t[t.Pop=1]=\"Pop\",t))(k||{});let y={[0](e,c){let t=c.id,s=e.stack,i=e.stack.indexOf(t);if(i!==-1){let n=e.stack.slice();return n.splice(i,1),n.push(t),s=n,{...e,stack:s}}return{...e,stack:[...e.stack,t]}},[1](e,c){let t=c.id,s=e.stack.indexOf(t);if(s===-1)return e;let i=e.stack.slice();return i.splice(s,1),{...e,stack:i}}};class o extends d{constructor(){super(...arguments);p(this,\"actions\",{push:t=>this.send({type:0,id:t}),pop:t=>this.send({type:1,id:t})});p(this,\"selectors\",{isTop:(t,s)=>t.stack[t.stack.length-1]===s,inStack:(t,s)=>t.stack.includes(s)})}static new(){return new o({stack:[]})}reduce(t,s){return u(s.type,y,t,s)}}const x=new l(()=>o.new());export{k as ActionTypes,x as stackMachines};\n"], "names": [], "mappings": ";;;;AAAwK;AAAwC;AAAqD;AAArQ,IAAI,IAAE,OAAO,cAAc;AAAC,IAAI,IAAE,CAAC,GAAE,GAAE,IAAI,KAAK,IAAE,EAAE,GAAE,GAAE;QAAC,YAAW,CAAC;QAAE,cAAa,CAAC;QAAE,UAAS,CAAC;QAAE,OAAM;IAAC,KAAG,CAAC,CAAC,EAAE,GAAC;AAAE,IAAI,IAAE,CAAC,GAAE,GAAE,IAAI,CAAC,EAAE,GAAE,OAAO,KAAG,WAAS,IAAE,KAAG,GAAE,IAAG,CAAC;;;;AAAyI,IAAI,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,IAAI,GAAC,EAAE,GAAC,QAAO,CAAC,CAAC,EAAE,GAAG,GAAC,EAAE,GAAC,OAAM,CAAC,CAAC,EAAE,KAAG,CAAC;AAAG,IAAI,IAAE;IAAC,CAAC,EAAE,EAAC,CAAC,EAAC,CAAC;QAAE,IAAI,IAAE,EAAE,EAAE,EAAC,IAAE,EAAE,KAAK,EAAC,IAAE,EAAE,KAAK,CAAC,OAAO,CAAC;QAAG,IAAG,MAAI,CAAC,GAAE;YAAC,IAAI,IAAE,EAAE,KAAK,CAAC,KAAK;YAAG,OAAO,EAAE,MAAM,CAAC,GAAE,IAAG,EAAE,IAAI,CAAC,IAAG,IAAE,GAAE;gBAAC,GAAG,CAAC;gBAAC,OAAM;YAAC;QAAC;QAAC,OAAM;YAAC,GAAG,CAAC;YAAC,OAAM;mBAAI,EAAE,KAAK;gBAAC;aAAE;QAAA;IAAC;IAAE,CAAC,EAAE,EAAC,CAAC,EAAC,CAAC;QAAE,IAAI,IAAE,EAAE,EAAE,EAAC,IAAE,EAAE,KAAK,CAAC,OAAO,CAAC;QAAG,IAAG,MAAI,CAAC,GAAE,OAAO;QAAE,IAAI,IAAE,EAAE,KAAK,CAAC,KAAK;QAAG,OAAO,EAAE,MAAM,CAAC,GAAE,IAAG;YAAC,GAAG,CAAC;YAAC,OAAM;QAAC;IAAC;AAAC;AAAE,MAAM,UAAU,wJAAA,CAAA,UAAC;IAAC,aAAa;QAAC,KAAK,IAAI;QAAW,EAAE,IAAI,EAAC,WAAU;YAAC,MAAK,CAAA,IAAG,IAAI,CAAC,IAAI,CAAC;oBAAC,MAAK;oBAAE,IAAG;gBAAC;YAAG,KAAI,CAAA,IAAG,IAAI,CAAC,IAAI,CAAC;oBAAC,MAAK;oBAAE,IAAG;gBAAC;QAAE;QAAG,EAAE,IAAI,EAAC,aAAY;YAAC,OAAM,CAAC,GAAE,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,MAAM,GAAC,EAAE,KAAG;YAAE,SAAQ,CAAC,GAAE,IAAI,EAAE,KAAK,CAAC,QAAQ,CAAC;QAAE;IAAE;IAAC,OAAO,MAAK;QAAC,OAAO,IAAI,EAAE;YAAC,OAAM,EAAE;QAAA;IAAE;IAAC,OAAO,CAAC,EAAC,CAAC,EAAC;QAAC,OAAO,CAAA,GAAA,+JAAA,CAAA,QAAC,AAAD,EAAE,EAAE,IAAI,EAAC,GAAE,GAAE;IAAE;AAAC;AAAC,MAAM,IAAE,IAAI,wKAAA,CAAA,aAAC,CAAC,IAAI,EAAE,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 409, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/hooks/use-event.js"], "sourcesContent": ["import a from\"react\";import{useLatestValue as n}from'./use-latest-value.js';let o=function(t){let e=n(t);return a.useCallback((...r)=>e.current(...r),[e])};export{o as useEvent};\n"], "names": [], "mappings": ";;;AAAA;AAAqB;;;AAAuD,IAAI,IAAE,SAAS,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,gLAAA,CAAA,iBAAC,AAAD,EAAE;IAAG,OAAO,qMAAA,CAAA,UAAC,CAAC,WAAW,CAAC,CAAC,GAAG,IAAI,EAAE,OAAO,IAAI,IAAG;QAAC;KAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 427, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/react-glue.js"], "sourcesContent": ["import{useSyncExternalStoreWithSelector as a}from\"use-sync-external-store/with-selector\";import{useEvent as t}from'./hooks/use-event.js';import{shallowEqual as o}from'./machine.js';function S(e,n,r=o){return a(t(i=>e.subscribe(s,i)),t(()=>e.state),t(()=>e.state),t(n),r)}function s(e){return e}export{S as useSlice};\n"], "names": [], "mappings": ";;;AAAA;AAAyF;AAAgD;;;;AAA4C,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,IAAE,wJAAA,CAAA,eAAC;IAAE,OAAO,CAAA,GAAA,oKAAA,CAAA,mCAAC,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE,CAAA,IAAG,EAAE,SAAS,CAAC,GAAE,KAAI,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE,IAAI,EAAE,KAAK,GAAE,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE,IAAI,EAAE,KAAK,GAAE,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE,IAAG;AAAE;AAAC,SAAS,EAAE,CAAC;IAAE,OAAO;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 447, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/hooks/use-is-top-layer.js"], "sourcesContent": ["import{useCallback as n,useId as u}from\"react\";import{stackMachines as p}from'../machines/stack-machine.js';import{useSlice as f}from'../react-glue.js';import{useIsoMorphicEffect as a}from'./use-iso-morphic-effect.js';function I(o,s){let t=u(),r=p.get(s),[i,c]=f(r,n(e=>[r.selectors.isTop(e,t),r.selectors.inStack(e,t)],[r,t]));return a(()=>{if(o)return r.actions.push(t),()=>r.actions.pop(t)},[r,o,t]),o?c?i:!0:!1}export{I as useIsTopLayer};\n"], "names": [], "mappings": ";;;AAAA;AAA+C;AAA6D;AAA4C;;;;;AAAkE,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,QAAC,AAAD,KAAI,IAAE,6KAAA,CAAA,gBAAC,CAAC,GAAG,CAAC,IAAG,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,8JAAA,CAAA,WAAC,AAAD,EAAE,GAAE,CAAA,GAAA,qMAAA,CAAA,cAAC,AAAD,EAAE,CAAA,IAAG;YAAC,EAAE,SAAS,CAAC,KAAK,CAAC,GAAE;YAAG,EAAE,SAAS,CAAC,OAAO,CAAC,GAAE;SAAG,EAAC;QAAC;QAAE;KAAE;IAAG,OAAO,CAAA,GAAA,yLAAA,CAAA,sBAAC,AAAD,EAAE;QAAK,IAAG,GAAE,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,IAAG,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC;IAAE,GAAE;QAAC;QAAE;QAAE;KAAE,GAAE,IAAE,IAAE,IAAE,CAAC,IAAE,CAAC;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 479, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/hooks/use-escape.js"], "sourcesContent": ["import{Keys as u}from'../components/keyboard.js';import{useEventListener as i}from'./use-event-listener.js';import{useIsTopLayer as f}from'./use-is-top-layer.js';function a(o,r=typeof document!=\"undefined\"?document.defaultView:null,t){let n=f(o,\"escape\");i(r,\"keydown\",e=>{n&&(e.defaultPrevented||e.key===u.Escape&&t(e))})}export{a as useEscape};\n"], "names": [], "mappings": ";;;AAAA;AAAiD;AAA2D;;;;AAAsD,SAAS,EAAE,CAAC,EAAC,IAAE,OAAO,YAAU,cAAY,SAAS,WAAW,GAAC,IAAI,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,mLAAA,CAAA,gBAAC,AAAD,EAAE,GAAE;IAAU,CAAA,GAAA,kLAAA,CAAA,mBAAC,AAAD,EAAE,GAAE,WAAU,CAAA;QAAI,KAAG,CAAC,EAAE,gBAAgB,IAAE,EAAE,GAAG,KAAG,uKAAA,CAAA,OAAC,CAAC,MAAM,IAAE,EAAE,EAAE;IAAC;AAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 499, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/utils/owner.js"], "sourcesContent": ["import{env as t}from'./env.js';function o(n){var e,r;return t.isServer?null:n?\"ownerDocument\"in n?n.ownerDocument:\"current\"in n?(r=(e=n.current)==null?void 0:e.ownerDocument)!=null?r:document:null:document}export{o as getOwnerDocument};\n"], "names": [], "mappings": ";;;AAAA;;AAA+B,SAAS,EAAE,CAAC;IAAE,IAAI,GAAE;IAAE,OAAO,6JAAA,CAAA,MAAC,CAAC,QAAQ,GAAC,OAAK,IAAE,mBAAkB,IAAE,EAAE,aAAa,GAAC,aAAY,IAAE,CAAC,IAAE,CAAC,IAAE,EAAE,OAAO,KAAG,OAAK,KAAK,IAAE,EAAE,aAAa,KAAG,OAAK,IAAE,WAAS,OAAK;AAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 513, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/hooks/use-inert-others.js"], "sourcesContent": ["import{disposables as M}from'../utils/disposables.js';import{getOwnerDocument as b}from'../utils/owner.js';import{useIsTopLayer as L}from'./use-is-top-layer.js';import{useIsoMorphicEffect as T}from'./use-iso-morphic-effect.js';let f=new Map,u=new Map;function h(t){var e;let r=(e=u.get(t))!=null?e:0;return u.set(t,r+1),r!==0?()=>m(t):(f.set(t,{\"aria-hidden\":t.getAttribute(\"aria-hidden\"),inert:t.inert}),t.setAttribute(\"aria-hidden\",\"true\"),t.inert=!0,()=>m(t))}function m(t){var i;let r=(i=u.get(t))!=null?i:1;if(r===1?u.delete(t):u.set(t,r-1),r!==1)return;let e=f.get(t);e&&(e[\"aria-hidden\"]===null?t.removeAttribute(\"aria-hidden\"):t.setAttribute(\"aria-hidden\",e[\"aria-hidden\"]),t.inert=e.inert,f.delete(t))}function y(t,{allowed:r,disallowed:e}={}){let i=L(t,\"inert-others\");T(()=>{var d,c;if(!i)return;let a=M();for(let n of(d=e==null?void 0:e())!=null?d:[])n&&a.add(h(n));let s=(c=r==null?void 0:r())!=null?c:[];for(let n of s){if(!n)continue;let l=b(n);if(!l)continue;let o=n.parentElement;for(;o&&o!==l.body;){for(let p of o.children)s.some(E=>p.contains(E))||a.add(h(p));o=o.parentElement}}return a.dispose},[i,r,e])}export{y as useInertOthers};\n"], "names": [], "mappings": ";;;AAAA;AAAsD;AAAqD;AAAsD;;;;;AAAkE,IAAI,IAAE,IAAI,KAAI,IAAE,IAAI;AAAI,SAAS,EAAE,CAAC;IAAE,IAAI;IAAE,IAAI,IAAE,CAAC,IAAE,EAAE,GAAG,CAAC,EAAE,KAAG,OAAK,IAAE;IAAE,OAAO,EAAE,GAAG,CAAC,GAAE,IAAE,IAAG,MAAI,IAAE,IAAI,EAAE,KAAG,CAAC,EAAE,GAAG,CAAC,GAAE;QAAC,eAAc,EAAE,YAAY,CAAC;QAAe,OAAM,EAAE,KAAK;IAAA,IAAG,EAAE,YAAY,CAAC,eAAc,SAAQ,EAAE,KAAK,GAAC,CAAC,GAAE,IAAI,EAAE,EAAE;AAAC;AAAC,SAAS,EAAE,CAAC;IAAE,IAAI;IAAE,IAAI,IAAE,CAAC,IAAE,EAAE,GAAG,CAAC,EAAE,KAAG,OAAK,IAAE;IAAE,IAAG,MAAI,IAAE,EAAE,MAAM,CAAC,KAAG,EAAE,GAAG,CAAC,GAAE,IAAE,IAAG,MAAI,GAAE;IAAO,IAAI,IAAE,EAAE,GAAG,CAAC;IAAG,KAAG,CAAC,CAAC,CAAC,cAAc,KAAG,OAAK,EAAE,eAAe,CAAC,iBAAe,EAAE,YAAY,CAAC,eAAc,CAAC,CAAC,cAAc,GAAE,EAAE,KAAK,GAAC,EAAE,KAAK,EAAC,EAAE,MAAM,CAAC,EAAE;AAAC;AAAC,SAAS,EAAE,CAAC,EAAC,EAAC,SAAQ,CAAC,EAAC,YAAW,CAAC,EAAC,GAAC,CAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,mLAAA,CAAA,gBAAC,AAAD,EAAE,GAAE;IAAgB,CAAA,GAAA,yLAAA,CAAA,sBAAC,AAAD,EAAE;QAAK,IAAI,GAAE;QAAE,IAAG,CAAC,GAAE;QAAO,IAAI,IAAE,CAAA,GAAA,qKAAA,CAAA,cAAC,AAAD;QAAI,KAAI,IAAI,KAAI,CAAC,IAAE,KAAG,OAAK,KAAK,IAAE,GAAG,KAAG,OAAK,IAAE,EAAE,CAAC,KAAG,EAAE,GAAG,CAAC,EAAE;QAAI,IAAI,IAAE,CAAC,IAAE,KAAG,OAAK,KAAK,IAAE,GAAG,KAAG,OAAK,IAAE,EAAE;QAAC,KAAI,IAAI,KAAK,EAAE;YAAC,IAAG,CAAC,GAAE;YAAS,IAAI,IAAE,CAAA,GAAA,+JAAA,CAAA,mBAAC,AAAD,EAAE;YAAG,IAAG,CAAC,GAAE;YAAS,IAAI,IAAE,EAAE,aAAa;YAAC,MAAK,KAAG,MAAI,EAAE,IAAI,EAAE;gBAAC,KAAI,IAAI,KAAK,EAAE,QAAQ,CAAC,EAAE,IAAI,CAAC,CAAA,IAAG,EAAE,QAAQ,CAAC,OAAK,EAAE,GAAG,CAAC,EAAE;gBAAI,IAAE,EAAE,aAAa;YAAA;QAAC;QAAC,OAAO,EAAE,OAAO;IAAA,GAAE;QAAC;QAAE;QAAE;KAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 570, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/hooks/use-is-touch-device.js"], "sourcesContent": ["import{useState as i}from\"react\";import{useIsoMorphicEffect as s}from'./use-iso-morphic-effect.js';function f(){var t;let[e]=i(()=>typeof window!=\"undefined\"&&typeof window.matchMedia==\"function\"?window.matchMedia(\"(pointer: coarse)\"):null),[o,c]=i((t=e==null?void 0:e.matches)!=null?t:!1);return s(()=>{if(!e)return;function n(r){c(r.matches)}return e.addEventListener(\"change\",n),()=>e.removeEventListener(\"change\",n)},[e]),o}export{f as useIsTouchDevice};\n"], "names": [], "mappings": ";;;AAAA;AAAiC;;;AAAkE,SAAS;IAAI,IAAI;IAAE,IAAG,CAAC,EAAE,GAAC,CAAA,GAAA,qMAAA,CAAA,WAAC,AAAD,EAAE,IAAI,sCAAiE,0BAAuC,OAAM,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,qMAAA,CAAA,WAAC,AAAD,EAAE,CAAC,IAAE,KAAG,OAAK,KAAK,IAAE,EAAE,OAAO,KAAG,OAAK,IAAE,CAAC;IAAG,OAAO,CAAA,GAAA,yLAAA,CAAA,sBAAC,AAAD,EAAE;QAAK,IAAG,CAAC,GAAE;QAAO,SAAS,EAAE,CAAC;YAAE,EAAE,EAAE,OAAO;QAAC;QAAC,OAAO,EAAE,gBAAgB,CAAC,UAAS,IAAG,IAAI,EAAE,mBAAmB,CAAC,UAAS;IAAE,GAAE;QAAC;KAAE,GAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 595, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/utils/dom.js"], "sourcesContent": ["function o(e){return typeof e!=\"object\"||e===null?!1:\"nodeType\"in e}function t(e){return o(e)&&\"tagName\"in e}function n(e){return t(e)&&\"accessKey\"in e}function i(e){return t(e)&&\"tabIndex\"in e}function r(e){return t(e)&&\"style\"in e}function u(e){return n(e)&&e.nodeName===\"IFRAME\"}function l(e){return n(e)&&e.nodeName===\"INPUT\"}function s(e){return n(e)&&e.nodeName===\"TEXTAREA\"}function m(e){return n(e)&&e.nodeName===\"LABEL\"}function a(e){return n(e)&&e.nodeName===\"FIELDSET\"}function E(e){return n(e)&&e.nodeName===\"LEGEND\"}function L(e){return t(e)?e.matches('a[href],audio[controls],button,details,embed,iframe,img[usemap],input:not([type=\"hidden\"]),label,select,textarea,video[controls]'):!1}export{r as hasInlineStyle,t as isElement,n as isHTMLElement,a as isHTMLFieldSetElement,u as isHTMLIframeElement,l as isHTMLInputElement,m as isHTMLLabelElement,E as isHTMLLegendElement,s as isHTMLTextAreaElement,i as isHTMLorSVGElement,L as isInteractiveElement,o as isNode};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA,SAAS,EAAE,CAAC;IAAE,OAAO,OAAO,KAAG,YAAU,MAAI,OAAK,CAAC,IAAE,cAAa;AAAC;AAAC,SAAS,EAAE,CAAC;IAAE,OAAO,EAAE,MAAI,aAAY;AAAC;AAAC,SAAS,EAAE,CAAC;IAAE,OAAO,EAAE,MAAI,eAAc;AAAC;AAAC,SAAS,EAAE,CAAC;IAAE,OAAO,EAAE,MAAI,cAAa;AAAC;AAAC,SAAS,EAAE,CAAC;IAAE,OAAO,EAAE,MAAI,WAAU;AAAC;AAAC,SAAS,EAAE,CAAC;IAAE,OAAO,EAAE,MAAI,EAAE,QAAQ,KAAG;AAAQ;AAAC,SAAS,EAAE,CAAC;IAAE,OAAO,EAAE,MAAI,EAAE,QAAQ,KAAG;AAAO;AAAC,SAAS,EAAE,CAAC;IAAE,OAAO,EAAE,MAAI,EAAE,QAAQ,KAAG;AAAU;AAAC,SAAS,EAAE,CAAC;IAAE,OAAO,EAAE,MAAI,EAAE,QAAQ,KAAG;AAAO;AAAC,SAAS,EAAE,CAAC;IAAE,OAAO,EAAE,MAAI,EAAE,QAAQ,KAAG;AAAU;AAAC,SAAS,EAAE,CAAC;IAAE,OAAO,EAAE,MAAI,EAAE,QAAQ,KAAG;AAAQ;AAAC,SAAS,EAAE,CAAC;IAAE,OAAO,EAAE,KAAG,EAAE,OAAO,CAAC,sIAAoI,CAAC;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 650, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/hooks/use-on-disappear.js"], "sourcesContent": ["import{useEffect as l}from\"react\";import{disposables as u}from'../utils/disposables.js';import*as c from'../utils/dom.js';import{useLatestValue as d}from'./use-latest-value.js';function p(s,n,o){let i=d(t=>{let e=t.getBoundingClientRect();e.x===0&&e.y===0&&e.width===0&&e.height===0&&o()});l(()=>{if(!s)return;let t=n===null?null:c.isHTMLElement(n)?n:n.current;if(!t)return;let e=u();if(typeof ResizeObserver!=\"undefined\"){let r=new ResizeObserver(()=>i.current(t));r.observe(t),e.add(()=>r.disconnect())}if(typeof IntersectionObserver!=\"undefined\"){let r=new IntersectionObserver(()=>i.current(t));r.observe(t),e.add(()=>r.disconnect())}return()=>e.dispose()},[n,i,s])}export{p as useOnDisappear};\n"], "names": [], "mappings": ";;;AAAA;AAAkC;AAAsD;AAAkC;;;;;AAAuD,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,gLAAA,CAAA,iBAAC,AAAD,EAAE,CAAA;QAAI,IAAI,IAAE,EAAE,qBAAqB;QAAG,EAAE,CAAC,KAAG,KAAG,EAAE,CAAC,KAAG,KAAG,EAAE,KAAK,KAAG,KAAG,EAAE,MAAM,KAAG,KAAG;IAAG;IAAG,CAAA,GAAA,qMAAA,CAAA,YAAC,AAAD,EAAE;QAAK,IAAG,CAAC,GAAE;QAAO,IAAI,IAAE,MAAI,OAAK,OAAK,6JAAA,CAAA,gBAAe,CAAC,KAAG,IAAE,EAAE,OAAO;QAAC,IAAG,CAAC,GAAE;QAAO,IAAI,IAAE,CAAA,GAAA,qKAAA,CAAA,cAAC,AAAD;QAAI,IAAG,OAAO,kBAAgB,aAAY;YAAC,IAAI,IAAE,IAAI,eAAe,IAAI,EAAE,OAAO,CAAC;YAAI,EAAE,OAAO,CAAC,IAAG,EAAE,GAAG,CAAC,IAAI,EAAE,UAAU;QAAG;QAAC,IAAG,OAAO,wBAAsB,aAAY;YAAC,IAAI,IAAE,IAAI,qBAAqB,IAAI,EAAE,OAAO,CAAC;YAAI,EAAE,OAAO,CAAC,IAAG,EAAE,GAAG,CAAC,IAAI,EAAE,UAAU;QAAG;QAAC,OAAM,IAAI,EAAE,OAAO;IAAE,GAAE;QAAC;QAAE;QAAE;KAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 691, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/utils/focus-management.js"], "sourcesContent": ["import{disposables as N}from'./disposables.js';import*as p from'./dom.js';import{match as L}from'./match.js';import{getOwnerDocument as E}from'./owner.js';let f=[\"[contentEditable=true]\",\"[tabindex]\",\"a[href]\",\"area[href]\",\"button:not([disabled])\",\"iframe\",\"input:not([disabled])\",\"select:not([disabled])\",\"textarea:not([disabled])\"].map(e=>`${e}:not([tabindex='-1'])`).join(\",\"),F=[\"[data-autofocus]\"].map(e=>`${e}:not([tabindex='-1'])`).join(\",\");var T=(n=>(n[n.First=1]=\"First\",n[n.Previous=2]=\"Previous\",n[n.Next=4]=\"Next\",n[n.Last=8]=\"Last\",n[n.WrapAround=16]=\"WrapAround\",n[n.NoScroll=32]=\"NoScroll\",n[n.AutoFocus=64]=\"AutoFocus\",n))(T||{}),y=(o=>(o[o.Error=0]=\"Error\",o[o.Overflow=1]=\"Overflow\",o[o.Success=2]=\"Success\",o[o.Underflow=3]=\"Underflow\",o))(y||{}),S=(t=>(t[t.Previous=-1]=\"Previous\",t[t.Next=1]=\"Next\",t))(S||{});function b(e=document.body){return e==null?[]:Array.from(e.querySelectorAll(f)).sort((r,t)=>Math.sign((r.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}function O(e=document.body){return e==null?[]:Array.from(e.querySelectorAll(F)).sort((r,t)=>Math.sign((r.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}var h=(t=>(t[t.Strict=0]=\"Strict\",t[t.Loose=1]=\"Loose\",t))(h||{});function A(e,r=0){var t;return e===((t=E(e))==null?void 0:t.body)?!1:L(r,{[0](){return e.matches(f)},[1](){let l=e;for(;l!==null;){if(l.matches(f))return!0;l=l.parentElement}return!1}})}function V(e){let r=E(e);N().nextFrame(()=>{r&&p.isHTMLorSVGElement(r.activeElement)&&!A(r.activeElement,0)&&I(e)})}var H=(t=>(t[t.Keyboard=0]=\"Keyboard\",t[t.Mouse=1]=\"Mouse\",t))(H||{});typeof window!=\"undefined\"&&typeof document!=\"undefined\"&&(document.addEventListener(\"keydown\",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible=\"\")},!0),document.addEventListener(\"click\",e=>{e.detail===1?delete document.documentElement.dataset.headlessuiFocusVisible:e.detail===0&&(document.documentElement.dataset.headlessuiFocusVisible=\"\")},!0));function I(e){e==null||e.focus({preventScroll:!0})}let w=[\"textarea\",\"input\"].join(\",\");function _(e){var r,t;return(t=(r=e==null?void 0:e.matches)==null?void 0:r.call(e,w))!=null?t:!1}function P(e,r=t=>t){return e.slice().sort((t,l)=>{let o=r(t),c=r(l);if(o===null||c===null)return 0;let u=o.compareDocumentPosition(c);return u&Node.DOCUMENT_POSITION_FOLLOWING?-1:u&Node.DOCUMENT_POSITION_PRECEDING?1:0})}function j(e,r){return g(b(),r,{relativeTo:e})}function g(e,r,{sorted:t=!0,relativeTo:l=null,skipElements:o=[]}={}){let c=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,u=Array.isArray(e)?t?P(e):e:r&64?O(e):b(e);o.length>0&&u.length>1&&(u=u.filter(s=>!o.some(a=>a!=null&&\"current\"in a?(a==null?void 0:a.current)===s:a===s))),l=l!=null?l:c.activeElement;let n=(()=>{if(r&5)return 1;if(r&10)return-1;throw new Error(\"Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last\")})(),x=(()=>{if(r&1)return 0;if(r&2)return Math.max(0,u.indexOf(l))-1;if(r&4)return Math.max(0,u.indexOf(l))+1;if(r&8)return u.length-1;throw new Error(\"Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last\")})(),M=r&32?{preventScroll:!0}:{},m=0,d=u.length,i;do{if(m>=d||m+d<=0)return 0;let s=x+m;if(r&16)s=(s+d)%d;else{if(s<0)return 3;if(s>=d)return 1}i=u[s],i==null||i.focus(M),m+=n}while(i!==c.activeElement);return r&6&&_(i)&&i.select(),2}export{T as Focus,y as FocusResult,h as FocusableMode,I as focusElement,j as focusFrom,g as focusIn,f as focusableSelector,O as getAutoFocusableElements,b as getFocusableElements,A as isFocusableElement,V as restoreFocusIfNecessary,P as sortByDomNode};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AAA+C;AAA2B;AAAmC;;;;;AAA8C,IAAI,IAAE;IAAC;IAAyB;IAAa;IAAU;IAAa;IAAyB;IAAS;IAAwB;IAAyB;CAA2B,CAAC,GAAG,CAAC,CAAA,IAAG,GAAG,EAAE,qBAAqB,CAAC,EAAE,IAAI,CAAC,MAAK,IAAE;IAAC;CAAmB,CAAC,GAAG,CAAC,CAAA,IAAG,GAAG,EAAE,qBAAqB,CAAC,EAAE,IAAI,CAAC;AAAK,IAAI,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,KAAK,GAAC,EAAE,GAAC,SAAQ,CAAC,CAAC,EAAE,QAAQ,GAAC,EAAE,GAAC,YAAW,CAAC,CAAC,EAAE,IAAI,GAAC,EAAE,GAAC,QAAO,CAAC,CAAC,EAAE,IAAI,GAAC,EAAE,GAAC,QAAO,CAAC,CAAC,EAAE,UAAU,GAAC,GAAG,GAAC,cAAa,CAAC,CAAC,EAAE,QAAQ,GAAC,GAAG,GAAC,YAAW,CAAC,CAAC,EAAE,SAAS,GAAC,GAAG,GAAC,aAAY,CAAC,CAAC,EAAE,KAAG,CAAC,IAAG,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,KAAK,GAAC,EAAE,GAAC,SAAQ,CAAC,CAAC,EAAE,QAAQ,GAAC,EAAE,GAAC,YAAW,CAAC,CAAC,EAAE,OAAO,GAAC,EAAE,GAAC,WAAU,CAAC,CAAC,EAAE,SAAS,GAAC,EAAE,GAAC,aAAY,CAAC,CAAC,EAAE,KAAG,CAAC,IAAG,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,QAAQ,GAAC,CAAC,EAAE,GAAC,YAAW,CAAC,CAAC,EAAE,IAAI,GAAC,EAAE,GAAC,QAAO,CAAC,CAAC,EAAE,KAAG,CAAC;AAAG,SAAS,EAAE,IAAE,SAAS,IAAI;IAAE,OAAO,KAAG,OAAK,EAAE,GAAC,MAAM,IAAI,CAAC,EAAE,gBAAgB,CAAC,IAAI,IAAI,CAAC,CAAC,GAAE,IAAI,KAAK,IAAI,CAAC,CAAC,EAAE,QAAQ,IAAE,OAAO,gBAAgB,IAAE,CAAC,EAAE,QAAQ,IAAE,OAAO,gBAAgB;AAAG;AAAC,SAAS,EAAE,IAAE,SAAS,IAAI;IAAE,OAAO,KAAG,OAAK,EAAE,GAAC,MAAM,IAAI,CAAC,EAAE,gBAAgB,CAAC,IAAI,IAAI,CAAC,CAAC,GAAE,IAAI,KAAK,IAAI,CAAC,CAAC,EAAE,QAAQ,IAAE,OAAO,gBAAgB,IAAE,CAAC,EAAE,QAAQ,IAAE,OAAO,gBAAgB;AAAG;AAAC,IAAI,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE,GAAC,UAAS,CAAC,CAAC,EAAE,KAAK,GAAC,EAAE,GAAC,SAAQ,CAAC,CAAC,EAAE,KAAG,CAAC;AAAG,SAAS,EAAE,CAAC,EAAC,IAAE,CAAC;IAAE,IAAI;IAAE,OAAO,MAAI,CAAC,CAAC,IAAE,CAAA,GAAA,+JAAA,CAAA,mBAAC,AAAD,EAAE,EAAE,KAAG,OAAK,KAAK,IAAE,EAAE,IAAI,IAAE,CAAC,IAAE,CAAA,GAAA,+JAAA,CAAA,QAAC,AAAD,EAAE,GAAE;QAAC,CAAC,EAAE;YAAG,OAAO,EAAE,OAAO,CAAC;QAAE;QAAE,CAAC,EAAE;YAAG,IAAI,IAAE;YAAE,MAAK,MAAI,MAAM;gBAAC,IAAG,EAAE,OAAO,CAAC,IAAG,OAAM,CAAC;gBAAE,IAAE,EAAE,aAAa;YAAA;YAAC,OAAM,CAAC;QAAC;IAAC;AAAE;AAAC,SAAS,EAAE,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,+JAAA,CAAA,mBAAC,AAAD,EAAE;IAAG,CAAA,GAAA,qKAAA,CAAA,cAAC,AAAD,IAAI,SAAS,CAAC;QAAK,KAAG,6JAAA,CAAA,qBAAoB,CAAC,EAAE,aAAa,KAAG,CAAC,EAAE,EAAE,aAAa,EAAC,MAAI,EAAE;IAAE;AAAE;AAAC,IAAI,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,QAAQ,GAAC,EAAE,GAAC,YAAW,CAAC,CAAC,EAAE,KAAK,GAAC,EAAE,GAAC,SAAQ,CAAC,CAAC,EAAE,KAAG,CAAC;AAAG,eAAe,eAAa,OAAO,YAAU,eAAa,CAAC,SAAS,gBAAgB,CAAC,WAAU,CAAA;IAAI,EAAE,OAAO,IAAE,EAAE,MAAM,IAAE,EAAE,OAAO,IAAE,CAAC,SAAS,eAAe,CAAC,OAAO,CAAC,sBAAsB,GAAC,EAAE;AAAC,GAAE,CAAC,IAAG,SAAS,gBAAgB,CAAC,SAAQ,CAAA;IAAI,EAAE,MAAM,KAAG,IAAE,OAAO,SAAS,eAAe,CAAC,OAAO,CAAC,sBAAsB,GAAC,EAAE,MAAM,KAAG,KAAG,CAAC,SAAS,eAAe,CAAC,OAAO,CAAC,sBAAsB,GAAC,EAAE;AAAC,GAAE,CAAC,EAAE;AAAE,SAAS,EAAE,CAAC;IAAE,KAAG,QAAM,EAAE,KAAK,CAAC;QAAC,eAAc,CAAC;IAAC;AAAE;AAAC,IAAI,IAAE;IAAC;IAAW;CAAQ,CAAC,IAAI,CAAC;AAAK,SAAS,EAAE,CAAC;IAAE,IAAI,GAAE;IAAE,OAAM,CAAC,IAAE,CAAC,IAAE,KAAG,OAAK,KAAK,IAAE,EAAE,OAAO,KAAG,OAAK,KAAK,IAAE,EAAE,IAAI,CAAC,GAAE,EAAE,KAAG,OAAK,IAAE,CAAC;AAAC;AAAC,SAAS,EAAE,CAAC,EAAC,IAAE,CAAA,IAAG,CAAC;IAAE,OAAO,EAAE,KAAK,GAAG,IAAI,CAAC,CAAC,GAAE;QAAK,IAAI,IAAE,EAAE,IAAG,IAAE,EAAE;QAAG,IAAG,MAAI,QAAM,MAAI,MAAK,OAAO;QAAE,IAAI,IAAE,EAAE,uBAAuB,CAAC;QAAG,OAAO,IAAE,KAAK,2BAA2B,GAAC,CAAC,IAAE,IAAE,KAAK,2BAA2B,GAAC,IAAE;IAAC;AAAE;AAAC,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,OAAO,EAAE,KAAI,GAAE;QAAC,YAAW;IAAC;AAAE;AAAC,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,EAAC,QAAO,IAAE,CAAC,CAAC,EAAC,YAAW,IAAE,IAAI,EAAC,cAAa,IAAE,EAAE,EAAC,GAAC,CAAC,CAAC;IAAE,IAAI,IAAE,MAAM,OAAO,CAAC,KAAG,EAAE,MAAM,GAAC,IAAE,CAAC,CAAC,EAAE,CAAC,aAAa,GAAC,WAAS,EAAE,aAAa,EAAC,IAAE,MAAM,OAAO,CAAC,KAAG,IAAE,EAAE,KAAG,IAAE,IAAE,KAAG,EAAE,KAAG,EAAE;IAAG,EAAE,MAAM,GAAC,KAAG,EAAE,MAAM,GAAC,KAAG,CAAC,IAAE,EAAE,MAAM,CAAC,CAAA,IAAG,CAAC,EAAE,IAAI,CAAC,CAAA,IAAG,KAAG,QAAM,aAAY,IAAE,CAAC,KAAG,OAAK,KAAK,IAAE,EAAE,OAAO,MAAI,IAAE,MAAI,GAAG,GAAE,IAAE,KAAG,OAAK,IAAE,EAAE,aAAa;IAAC,IAAI,IAAE,CAAC;QAAK,IAAG,IAAE,GAAE,OAAO;QAAE,IAAG,IAAE,IAAG,OAAM,CAAC;QAAE,MAAM,IAAI,MAAM;IAAgE,CAAC,KAAI,IAAE,CAAC;QAAK,IAAG,IAAE,GAAE,OAAO;QAAE,IAAG,IAAE,GAAE,OAAO,KAAK,GAAG,CAAC,GAAE,EAAE,OAAO,CAAC,MAAI;QAAE,IAAG,IAAE,GAAE,OAAO,KAAK,GAAG,CAAC,GAAE,EAAE,OAAO,CAAC,MAAI;QAAE,IAAG,IAAE,GAAE,OAAO,EAAE,MAAM,GAAC;QAAE,MAAM,IAAI,MAAM;IAAgE,CAAC,KAAI,IAAE,IAAE,KAAG;QAAC,eAAc,CAAC;IAAC,IAAE,CAAC,GAAE,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC;IAAE,GAAE;QAAC,IAAG,KAAG,KAAG,IAAE,KAAG,GAAE,OAAO;QAAE,IAAI,IAAE,IAAE;QAAE,IAAG,IAAE,IAAG,IAAE,CAAC,IAAE,CAAC,IAAE;aAAM;YAAC,IAAG,IAAE,GAAE,OAAO;YAAE,IAAG,KAAG,GAAE,OAAO;QAAC;QAAC,IAAE,CAAC,CAAC,EAAE,EAAC,KAAG,QAAM,EAAE,KAAK,CAAC,IAAG,KAAG;IAAC,QAAO,MAAI,EAAE,aAAa,CAAE;IAAA,OAAO,IAAE,KAAG,EAAE,MAAI,EAAE,MAAM,IAAG;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 821, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/utils/platform.js"], "sourcesContent": ["function t(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function i(){return/Android/gi.test(window.navigator.userAgent)}function n(){return t()||i()}export{i as isAndroid,t as isIOS,n as isMobile};\n"], "names": [], "mappings": ";;;;;AAAA,SAAS;IAAI,OAAM,WAAW,IAAI,CAAC,OAAO,SAAS,CAAC,QAAQ,KAAG,QAAQ,IAAI,CAAC,OAAO,SAAS,CAAC,QAAQ,KAAG,OAAO,SAAS,CAAC,cAAc,GAAC;AAAC;AAAC,SAAS;IAAI,OAAM,YAAY,IAAI,CAAC,OAAO,SAAS,CAAC,SAAS;AAAC;AAAC,SAAS;IAAI,OAAO,OAAK;AAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 840, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/hooks/use-document-event.js"], "sourcesContent": ["import{useEffect as c}from\"react\";import{useLatestValue as a}from'./use-latest-value.js';function i(t,e,o,n){let u=a(o);c(()=>{if(!t)return;function r(m){u.current(m)}return document.addEventListener(e,r,n),()=>document.removeEventListener(e,r,n)},[t,e,n])}export{i as useDocumentEvent};\n"], "names": [], "mappings": ";;;AAAA;AAAkC;;;AAAuD,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,gLAAA,CAAA,iBAAC,AAAD,EAAE;IAAG,CAAA,GAAA,qMAAA,CAAA,YAAC,AAAD,EAAE;QAAK,IAAG,CAAC,GAAE;QAAO,SAAS,EAAE,CAAC;YAAE,EAAE,OAAO,CAAC;QAAE;QAAC,OAAO,SAAS,gBAAgB,CAAC,GAAE,GAAE,IAAG,IAAI,SAAS,mBAAmB,CAAC,GAAE,GAAE;IAAE,GAAE;QAAC;QAAE;QAAE;KAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 866, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/hooks/use-window-event.js"], "sourcesContent": ["import{useEffect as a}from\"react\";import{useLatestValue as f}from'./use-latest-value.js';function s(t,e,o,n){let i=f(o);a(()=>{if(!t)return;function r(d){i.current(d)}return window.addEventListener(e,r,n),()=>window.removeEventListener(e,r,n)},[t,e,n])}export{s as useWindowEvent};\n"], "names": [], "mappings": ";;;AAAA;AAAkC;;;AAAuD,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,gLAAA,CAAA,iBAAC,AAAD,EAAE;IAAG,CAAA,GAAA,qMAAA,CAAA,YAAC,AAAD,EAAE;QAAK,IAAG,CAAC,GAAE;QAAO,SAAS,EAAE,CAAC;YAAE,EAAE,OAAO,CAAC;QAAE;QAAC,OAAO,OAAO,gBAAgB,CAAC,GAAE,GAAE,IAAG,IAAI,OAAO,mBAAmB,CAAC,GAAE,GAAE;IAAE,GAAE;QAAC;QAAE;QAAE;KAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 892, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/hooks/use-outside-click.js"], "sourcesContent": ["import{useCallback as T,useRef as E}from\"react\";import*as d from'../utils/dom.js';import{FocusableMode as g,isFocusableElement as y}from'../utils/focus-management.js';import{isMobile as p}from'../utils/platform.js';import{useDocumentEvent as a}from'./use-document-event.js';import{useLatestValue as L}from'./use-latest-value.js';import{useWindowEvent as x}from'./use-window-event.js';const C=30;function k(o,f,h){let m=L(h),s=T(function(e,c){if(e.defaultPrevented)return;let r=c(e);if(r===null||!r.getRootNode().contains(r)||!r.isConnected)return;let M=function u(n){return typeof n==\"function\"?u(n()):Array.isArray(n)||n instanceof Set?n:[n]}(f);for(let u of M)if(u!==null&&(u.contains(r)||e.composed&&e.composedPath().includes(u)))return;return!y(r,g.Loose)&&r.tabIndex!==-1&&e.preventDefault(),m.current(e,r)},[m,f]),i=E(null);a(o,\"pointerdown\",t=>{var e,c;p()||(i.current=((c=(e=t.composedPath)==null?void 0:e.call(t))==null?void 0:c[0])||t.target)},!0),a(o,\"pointerup\",t=>{if(p()||!i.current)return;let e=i.current;return i.current=null,s(t,()=>e)},!0);let l=E({x:0,y:0});a(o,\"touchstart\",t=>{l.current.x=t.touches[0].clientX,l.current.y=t.touches[0].clientY},!0),a(o,\"touchend\",t=>{let e={x:t.changedTouches[0].clientX,y:t.changedTouches[0].clientY};if(!(Math.abs(e.x-l.current.x)>=C||Math.abs(e.y-l.current.y)>=C))return s(t,()=>d.isHTMLorSVGElement(t.target)?t.target:null)},!0),x(o,\"blur\",t=>s(t,()=>d.isHTMLIframeElement(window.document.activeElement)?window.document.activeElement:null),!0)}export{k as useOutsideClick};\n"], "names": [], "mappings": ";;;AAAA;AAAgD;AAAkC;AAAqF;AAAgD;AAA2D;AAAuD;;;;;;;;AAAuD,MAAM,IAAE;AAAG,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,gLAAA,CAAA,iBAAC,AAAD,EAAE,IAAG,IAAE,CAAA,GAAA,qMAAA,CAAA,cAAC,AAAD,EAAE,SAAS,CAAC,EAAC,CAAC;QAAE,IAAG,EAAE,gBAAgB,EAAC;QAAO,IAAI,IAAE,EAAE;QAAG,IAAG,MAAI,QAAM,CAAC,EAAE,WAAW,GAAG,QAAQ,CAAC,MAAI,CAAC,EAAE,WAAW,EAAC;QAAO,IAAI,IAAE,SAAS,EAAE,CAAC;YAAE,OAAO,OAAO,KAAG,aAAW,EAAE,OAAK,MAAM,OAAO,CAAC,MAAI,aAAa,MAAI,IAAE;gBAAC;aAAE;QAAA,EAAE;QAAG,KAAI,IAAI,KAAK,EAAE,IAAG,MAAI,QAAM,CAAC,EAAE,QAAQ,CAAC,MAAI,EAAE,QAAQ,IAAE,EAAE,YAAY,GAAG,QAAQ,CAAC,EAAE,GAAE;QAAO,OAAM,CAAC,CAAA,GAAA,6KAAA,CAAA,qBAAC,AAAD,EAAE,GAAE,6KAAA,CAAA,gBAAC,CAAC,KAAK,KAAG,EAAE,QAAQ,KAAG,CAAC,KAAG,EAAE,cAAc,IAAG,EAAE,OAAO,CAAC,GAAE;IAAE,GAAE;QAAC;QAAE;KAAE,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,SAAC,AAAD,EAAE;IAAM,CAAA,GAAA,kLAAA,CAAA,mBAAC,AAAD,EAAE,GAAE,eAAc,CAAA;QAAI,IAAI,GAAE;QAAE,CAAA,GAAA,kKAAA,CAAA,WAAC,AAAD,OAAK,CAAC,EAAE,OAAO,GAAC,CAAC,CAAC,IAAE,CAAC,IAAE,EAAE,YAAY,KAAG,OAAK,KAAK,IAAE,EAAE,IAAI,CAAC,EAAE,KAAG,OAAK,KAAK,IAAE,CAAC,CAAC,EAAE,KAAG,EAAE,MAAM;IAAC,GAAE,CAAC,IAAG,CAAA,GAAA,kLAAA,CAAA,mBAAC,AAAD,EAAE,GAAE,aAAY,CAAA;QAAI,IAAG,CAAA,GAAA,kKAAA,CAAA,WAAC,AAAD,OAAK,CAAC,EAAE,OAAO,EAAC;QAAO,IAAI,IAAE,EAAE,OAAO;QAAC,OAAO,EAAE,OAAO,GAAC,MAAK,EAAE,GAAE,IAAI;IAAE,GAAE,CAAC;IAAG,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,SAAC,AAAD,EAAE;QAAC,GAAE;QAAE,GAAE;IAAC;IAAG,CAAA,GAAA,kLAAA,CAAA,mBAAC,AAAD,EAAE,GAAE,cAAa,CAAA;QAAI,EAAE,OAAO,CAAC,CAAC,GAAC,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAC,EAAE,OAAO,CAAC,CAAC,GAAC,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO;IAAA,GAAE,CAAC,IAAG,CAAA,GAAA,kLAAA,CAAA,mBAAC,AAAD,EAAE,GAAE,YAAW,CAAA;QAAI,IAAI,IAAE;YAAC,GAAE,EAAE,cAAc,CAAC,EAAE,CAAC,OAAO;YAAC,GAAE,EAAE,cAAc,CAAC,EAAE,CAAC,OAAO;QAAA;QAAE,IAAG,CAAC,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,GAAC,EAAE,OAAO,CAAC,CAAC,KAAG,KAAG,KAAK,GAAG,CAAC,EAAE,CAAC,GAAC,EAAE,OAAO,CAAC,CAAC,KAAG,CAAC,GAAE,OAAO,EAAE,GAAE,IAAI,6JAAA,CAAA,qBAAoB,CAAC,EAAE,MAAM,IAAE,EAAE,MAAM,GAAC;IAAK,GAAE,CAAC,IAAG,CAAA,GAAA,gLAAA,CAAA,iBAAC,AAAD,EAAE,GAAE,QAAO,CAAA,IAAG,EAAE,GAAE,IAAI,6JAAA,CAAA,sBAAqB,CAAC,OAAO,QAAQ,CAAC,aAAa,IAAE,OAAO,QAAQ,CAAC,aAAa,GAAC,OAAM,CAAC;AAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 953, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/hooks/use-owner.js"], "sourcesContent": ["import{useMemo as t}from\"react\";import{getOwnerDocument as o}from'../utils/owner.js';function n(...e){return t(()=>o(...e),[...e])}export{n as useOwnerDocument};\n"], "names": [], "mappings": ";;;AAAA;AAAgC;;;AAAqD,SAAS,EAAE,GAAG,CAAC;IAAE,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAA,GAAA,+JAAA,CAAA,mBAAC,AAAD,KAAK,IAAG;WAAI;KAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 970, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/utils/class-names.js"], "sourcesContent": ["function t(...r){return Array.from(new Set(r.flatMap(n=>typeof n==\"string\"?n.split(\" \"):[]))).filter(Boolean).join(\" \")}export{t as classNames};\n"], "names": [], "mappings": ";;;AAAA,SAAS,EAAE,GAAG,CAAC;IAAE,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI,EAAE,OAAO,CAAC,CAAA,IAAG,OAAO,KAAG,WAAS,EAAE,KAAK,CAAC,OAAK,EAAE,IAAI,MAAM,CAAC,SAAS,IAAI,CAAC;AAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 981, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/utils/render.js"], "sourcesContent": ["import E,{Fragment as b,cloneElement as j,createElement as v,forwardRef as S,isValidElement as w,use<PERSON><PERSON>back as x,useRef as k}from\"react\";import{classNames as N}from'./class-names.js';import{match as M}from'./match.js';var O=(a=>(a[a.None=0]=\"None\",a[a.RenderStrategy=1]=\"RenderStrategy\",a[a.Static=2]=\"Static\",a))(O||{}),A=(e=>(e[e.Unmount=0]=\"Unmount\",e[e.Hidden=1]=\"Hidden\",e))(A||{});function L(){let n=U();return x(r=>C({mergeRefs:n,...r}),[n])}function C({ourProps:n,theirProps:r,slot:e,defaultTag:a,features:s,visible:t=!0,name:l,mergeRefs:i}){i=i!=null?i:$;let o=P(r,n);if(t)return F(o,e,a,l,i);let y=s!=null?s:0;if(y&2){let{static:f=!1,...u}=o;if(f)return F(u,e,a,l,i)}if(y&1){let{unmount:f=!0,...u}=o;return M(f?0:1,{[0](){return null},[1](){return F({...u,hidden:!0,style:{display:\"none\"}},e,a,l,i)}})}return F(o,e,a,l,i)}function F(n,r={},e,a,s){let{as:t=e,children:l,refName:i=\"ref\",...o}=h(n,[\"unmount\",\"static\"]),y=n.ref!==void 0?{[i]:n.ref}:{},f=typeof l==\"function\"?l(r):l;\"className\"in o&&o.className&&typeof o.className==\"function\"&&(o.className=o.className(r)),o[\"aria-labelledby\"]&&o[\"aria-labelledby\"]===o.id&&(o[\"aria-labelledby\"]=void 0);let u={};if(r){let d=!1,p=[];for(let[c,T]of Object.entries(r))typeof T==\"boolean\"&&(d=!0),T===!0&&p.push(c.replace(/([A-Z])/g,g=>`-${g.toLowerCase()}`));if(d){u[\"data-headlessui-state\"]=p.join(\" \");for(let c of p)u[`data-${c}`]=\"\"}}if(t===b&&(Object.keys(m(o)).length>0||Object.keys(m(u)).length>0))if(!w(f)||Array.isArray(f)&&f.length>1){if(Object.keys(m(o)).length>0)throw new Error(['Passing props on \"Fragment\"!',\"\",`The current component <${a} /> is rendering a \"Fragment\".`,\"However we need to passthrough the following props:\",Object.keys(m(o)).concat(Object.keys(m(u))).map(d=>`  - ${d}`).join(`\n`),\"\",\"You can apply a few solutions:\",['Add an `as=\"...\"` prop, to ensure that we render an actual element instead of a \"Fragment\".',\"Render a single element as the child so that we can forward the props onto that element.\"].map(d=>`  - ${d}`).join(`\n`)].join(`\n`))}else{let d=f.props,p=d==null?void 0:d.className,c=typeof p==\"function\"?(...R)=>N(p(...R),o.className):N(p,o.className),T=c?{className:c}:{},g=P(f.props,m(h(o,[\"ref\"])));for(let R in u)R in g&&delete u[R];return j(f,Object.assign({},g,u,y,{ref:s(H(f),y.ref)},T))}return v(t,Object.assign({},h(o,[\"ref\"]),t!==b&&y,t!==b&&u),f)}function U(){let n=k([]),r=x(e=>{for(let a of n.current)a!=null&&(typeof a==\"function\"?a(e):a.current=e)},[]);return(...e)=>{if(!e.every(a=>a==null))return n.current=e,r}}function $(...n){return n.every(r=>r==null)?void 0:r=>{for(let e of n)e!=null&&(typeof e==\"function\"?e(r):e.current=r)}}function P(...n){var a;if(n.length===0)return{};if(n.length===1)return n[0];let r={},e={};for(let s of n)for(let t in s)t.startsWith(\"on\")&&typeof s[t]==\"function\"?((a=e[t])!=null||(e[t]=[]),e[t].push(s[t])):r[t]=s[t];if(r.disabled||r[\"aria-disabled\"])for(let s in e)/^(on(?:Click|Pointer|Mouse|Key)(?:Down|Up|Press)?)$/.test(s)&&(e[s]=[t=>{var l;return(l=t==null?void 0:t.preventDefault)==null?void 0:l.call(t)}]);for(let s in e)Object.assign(r,{[s](t,...l){let i=e[s];for(let o of i){if((t instanceof Event||(t==null?void 0:t.nativeEvent)instanceof Event)&&t.defaultPrevented)return;o(t,...l)}}});return r}function _(...n){var a;if(n.length===0)return{};if(n.length===1)return n[0];let r={},e={};for(let s of n)for(let t in s)t.startsWith(\"on\")&&typeof s[t]==\"function\"?((a=e[t])!=null||(e[t]=[]),e[t].push(s[t])):r[t]=s[t];for(let s in e)Object.assign(r,{[s](...t){let l=e[s];for(let i of l)i==null||i(...t)}});return r}function K(n){var r;return Object.assign(S(n),{displayName:(r=n.displayName)!=null?r:n.name})}function m(n){let r=Object.assign({},n);for(let e in r)r[e]===void 0&&delete r[e];return r}function h(n,r=[]){let e=Object.assign({},n);for(let a of r)a in e&&delete e[a];return e}function H(n){return E.version.split(\".\")[0]>=\"19\"?n.props.ref:n.ref}export{O as RenderFeatures,A as RenderStrategy,m as compact,K as forwardRefWithAs,_ as mergeProps,L as useRender};\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAA0I;AAA8C;;;;AAAmC,IAAI,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,IAAI,GAAC,EAAE,GAAC,QAAO,CAAC,CAAC,EAAE,cAAc,GAAC,EAAE,GAAC,kBAAiB,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE,GAAC,UAAS,CAAC,CAAC,EAAE,KAAG,CAAC,IAAG,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,OAAO,GAAC,EAAE,GAAC,WAAU,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE,GAAC,UAAS,CAAC,CAAC,EAAE,KAAG,CAAC;AAAG,SAAS;IAAI,IAAI,IAAE;IAAI,OAAO,CAAA,GAAA,qMAAA,CAAA,cAAC,AAAD,EAAE,CAAA,IAAG,EAAE;YAAC,WAAU;YAAE,GAAG,CAAC;QAAA,IAAG;QAAC;KAAE;AAAC;AAAC,SAAS,EAAE,EAAC,UAAS,CAAC,EAAC,YAAW,CAAC,EAAC,MAAK,CAAC,EAAC,YAAW,CAAC,EAAC,UAAS,CAAC,EAAC,SAAQ,IAAE,CAAC,CAAC,EAAC,MAAK,CAAC,EAAC,WAAU,CAAC,EAAC;IAAE,IAAE,KAAG,OAAK,IAAE;IAAE,IAAI,IAAE,EAAE,GAAE;IAAG,IAAG,GAAE,OAAO,EAAE,GAAE,GAAE,GAAE,GAAE;IAAG,IAAI,IAAE,KAAG,OAAK,IAAE;IAAE,IAAG,IAAE,GAAE;QAAC,IAAG,EAAC,QAAO,IAAE,CAAC,CAAC,EAAC,GAAG,GAAE,GAAC;QAAE,IAAG,GAAE,OAAO,EAAE,GAAE,GAAE,GAAE,GAAE;IAAE;IAAC,IAAG,IAAE,GAAE;QAAC,IAAG,EAAC,SAAQ,IAAE,CAAC,CAAC,EAAC,GAAG,GAAE,GAAC;QAAE,OAAO,CAAA,GAAA,+JAAA,CAAA,QAAC,AAAD,EAAE,IAAE,IAAE,GAAE;YAAC,CAAC,EAAE;gBAAG,OAAO;YAAI;YAAE,CAAC,EAAE;gBAAG,OAAO,EAAE;oBAAC,GAAG,CAAC;oBAAC,QAAO,CAAC;oBAAE,OAAM;wBAAC,SAAQ;oBAAM;gBAAC,GAAE,GAAE,GAAE,GAAE;YAAE;QAAC;IAAE;IAAC,OAAO,EAAE,GAAE,GAAE,GAAE,GAAE;AAAE;AAAC,SAAS,EAAE,CAAC,EAAC,IAAE,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;IAAE,IAAG,EAAC,IAAG,IAAE,CAAC,EAAC,UAAS,CAAC,EAAC,SAAQ,IAAE,KAAK,EAAC,GAAG,GAAE,GAAC,EAAE,GAAE;QAAC;QAAU;KAAS,GAAE,IAAE,EAAE,GAAG,KAAG,KAAK,IAAE;QAAC,CAAC,EAAE,EAAC,EAAE,GAAG;IAAA,IAAE,CAAC,GAAE,IAAE,OAAO,KAAG,aAAW,EAAE,KAAG;IAAE,eAAc,KAAG,EAAE,SAAS,IAAE,OAAO,EAAE,SAAS,IAAE,cAAY,CAAC,EAAE,SAAS,GAAC,EAAE,SAAS,CAAC,EAAE,GAAE,CAAC,CAAC,kBAAkB,IAAE,CAAC,CAAC,kBAAkB,KAAG,EAAE,EAAE,IAAE,CAAC,CAAC,CAAC,kBAAkB,GAAC,KAAK,CAAC;IAAE,IAAI,IAAE,CAAC;IAAE,IAAG,GAAE;QAAC,IAAI,IAAE,CAAC,GAAE,IAAE,EAAE;QAAC,KAAI,IAAG,CAAC,GAAE,EAAE,IAAG,OAAO,OAAO,CAAC,GAAG,OAAO,KAAG,aAAW,CAAC,IAAE,CAAC,CAAC,GAAE,MAAI,CAAC,KAAG,EAAE,IAAI,CAAC,EAAE,OAAO,CAAC,YAAW,CAAA,IAAG,CAAC,CAAC,EAAE,EAAE,WAAW,IAAI;QAAG,IAAG,GAAE;YAAC,CAAC,CAAC,wBAAwB,GAAC,EAAE,IAAI,CAAC;YAAK,KAAI,IAAI,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,EAAE,GAAG,CAAC,GAAC;QAAE;IAAC;IAAC,IAAG,MAAI,qMAAA,CAAA,WAAC,IAAE,CAAC,OAAO,IAAI,CAAC,EAAE,IAAI,MAAM,GAAC,KAAG,OAAO,IAAI,CAAC,EAAE,IAAI,MAAM,GAAC,CAAC,GAAE,IAAG,CAAC,CAAA,GAAA,qMAAA,CAAA,iBAAC,AAAD,EAAE,MAAI,MAAM,OAAO,CAAC,MAAI,EAAE,MAAM,GAAC,GAAE;QAAC,IAAG,OAAO,IAAI,CAAC,EAAE,IAAI,MAAM,GAAC,GAAE,MAAM,IAAI,MAAM;YAAC;YAA+B;YAAG,CAAC,uBAAuB,EAAE,EAAE,8BAA8B,CAAC;YAAC;YAAsD,OAAO,IAAI,CAAC,EAAE,IAAI,MAAM,CAAC,OAAO,IAAI,CAAC,EAAE,KAAK,GAAG,CAAC,CAAA,IAAG,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;AACruD,CAAC;YAAE;YAAG;YAAiC;gBAAC;gBAA8F;aAA2F,CAAC,GAAG,CAAC,CAAA,IAAG,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;AAC3P,CAAC;SAAE,CAAC,IAAI,CAAC,CAAC;AACV,CAAC;IAAE,OAAK;QAAC,IAAI,IAAE,EAAE,KAAK,EAAC,IAAE,KAAG,OAAK,KAAK,IAAE,EAAE,SAAS,EAAC,IAAE,OAAO,KAAG,aAAW,CAAC,GAAG,IAAI,CAAA,GAAA,wKAAA,CAAA,aAAC,AAAD,EAAE,KAAK,IAAG,EAAE,SAAS,IAAE,CAAA,GAAA,wKAAA,CAAA,aAAC,AAAD,EAAE,GAAE,EAAE,SAAS,GAAE,IAAE,IAAE;YAAC,WAAU;QAAC,IAAE,CAAC,GAAE,IAAE,EAAE,EAAE,KAAK,EAAC,EAAE,EAAE,GAAE;YAAC;SAAM;QAAI,IAAI,IAAI,KAAK,EAAE,KAAK,KAAG,OAAO,CAAC,CAAC,EAAE;QAAC,OAAO,CAAA,GAAA,qMAAA,CAAA,eAAC,AAAD,EAAE,GAAE,OAAO,MAAM,CAAC,CAAC,GAAE,GAAE,GAAE,GAAE;YAAC,KAAI,EAAE,EAAE,IAAG,EAAE,GAAG;QAAC,GAAE;IAAG;IAAC,OAAO,CAAA,GAAA,qMAAA,CAAA,gBAAC,AAAD,EAAE,GAAE,OAAO,MAAM,CAAC,CAAC,GAAE,EAAE,GAAE;QAAC;KAAM,GAAE,MAAI,qMAAA,CAAA,WAAC,IAAE,GAAE,MAAI,qMAAA,CAAA,WAAC,IAAE,IAAG;AAAE;AAAC,SAAS;IAAI,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,SAAC,AAAD,EAAE,EAAE,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,cAAC,AAAD,EAAE,CAAA;QAAI,KAAI,IAAI,KAAK,EAAE,OAAO,CAAC,KAAG,QAAM,CAAC,OAAO,KAAG,aAAW,EAAE,KAAG,EAAE,OAAO,GAAC,CAAC;IAAC,GAAE,EAAE;IAAE,OAAM,CAAC,GAAG;QAAK,IAAG,CAAC,EAAE,KAAK,CAAC,CAAA,IAAG,KAAG,OAAM,OAAO,EAAE,OAAO,GAAC,GAAE;IAAC;AAAC;AAAC,SAAS,EAAE,GAAG,CAAC;IAAE,OAAO,EAAE,KAAK,CAAC,CAAA,IAAG,KAAG,QAAM,KAAK,IAAE,CAAA;QAAI,KAAI,IAAI,KAAK,EAAE,KAAG,QAAM,CAAC,OAAO,KAAG,aAAW,EAAE,KAAG,EAAE,OAAO,GAAC,CAAC;IAAC;AAAC;AAAC,SAAS,EAAE,GAAG,CAAC;IAAE,IAAI;IAAE,IAAG,EAAE,MAAM,KAAG,GAAE,OAAM,CAAC;IAAE,IAAG,EAAE,MAAM,KAAG,GAAE,OAAO,CAAC,CAAC,EAAE;IAAC,IAAI,IAAE,CAAC,GAAE,IAAE,CAAC;IAAE,KAAI,IAAI,KAAK,EAAE,IAAI,IAAI,KAAK,EAAE,EAAE,UAAU,CAAC,SAAO,OAAO,CAAC,CAAC,EAAE,IAAE,aAAW,CAAC,CAAC,IAAE,CAAC,CAAC,EAAE,KAAG,QAAM,CAAC,CAAC,CAAC,EAAE,GAAC,EAAE,GAAE,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,IAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE;IAAC,IAAG,EAAE,QAAQ,IAAE,CAAC,CAAC,gBAAgB,EAAC,IAAI,IAAI,KAAK,EAAE,sDAAsD,IAAI,CAAC,MAAI,CAAC,CAAC,CAAC,EAAE,GAAC;QAAC,CAAA;YAAI,IAAI;YAAE,OAAM,CAAC,IAAE,KAAG,OAAK,KAAK,IAAE,EAAE,cAAc,KAAG,OAAK,KAAK,IAAE,EAAE,IAAI,CAAC;QAAE;KAAE;IAAE,IAAI,IAAI,KAAK,EAAE,OAAO,MAAM,CAAC,GAAE;QAAC,CAAC,EAAE,EAAC,CAAC,EAAC,GAAG,CAAC;YAAE,IAAI,IAAE,CAAC,CAAC,EAAE;YAAC,KAAI,IAAI,KAAK,EAAE;gBAAC,IAAG,CAAC,aAAa,SAAO,CAAC,KAAG,OAAK,KAAK,IAAE,EAAE,WAAW,aAAY,KAAK,KAAG,EAAE,gBAAgB,EAAC;gBAAO,EAAE,MAAK;YAAE;QAAC;IAAC;IAAG,OAAO;AAAC;AAAC,SAAS,EAAE,GAAG,CAAC;IAAE,IAAI;IAAE,IAAG,EAAE,MAAM,KAAG,GAAE,OAAM,CAAC;IAAE,IAAG,EAAE,MAAM,KAAG,GAAE,OAAO,CAAC,CAAC,EAAE;IAAC,IAAI,IAAE,CAAC,GAAE,IAAE,CAAC;IAAE,KAAI,IAAI,KAAK,EAAE,IAAI,IAAI,KAAK,EAAE,EAAE,UAAU,CAAC,SAAO,OAAO,CAAC,CAAC,EAAE,IAAE,aAAW,CAAC,CAAC,IAAE,CAAC,CAAC,EAAE,KAAG,QAAM,CAAC,CAAC,CAAC,EAAE,GAAC,EAAE,GAAE,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,IAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE;IAAC,IAAI,IAAI,KAAK,EAAE,OAAO,MAAM,CAAC,GAAE;QAAC,CAAC,EAAE,EAAC,GAAG,CAAC;YAAE,IAAI,IAAE,CAAC,CAAC,EAAE;YAAC,KAAI,IAAI,KAAK,EAAE,KAAG,QAAM,KAAK;QAAE;IAAC;IAAG,OAAO;AAAC;AAAC,SAAS,EAAE,CAAC;IAAE,IAAI;IAAE,OAAO,OAAO,MAAM,CAAC,CAAA,GAAA,qMAAA,CAAA,aAAC,AAAD,EAAE,IAAG;QAAC,aAAY,CAAC,IAAE,EAAE,WAAW,KAAG,OAAK,IAAE,EAAE,IAAI;IAAA;AAAE;AAAC,SAAS,EAAE,CAAC;IAAE,IAAI,IAAE,OAAO,MAAM,CAAC,CAAC,GAAE;IAAG,IAAI,IAAI,KAAK,EAAE,CAAC,CAAC,EAAE,KAAG,KAAK,KAAG,OAAO,CAAC,CAAC,EAAE;IAAC,OAAO;AAAC;AAAC,SAAS,EAAE,CAAC,EAAC,IAAE,EAAE;IAAE,IAAI,IAAE,OAAO,MAAM,CAAC,CAAC,GAAE;IAAG,KAAI,IAAI,KAAK,EAAE,KAAK,KAAG,OAAO,CAAC,CAAC,EAAE;IAAC,OAAO;AAAC;AAAC,SAAS,EAAE,CAAC;IAAE,OAAO,qMAAA,CAAA,UAAC,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAE,OAAK,EAAE,KAAK,CAAC,GAAG,GAAC,EAAE,GAAG;AAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1156, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/internal/hidden.js"], "sourcesContent": ["import{forwardRefWithAs as i,useRender as p}from'../utils/render.js';let a=\"span\";var s=(e=>(e[e.None=1]=\"None\",e[e.Focusable=2]=\"Focusable\",e[e.Hidden=4]=\"Hidden\",e))(s||{});function l(t,r){var n;let{features:d=1,...e}=t,o={ref:r,\"aria-hidden\":(d&2)===2?!0:(n=e[\"aria-hidden\"])!=null?n:void 0,hidden:(d&4)===4?!0:void 0,style:{position:\"fixed\",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:\"hidden\",clip:\"rect(0, 0, 0, 0)\",whiteSpace:\"nowrap\",borderWidth:\"0\",...(d&4)===4&&(d&2)!==2&&{display:\"none\"}}};return p()({ourProps:o,theirProps:e,slot:{},defaultTag:a,name:\"Hidden\"})}let f=i(l);export{f as Hidden,s as HiddenFeatures};\n"], "names": [], "mappings": ";;;;AAAA;;AAAqE,IAAI,IAAE;AAAO,IAAI,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,IAAI,GAAC,EAAE,GAAC,QAAO,CAAC,CAAC,EAAE,SAAS,GAAC,EAAE,GAAC,aAAY,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE,GAAC,UAAS,CAAC,CAAC,EAAE,KAAG,CAAC;AAAG,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,IAAI;IAAE,IAAG,EAAC,UAAS,IAAE,CAAC,EAAC,GAAG,GAAE,GAAC,GAAE,IAAE;QAAC,KAAI;QAAE,eAAc,CAAC,IAAE,CAAC,MAAI,IAAE,CAAC,IAAE,CAAC,IAAE,CAAC,CAAC,cAAc,KAAG,OAAK,IAAE,KAAK;QAAE,QAAO,CAAC,IAAE,CAAC,MAAI,IAAE,CAAC,IAAE,KAAK;QAAE,OAAM;YAAC,UAAS;YAAQ,KAAI;YAAE,MAAK;YAAE,OAAM;YAAE,QAAO;YAAE,SAAQ;YAAE,QAAO,CAAC;YAAE,UAAS;YAAS,MAAK;YAAmB,YAAW;YAAS,aAAY;YAAI,GAAG,CAAC,IAAE,CAAC,MAAI,KAAG,CAAC,IAAE,CAAC,MAAI,KAAG;gBAAC,SAAQ;YAAM,CAAC;QAAA;IAAC;IAAE,OAAO,CAAA,GAAA,gKAAA,CAAA,YAAC,AAAD,IAAI;QAAC,UAAS;QAAE,YAAW;QAAE,MAAK,CAAC;QAAE,YAAW;QAAE,MAAK;IAAQ;AAAE;AAAC,IAAI,IAAE,CAAA,GAAA,gKAAA,CAAA,mBAAC,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1201, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/hooks/use-root-containers.js"], "sourcesContent": ["import s,{createContext as E,useContext as h,useState as p}from\"react\";import{Hidden as b,HiddenFeatures as M}from'../internal/hidden.js';import*as f from'../utils/dom.js';import{getOwnerDocument as v}from'../utils/owner.js';import{useEvent as m}from'./use-event.js';import{useOwnerDocument as x}from'./use-owner.js';function H({defaultContainers:r=[],portals:n,mainTreeNode:o}={}){let l=x(o),u=m(()=>{var i,c;let t=[];for(let e of r)e!==null&&(f.isElement(e)?t.push(e):\"current\"in e&&f.isElement(e.current)&&t.push(e.current));if(n!=null&&n.current)for(let e of n.current)t.push(e);for(let e of(i=l==null?void 0:l.querySelectorAll(\"html > *, body > *\"))!=null?i:[])e!==document.body&&e!==document.head&&f.isElement(e)&&e.id!==\"headlessui-portal-root\"&&(o&&(e.contains(o)||e.contains((c=o==null?void 0:o.getRootNode())==null?void 0:c.host))||t.some(d=>e.contains(d))||t.push(e));return t});return{resolveContainers:u,contains:m(t=>u().some(i=>i.contains(t)))}}let a=E(null);function P({children:r,node:n}){let[o,l]=p(null),u=y(n!=null?n:o);return s.createElement(a.Provider,{value:u},r,u===null&&s.createElement(b,{features:M.Hidden,ref:t=>{var i,c;if(t){for(let e of(c=(i=v(t))==null?void 0:i.querySelectorAll(\"html > *, body > *\"))!=null?c:[])if(e!==document.body&&e!==document.head&&f.isElement(e)&&e!=null&&e.contains(t)){l(e);break}}}}))}function y(r=null){var n;return(n=h(a))!=null?n:r}export{P as MainTreeProvider,y as useMainTreeNode,H as useRootContainers};\n"], "names": [], "mappings": ";;;;;AAAA;AAAuE;AAAmE;AAAkC;AAAqD;AAA0C;;;;;;;AAAkD,SAAS,EAAE,EAAC,mBAAkB,IAAE,EAAE,EAAC,SAAQ,CAAC,EAAC,cAAa,CAAC,EAAC,GAAC,CAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,sKAAA,CAAA,mBAAC,AAAD,EAAE,IAAG,IAAE,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE;QAAK,IAAI,GAAE;QAAE,IAAI,IAAE,EAAE;QAAC,KAAI,IAAI,KAAK,EAAE,MAAI,QAAM,CAAC,6JAAA,CAAA,YAAW,CAAC,KAAG,EAAE,IAAI,CAAC,KAAG,aAAY,KAAG,6JAAA,CAAA,YAAW,CAAC,EAAE,OAAO,KAAG,EAAE,IAAI,CAAC,EAAE,OAAO,CAAC;QAAE,IAAG,KAAG,QAAM,EAAE,OAAO,EAAC,KAAI,IAAI,KAAK,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC;QAAG,KAAI,IAAI,KAAI,CAAC,IAAE,KAAG,OAAK,KAAK,IAAE,EAAE,gBAAgB,CAAC,qBAAqB,KAAG,OAAK,IAAE,EAAE,CAAC,MAAI,SAAS,IAAI,IAAE,MAAI,SAAS,IAAI,IAAE,6JAAA,CAAA,YAAW,CAAC,MAAI,EAAE,EAAE,KAAG,4BAA0B,CAAC,KAAG,CAAC,EAAE,QAAQ,CAAC,MAAI,EAAE,QAAQ,CAAC,CAAC,IAAE,KAAG,OAAK,KAAK,IAAE,EAAE,WAAW,EAAE,KAAG,OAAK,KAAK,IAAE,EAAE,IAAI,CAAC,KAAG,EAAE,IAAI,CAAC,CAAA,IAAG,EAAE,QAAQ,CAAC,OAAK,EAAE,IAAI,CAAC,EAAE;QAAE,OAAO;IAAC;IAAG,OAAM;QAAC,mBAAkB;QAAE,UAAS,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE,CAAA,IAAG,IAAI,IAAI,CAAC,CAAA,IAAG,EAAE,QAAQ,CAAC;IAAI;AAAC;AAAC,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,gBAAC,AAAD,EAAE;AAAM,SAAS,EAAE,EAAC,UAAS,CAAC,EAAC,MAAK,CAAC,EAAC;IAAE,IAAG,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,qMAAA,CAAA,WAAC,AAAD,EAAE,OAAM,IAAE,EAAE,KAAG,OAAK,IAAE;IAAG,OAAO,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAC;QAAC,OAAM;IAAC,GAAE,GAAE,MAAI,QAAM,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,mKAAA,CAAA,SAAC,EAAC;QAAC,UAAS,mKAAA,CAAA,iBAAC,CAAC,MAAM;QAAC,KAAI,CAAA;YAAI,IAAI,GAAE;YAAE,IAAG,GAAE;gBAAC,KAAI,IAAI,KAAI,CAAC,IAAE,CAAC,IAAE,CAAA,GAAA,+JAAA,CAAA,mBAAC,AAAD,EAAE,EAAE,KAAG,OAAK,KAAK,IAAE,EAAE,gBAAgB,CAAC,qBAAqB,KAAG,OAAK,IAAE,EAAE,CAAC,IAAG,MAAI,SAAS,IAAI,IAAE,MAAI,SAAS,IAAI,IAAE,6JAAA,CAAA,YAAW,CAAC,MAAI,KAAG,QAAM,EAAE,QAAQ,CAAC,IAAG;oBAAC,EAAE;oBAAG;gBAAK;YAAC;QAAC;IAAC;AAAG;AAAC,SAAS,EAAE,IAAE,IAAI;IAAE,IAAI;IAAE,OAAM,CAAC,IAAE,CAAA,GAAA,qMAAA,CAAA,aAAC,AAAD,EAAE,EAAE,KAAG,OAAK,IAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1259, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/hooks/use-store.js"], "sourcesContent": ["import{useSyncExternalStore as e}from\"react\";function o(t){return e(t.subscribe,t.getSnapshot,t.getSnapshot)}export{o as useStore};\n"], "names": [], "mappings": ";;;AAAA;;AAA6C,SAAS,EAAE,CAAC;IAAE,OAAO,CAAA,GAAA,qMAAA,CAAA,uBAAC,AAAD,EAAE,EAAE,SAAS,EAAC,EAAE,WAAW,EAAC,EAAE,WAAW;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1272, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/utils/store.js"], "sourcesContent": ["function a(o,r){let t=o(),n=new Set;return{getSnapshot(){return t},subscribe(e){return n.add(e),()=>n.delete(e)},dispatch(e,...s){let i=r[e].call(t,...s);i&&(t=i,n.forEach(c=>c()))}}}export{a as createStore};\n"], "names": [], "mappings": ";;;AAAA,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,KAAI,IAAE,IAAI;IAAI,OAAM;QAAC;YAAc,OAAO;QAAC;QAAE,WAAU,CAAC;YAAE,OAAO,EAAE,GAAG,CAAC,IAAG,IAAI,EAAE,MAAM,CAAC;QAAE;QAAE,UAAS,CAAC,EAAC,GAAG,CAAC;YAAE,IAAI,IAAE,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,MAAK;YAAG,KAAG,CAAC,IAAE,GAAE,EAAE,OAAO,CAAC,CAAA,IAAG,IAAI;QAAC;IAAC;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1295, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/hooks/document-overflow/adjust-scrollbar-padding.js"], "sourcesContent": ["function d(){let r;return{before({doc:e}){var l;let o=e.documentElement,t=(l=e.defaultView)!=null?l:window;r=Math.max(0,t.innerWidth-o.clientWidth)},after({doc:e,d:o}){let t=e.documentElement,l=Math.max(0,t.clientWidth-t.offsetWidth),n=Math.max(0,r-l);o.style(t,\"paddingRight\",`${n}px`)}}}export{d as adjustScrollbarPadding};\n"], "names": [], "mappings": ";;;AAAA,SAAS;IAAI,IAAI;IAAE,OAAM;QAAC,QAAO,EAAC,KAAI,CAAC,EAAC;YAAE,IAAI;YAAE,IAAI,IAAE,EAAE,eAAe,EAAC,IAAE,CAAC,IAAE,EAAE,WAAW,KAAG,OAAK,IAAE;YAAO,IAAE,KAAK,GAAG,CAAC,GAAE,EAAE,UAAU,GAAC,EAAE,WAAW;QAAC;QAAE,OAAM,EAAC,KAAI,CAAC,EAAC,GAAE,CAAC,EAAC;YAAE,IAAI,IAAE,EAAE,eAAe,EAAC,IAAE,KAAK,GAAG,CAAC,GAAE,EAAE,WAAW,GAAC,EAAE,WAAW,GAAE,IAAE,KAAK,GAAG,CAAC,GAAE,IAAE;YAAG,EAAE,KAAK,CAAC,GAAE,gBAAe,GAAG,EAAE,EAAE,CAAC;QAAC;IAAC;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1317, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/hooks/document-overflow/handle-ios-locking.js"], "sourcesContent": ["import{disposables as u}from'../../utils/disposables.js';import*as o from'../../utils/dom.js';import{isIOS as p}from'../../utils/platform.js';function w(){return p()?{before({doc:n,d:l,meta:f}){function i(a){return f.containers.flatMap(r=>r()).some(r=>r.contains(a))}l.microTask(()=>{var c;if(window.getComputedStyle(n.documentElement).scrollBehavior!==\"auto\"){let t=u();t.style(n.documentElement,\"scrollBehavior\",\"auto\"),l.add(()=>l.microTask(()=>t.dispose()))}let a=(c=window.scrollY)!=null?c:window.pageYOffset,r=null;l.addEventListener(n,\"click\",t=>{if(o.isHTMLorSVGElement(t.target))try{let e=t.target.closest(\"a\");if(!e)return;let{hash:m}=new URL(e.href),s=n.querySelector(m);o.isHTMLorSVGElement(s)&&!i(s)&&(r=s)}catch{}},!0),l.addEventListener(n,\"touchstart\",t=>{if(o.isHTMLorSVGElement(t.target)&&o.hasInlineStyle(t.target))if(i(t.target)){let e=t.target;for(;e.parentElement&&i(e.parentElement);)e=e.parentElement;l.style(e,\"overscrollBehavior\",\"contain\")}else l.style(t.target,\"touchAction\",\"none\")}),l.addEventListener(n,\"touchmove\",t=>{if(o.isHTMLorSVGElement(t.target)){if(o.isHTMLInputElement(t.target))return;if(i(t.target)){let e=t.target;for(;e.parentElement&&e.dataset.headlessuiPortal!==\"\"&&!(e.scrollHeight>e.clientHeight||e.scrollWidth>e.clientWidth);)e=e.parentElement;e.dataset.headlessuiPortal===\"\"&&t.preventDefault()}else t.preventDefault()}},{passive:!1}),l.add(()=>{var e;let t=(e=window.scrollY)!=null?e:window.pageYOffset;a!==t&&window.scrollTo(0,a),r&&r.isConnected&&(r.scrollIntoView({block:\"nearest\"}),r=null)})})}}:{}}export{w as handleIOSLocking};\n"], "names": [], "mappings": ";;;AAAA;AAAyD;AAAqC;;;;AAAgD,SAAS;IAAI,OAAO,CAAA,GAAA,kKAAA,CAAA,QAAC,AAAD,MAAI;QAAC,QAAO,EAAC,KAAI,CAAC,EAAC,GAAE,CAAC,EAAC,MAAK,CAAC,EAAC;YAAE,SAAS,EAAE,CAAC;gBAAE,OAAO,EAAE,UAAU,CAAC,OAAO,CAAC,CAAA,IAAG,KAAK,IAAI,CAAC,CAAA,IAAG,EAAE,QAAQ,CAAC;YAAG;YAAC,EAAE,SAAS,CAAC;gBAAK,IAAI;gBAAE,IAAG,OAAO,gBAAgB,CAAC,EAAE,eAAe,EAAE,cAAc,KAAG,QAAO;oBAAC,IAAI,IAAE,CAAA,GAAA,qKAAA,CAAA,cAAC,AAAD;oBAAI,EAAE,KAAK,CAAC,EAAE,eAAe,EAAC,kBAAiB,SAAQ,EAAE,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,IAAI,EAAE,OAAO;gBAAI;gBAAC,IAAI,IAAE,CAAC,IAAE,OAAO,OAAO,KAAG,OAAK,IAAE,OAAO,WAAW,EAAC,IAAE;gBAAK,EAAE,gBAAgB,CAAC,GAAE,SAAQ,CAAA;oBAAI,IAAG,6JAAA,CAAA,qBAAoB,CAAC,EAAE,MAAM,GAAE,IAAG;wBAAC,IAAI,IAAE,EAAE,MAAM,CAAC,OAAO,CAAC;wBAAK,IAAG,CAAC,GAAE;wBAAO,IAAG,EAAC,MAAK,CAAC,EAAC,GAAC,IAAI,IAAI,EAAE,IAAI,GAAE,IAAE,EAAE,aAAa,CAAC;wBAAG,6JAAA,CAAA,qBAAoB,CAAC,MAAI,CAAC,EAAE,MAAI,CAAC,IAAE,CAAC;oBAAC,EAAC,OAAK,CAAC;gBAAC,GAAE,CAAC,IAAG,EAAE,gBAAgB,CAAC,GAAE,cAAa,CAAA;oBAAI,IAAG,6JAAA,CAAA,qBAAoB,CAAC,EAAE,MAAM,KAAG,6JAAA,CAAA,iBAAgB,CAAC,EAAE,MAAM,GAAE,IAAG,EAAE,EAAE,MAAM,GAAE;wBAAC,IAAI,IAAE,EAAE,MAAM;wBAAC,MAAK,EAAE,aAAa,IAAE,EAAE,EAAE,aAAa,GAAG,IAAE,EAAE,aAAa;wBAAC,EAAE,KAAK,CAAC,GAAE,sBAAqB;oBAAU,OAAM,EAAE,KAAK,CAAC,EAAE,MAAM,EAAC,eAAc;gBAAO,IAAG,EAAE,gBAAgB,CAAC,GAAE,aAAY,CAAA;oBAAI,IAAG,6JAAA,CAAA,qBAAoB,CAAC,EAAE,MAAM,GAAE;wBAAC,IAAG,6JAAA,CAAA,qBAAoB,CAAC,EAAE,MAAM,GAAE;wBAAO,IAAG,EAAE,EAAE,MAAM,GAAE;4BAAC,IAAI,IAAE,EAAE,MAAM;4BAAC,MAAK,EAAE,aAAa,IAAE,EAAE,OAAO,CAAC,gBAAgB,KAAG,MAAI,CAAC,CAAC,EAAE,YAAY,GAAC,EAAE,YAAY,IAAE,EAAE,WAAW,GAAC,EAAE,WAAW,GAAG,IAAE,EAAE,aAAa;4BAAC,EAAE,OAAO,CAAC,gBAAgB,KAAG,MAAI,EAAE,cAAc;wBAAE,OAAM,EAAE,cAAc;oBAAE;gBAAC,GAAE;oBAAC,SAAQ,CAAC;gBAAC,IAAG,EAAE,GAAG,CAAC;oBAAK,IAAI;oBAAE,IAAI,IAAE,CAAC,IAAE,OAAO,OAAO,KAAG,OAAK,IAAE,OAAO,WAAW;oBAAC,MAAI,KAAG,OAAO,QAAQ,CAAC,GAAE,IAAG,KAAG,EAAE,WAAW,IAAE,CAAC,EAAE,cAAc,CAAC;wBAAC,OAAM;oBAAS,IAAG,IAAE,IAAI;gBAAC;YAAE;QAAE;IAAC,IAAE,CAAC;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1379, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/hooks/document-overflow/prevent-scroll.js"], "sourcesContent": ["function r(){return{before({doc:e,d:o}){o.style(e.documentElement,\"overflow\",\"hidden\")}}}export{r as preventScroll};\n"], "names": [], "mappings": ";;;AAAA,SAAS;IAAI,OAAM;QAAC,QAAO,EAAC,KAAI,CAAC,EAAC,GAAE,CAAC,EAAC;YAAE,EAAE,KAAK,CAAC,EAAE,eAAe,EAAC,YAAW;QAAS;IAAC;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1394, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/hooks/document-overflow/overflow-store.js"], "sourcesContent": ["import{disposables as s}from'../../utils/disposables.js';import{createStore as i}from'../../utils/store.js';import{adjustScrollbarPadding as l}from'./adjust-scrollbar-padding.js';import{handleIOSLocking as d}from'./handle-ios-locking.js';import{preventScroll as p}from'./prevent-scroll.js';function m(e){let n={};for(let t of e)Object.assign(n,t(n));return n}let a=i(()=>new Map,{PUSH(e,n){var o;let t=(o=this.get(e))!=null?o:{doc:e,count:0,d:s(),meta:new Set};return t.count++,t.meta.add(n),this.set(e,t),this},POP(e,n){let t=this.get(e);return t&&(t.count--,t.meta.delete(n)),this},SCROLL_PREVENT({doc:e,d:n,meta:t}){let o={doc:e,d:n,meta:m(t)},c=[d(),l(),p()];c.forEach(({before:r})=>r==null?void 0:r(o)),c.forEach(({after:r})=>r==null?void 0:r(o))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});a.subscribe(()=>{let e=a.getSnapshot(),n=new Map;for(let[t]of e)n.set(t,t.documentElement.style.overflow);for(let t of e.values()){let o=n.get(t.doc)===\"hidden\",c=t.count!==0;(c&&!o||!c&&o)&&a.dispatch(t.count>0?\"SCROLL_PREVENT\":\"SCROLL_ALLOW\",t),t.count===0&&a.dispatch(\"TEARDOWN\",t)}});export{a as overflows};\n"], "names": [], "mappings": ";;;AAAA;AAAyD;AAAmD;AAAuE;AAA2D;;;;;;AAAoD,SAAS,EAAE,CAAC;IAAE,IAAI,IAAE,CAAC;IAAE,KAAI,IAAI,KAAK,EAAE,OAAO,MAAM,CAAC,GAAE,EAAE;IAAI,OAAO;AAAC;AAAC,IAAI,IAAE,CAAA,GAAA,+JAAA,CAAA,cAAC,AAAD,EAAE,IAAI,IAAI,KAAI;IAAC,MAAK,CAAC,EAAC,CAAC;QAAE,IAAI;QAAE,IAAI,IAAE,CAAC,IAAE,IAAI,CAAC,GAAG,CAAC,EAAE,KAAG,OAAK,IAAE;YAAC,KAAI;YAAE,OAAM;YAAE,GAAE,CAAA,GAAA,qKAAA,CAAA,cAAC,AAAD;YAAI,MAAK,IAAI;QAAG;QAAE,OAAO,EAAE,KAAK,IAAG,EAAE,IAAI,CAAC,GAAG,CAAC,IAAG,IAAI,CAAC,GAAG,CAAC,GAAE,IAAG,IAAI;IAAA;IAAE,KAAI,CAAC,EAAC,CAAC;QAAE,IAAI,IAAE,IAAI,CAAC,GAAG,CAAC;QAAG,OAAO,KAAG,CAAC,EAAE,KAAK,IAAG,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,GAAE,IAAI;IAAA;IAAE,gBAAe,EAAC,KAAI,CAAC,EAAC,GAAE,CAAC,EAAC,MAAK,CAAC,EAAC;QAAE,IAAI,IAAE;YAAC,KAAI;YAAE,GAAE;YAAE,MAAK,EAAE;QAAE,GAAE,IAAE;YAAC,CAAA,GAAA,0MAAA,CAAA,mBAAC,AAAD;YAAI,CAAA,GAAA,gNAAA,CAAA,yBAAC,AAAD;YAAI,CAAA,GAAA,mMAAA,CAAA,gBAAC,AAAD;SAAI;QAAC,EAAE,OAAO,CAAC,CAAC,EAAC,QAAO,CAAC,EAAC,GAAG,KAAG,OAAK,KAAK,IAAE,EAAE,KAAI,EAAE,OAAO,CAAC,CAAC,EAAC,OAAM,CAAC,EAAC,GAAG,KAAG,OAAK,KAAK,IAAE,EAAE;IAAG;IAAE,cAAa,EAAC,GAAE,CAAC,EAAC;QAAE,EAAE,OAAO;IAAE;IAAE,UAAS,EAAC,KAAI,CAAC,EAAC;QAAE,IAAI,CAAC,MAAM,CAAC;IAAE;AAAC;AAAG,EAAE,SAAS,CAAC;IAAK,IAAI,IAAE,EAAE,WAAW,IAAG,IAAE,IAAI;IAAI,KAAI,IAAG,CAAC,EAAE,IAAG,EAAE,EAAE,GAAG,CAAC,GAAE,EAAE,eAAe,CAAC,KAAK,CAAC,QAAQ;IAAE,KAAI,IAAI,KAAK,EAAE,MAAM,GAAG;QAAC,IAAI,IAAE,EAAE,GAAG,CAAC,EAAE,GAAG,MAAI,UAAS,IAAE,EAAE,KAAK,KAAG;QAAE,CAAC,KAAG,CAAC,KAAG,CAAC,KAAG,CAAC,KAAG,EAAE,QAAQ,CAAC,EAAE,KAAK,GAAC,IAAE,mBAAiB,gBAAe,IAAG,EAAE,KAAK,KAAG,KAAG,EAAE,QAAQ,CAAC,YAAW;IAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1459, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/hooks/document-overflow/use-document-overflow.js"], "sourcesContent": ["import{useStore as s}from'../../hooks/use-store.js';import{useIsoMorphicEffect as u}from'../use-iso-morphic-effect.js';import{overflows as t}from'./overflow-store.js';function a(r,e,n=()=>({containers:[]})){let f=s(t),o=e?f.get(e):void 0,i=o?o.count>0:!1;return u(()=>{if(!(!e||!r))return t.dispatch(\"PUSH\",e,n),()=>t.dispatch(\"POP\",e,n)},[r,e]),i}export{a as useDocumentOverflowLockedEffect};\n"], "names": [], "mappings": ";;;AAAA;AAAoD;AAAmE;;;;AAAgD,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,IAAE,IAAI,CAAC;QAAC,YAAW,EAAE;IAAA,CAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE,mMAAA,CAAA,YAAC,GAAE,IAAE,IAAE,EAAE,GAAG,CAAC,KAAG,KAAK,GAAE,IAAE,IAAE,EAAE,KAAK,GAAC,IAAE,CAAC;IAAE,OAAO,CAAA,GAAA,yLAAA,CAAA,sBAAC,AAAD,EAAE;QAAK,IAAG,CAAC,CAAC,CAAC,KAAG,CAAC,CAAC,GAAE,OAAO,mMAAA,CAAA,YAAC,CAAC,QAAQ,CAAC,QAAO,GAAE,IAAG,IAAI,mMAAA,CAAA,YAAC,CAAC,QAAQ,CAAC,OAAM,GAAE;IAAE,GAAE;QAAC;QAAE;KAAE,GAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1484, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/hooks/use-scroll-lock.js"], "sourcesContent": ["import{useDocumentOverflowLockedEffect as l}from'./document-overflow/use-document-overflow.js';import{useIsTopLayer as m}from'./use-is-top-layer.js';function f(e,c,n=()=>[document.body]){let r=m(e,\"scroll-lock\");l(r,c,t=>{var o;return{containers:[...(o=t.containers)!=null?o:[],n]}})}export{f as useScrollLock};\n"], "names": [], "mappings": ";;;AAAA;AAA+F;;;AAAsD,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,IAAE,IAAI;QAAC,SAAS,IAAI;KAAC;IAAE,IAAI,IAAE,CAAA,GAAA,mLAAA,CAAA,gBAAC,AAAD,EAAE,GAAE;IAAe,CAAA,GAAA,6MAAA,CAAA,kCAAC,AAAD,EAAE,GAAE,GAAE,CAAA;QAAI,IAAI;QAAE,OAAM;YAAC,YAAW;mBAAI,CAAC,IAAE,EAAE,UAAU,KAAG,OAAK,IAAE,EAAE;gBAAC;aAAE;QAAA;IAAC;AAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1510, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/hooks/use-server-handoff-complete.js"], "sourcesContent": ["import*as t from\"react\";import{env as f}from'../utils/env.js';function s(){let r=typeof document==\"undefined\";return\"useSyncExternalStore\"in t?(o=>o.useSyncExternalStore)(t)(()=>()=>{},()=>!1,()=>!r):!1}function l(){let r=s(),[e,n]=t.useState(f.isHandoffComplete);return e&&f.isHandoffComplete===!1&&n(!1),t.useEffect(()=>{e!==!0&&n(!0)},[e]),t.useEffect(()=>f.handoff(),[]),r?!1:e}export{l as useServerHandoffComplete};\n"], "names": [], "mappings": ";;;AAAA;AAAwB;;;AAAsC,SAAS;IAAI,IAAI,IAAE,OAAO,YAAU;IAAY,OAAM,0BAAyB,wMAAE,CAAC,CAAA,IAAG,EAAE,oBAAoB,EAAE,uMAAG,IAAI,KAAK,GAAE,IAAI,CAAC,GAAE,IAAI,CAAC,KAAG,CAAC;AAAC;AAAC,SAAS;IAAI,IAAI,IAAE,KAAI,CAAC,GAAE,EAAE,GAAC,sMAAE,QAAQ,CAAC,6JAAA,CAAA,MAAC,CAAC,iBAAiB;IAAE,OAAO,KAAG,6JAAA,CAAA,MAAC,CAAC,iBAAiB,KAAG,CAAC,KAAG,EAAE,CAAC,IAAG,sMAAE,SAAS,CAAC;QAAK,MAAI,CAAC,KAAG,EAAE,CAAC;IAAE,GAAE;QAAC;KAAE,GAAE,sMAAE,SAAS,CAAC,IAAI,6JAAA,CAAA,MAAC,CAAC,OAAO,IAAG,EAAE,GAAE,IAAE,CAAC,IAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1534, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/hooks/use-sync-refs.js"], "sourcesContent": ["import{useEffect as l,useRef as i}from\"react\";import{useEvent as r}from'./use-event.js';let u=Symbol();function T(t,n=!0){return Object.assign(t,{[u]:n})}function y(...t){let n=i(t);l(()=>{n.current=t},[t]);let c=r(e=>{for(let o of n.current)o!=null&&(typeof o==\"function\"?o(e):o.current=e)});return t.every(e=>e==null||(e==null?void 0:e[u]))?void 0:c}export{T as optionalRef,y as useSyncRefs};\n"], "names": [], "mappings": ";;;;AAAA;AAA8C;;;AAA0C,IAAI,IAAE;AAAS,SAAS,EAAE,CAAC,EAAC,IAAE,CAAC,CAAC;IAAE,OAAO,OAAO,MAAM,CAAC,GAAE;QAAC,CAAC,EAAE,EAAC;IAAC;AAAE;AAAC,SAAS,EAAE,GAAG,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,SAAC,AAAD,EAAE;IAAG,CAAA,GAAA,qMAAA,CAAA,YAAC,AAAD,EAAE;QAAK,EAAE,OAAO,GAAC;IAAC,GAAE;QAAC;KAAE;IAAE,IAAI,IAAE,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,KAAI,IAAI,KAAK,EAAE,OAAO,CAAC,KAAG,QAAM,CAAC,OAAO,KAAG,aAAW,EAAE,KAAG,EAAE,OAAO,GAAC,CAAC;IAAC;IAAG,OAAO,EAAE,KAAK,CAAC,CAAA,IAAG,KAAG,QAAM,CAAC,KAAG,OAAK,KAAK,IAAE,CAAC,CAAC,EAAE,KAAG,KAAK,IAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1565, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/internal/close-provider.js"], "sourcesContent": ["\"use client\";import r,{createContext as n,useContext as i}from\"react\";let e=n(()=>{});function u(){return i(e)}function C({value:t,children:o}){return r.createElement(e.Provider,{value:t},o)}export{C as CloseProvider,u as useClose};\n"], "names": [], "mappings": ";;;;AAAa;AAAb;;AAAsE,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,gBAAC,AAAD,EAAE,KAAK;AAAG,SAAS;IAAI,OAAO,CAAA,GAAA,qMAAA,CAAA,aAAC,AAAD,EAAE;AAAE;AAAC,SAAS,EAAE,EAAC,OAAM,CAAC,EAAC,UAAS,CAAC,EAAC;IAAE,OAAO,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAC;QAAC,OAAM;IAAC,GAAE;AAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1586, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/internal/open-closed.js"], "sourcesContent": ["import r,{createContext as l,useContext as d}from\"react\";let n=l(null);n.displayName=\"OpenClosedContext\";var i=(e=>(e[e.Open=1]=\"Open\",e[e.Closed=2]=\"Closed\",e[e.Closing=4]=\"Closing\",e[e.Opening=8]=\"Opening\",e))(i||{});function u(){return d(n)}function c({value:o,children:t}){return r.createElement(n.Provider,{value:o},t)}function s({children:o}){return r.createElement(n.Provider,{value:null},o)}export{c as OpenClosedProvider,s as ResetOpenClosedProvider,i as State,u as useOpenClosed};\n"], "names": [], "mappings": ";;;;;;AAAA;;AAAyD,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,gBAAC,AAAD,EAAE;AAAM,EAAE,WAAW,GAAC;AAAoB,IAAI,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,IAAI,GAAC,EAAE,GAAC,QAAO,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE,GAAC,UAAS,CAAC,CAAC,EAAE,OAAO,GAAC,EAAE,GAAC,WAAU,CAAC,CAAC,EAAE,OAAO,GAAC,EAAE,GAAC,WAAU,CAAC,CAAC,EAAE,KAAG,CAAC;AAAG,SAAS;IAAI,OAAO,CAAA,GAAA,qMAAA,CAAA,aAAC,AAAD,EAAE;AAAE;AAAC,SAAS,EAAE,EAAC,OAAM,CAAC,EAAC,UAAS,CAAC,EAAC;IAAE,OAAO,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAC;QAAC,OAAM;IAAC,GAAE;AAAE;AAAC,SAAS,EAAE,EAAC,UAAS,CAAC,EAAC;IAAE,OAAO,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAC;QAAC,OAAM;IAAI,GAAE;AAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1615, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/internal/portal-force-root.js"], "sourcesContent": ["import t,{createContext as r,useContext as c}from\"react\";let e=r(!1);function a(){return c(e)}function l(o){return t.createElement(e.Provider,{value:o.force},o.children)}export{l as ForcePortalRoot,a as usePortalRoot};\n"], "names": [], "mappings": ";;;;AAAA;;AAAyD,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,gBAAC,AAAD,EAAE,CAAC;AAAG,SAAS;IAAI,OAAO,CAAA,GAAA,qMAAA,CAAA,aAAC,AAAD,EAAE;AAAE;AAAC,SAAS,EAAE,CAAC;IAAE,OAAO,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAC;QAAC,OAAM,EAAE,KAAK;IAAA,GAAE,EAAE,QAAQ;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1635, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/internal/disabled.js"], "sourcesContent": ["import n,{createContext as r,useContext as i}from\"react\";let e=r(void 0);function a(){return i(e)}function l({value:t,children:o}){return n.createElement(e.Provider,{value:t},o)}export{l as DisabledProvider,a as useDisabled};\n"], "names": [], "mappings": ";;;;AAAA;;AAAyD,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,gBAAC,AAAD,EAAE,KAAK;AAAG,SAAS;IAAI,OAAO,CAAA,GAAA,qMAAA,CAAA,aAAC,AAAD,EAAE;AAAE;AAAC,SAAS,EAAE,EAAC,OAAM,CAAC,EAAC,UAAS,CAAC,EAAC;IAAE,OAAO,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAC;QAAC,OAAM;IAAC,GAAE;AAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1655, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/components/description/description.js"], "sourcesContent": ["\"use client\";import m,{createContext as T,useContext as u,useMemo as c,useState as P}from\"react\";import{useEvent as g}from'../../hooks/use-event.js';import{useId as x}from'../../hooks/use-id.js';import{useIsoMorphicEffect as y}from'../../hooks/use-iso-morphic-effect.js';import{useSyncRefs as E}from'../../hooks/use-sync-refs.js';import{useDisabled as v}from'../../internal/disabled.js';import{forwardRefWithAs as R,useRender as I}from'../../utils/render.js';let a=T(null);a.displayName=\"DescriptionContext\";function f(){let r=u(a);if(r===null){let e=new Error(\"You used a <Description /> component, but it is not inside a relevant parent.\");throw Error.captureStackTrace&&Error.captureStackTrace(e,f),e}return r}function U(){var r,e;return(e=(r=u(a))==null?void 0:r.value)!=null?e:void 0}function w(){let[r,e]=P([]);return[r.length>0?r.join(\" \"):void 0,c(()=>function(t){let i=g(n=>(e(s=>[...s,n]),()=>e(s=>{let o=s.slice(),p=o.indexOf(n);return p!==-1&&o.splice(p,1),o}))),l=c(()=>({register:i,slot:t.slot,name:t.name,props:t.props,value:t.value}),[i,t.slot,t.name,t.props,t.value]);return m.createElement(a.Provider,{value:l},t.children)},[e])]}let S=\"p\";function C(r,e){let d=x(),t=v(),{id:i=`headlessui-description-${d}`,...l}=r,n=f(),s=E(e);y(()=>n.register(i),[i,n.register]);let o=t||!1,p=c(()=>({...n.slot,disabled:o}),[n.slot,o]),D={ref:s,...n.props,id:i};return I()({ourProps:D,theirProps:l,slot:p,defaultTag:S,name:n.name||\"Description\"})}let _=R(C),H=Object.assign(_,{});export{H as Description,U as useDescribedBy,w as useDescriptions};\n"], "names": [], "mappings": ";;;;;AAAa;AAAoF;AAAkG;AAA4E;AAA2D;AAAyD;AAAnY;;;;;;;;AAA2c,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,gBAAC,AAAD,EAAE;AAAM,EAAE,WAAW,GAAC;AAAqB,SAAS;IAAI,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,aAAC,AAAD,EAAE;IAAG,IAAG,MAAI,MAAK;QAAC,IAAI,IAAE,IAAI,MAAM;QAAiF,MAAM,MAAM,iBAAiB,IAAE,MAAM,iBAAiB,CAAC,GAAE,IAAG;IAAC;IAAC,OAAO;AAAC;AAAC,SAAS;IAAI,IAAI,GAAE;IAAE,OAAM,CAAC,IAAE,CAAC,IAAE,CAAA,GAAA,qMAAA,CAAA,aAAC,AAAD,EAAE,EAAE,KAAG,OAAK,KAAK,IAAE,EAAE,KAAK,KAAG,OAAK,IAAE,KAAK;AAAC;AAAC,SAAS;IAAI,IAAG,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,qMAAA,CAAA,WAAC,AAAD,EAAE,EAAE;IAAE,OAAM;QAAC,EAAE,MAAM,GAAC,IAAE,EAAE,IAAI,CAAC,OAAK,KAAK;QAAE,CAAA,GAAA,qMAAA,CAAA,UAAC,AAAD,EAAE,IAAI,SAAS,CAAC;gBAAE,IAAI,IAAE,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE,CAAA,IAAG,CAAC,EAAE,CAAA,IAAG;+BAAI;4BAAE;yBAAE,GAAE,IAAI,EAAE,CAAA;4BAAI,IAAI,IAAE,EAAE,KAAK,IAAG,IAAE,EAAE,OAAO,CAAC;4BAAG,OAAO,MAAI,CAAC,KAAG,EAAE,MAAM,CAAC,GAAE,IAAG;wBAAC,EAAE,IAAG,IAAE,CAAA,GAAA,qMAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC;wBAAC,UAAS;wBAAE,MAAK,EAAE,IAAI;wBAAC,MAAK,EAAE,IAAI;wBAAC,OAAM,EAAE,KAAK;wBAAC,OAAM,EAAE,KAAK;oBAAA,CAAC,GAAE;oBAAC;oBAAE,EAAE,IAAI;oBAAC,EAAE,IAAI;oBAAC,EAAE,KAAK;oBAAC,EAAE,KAAK;iBAAC;gBAAE,OAAO,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAC;oBAAC,OAAM;gBAAC,GAAE,EAAE,QAAQ;YAAC,GAAE;YAAC;SAAE;KAAE;AAAA;AAAC,IAAI,IAAE;AAAI,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,QAAC,AAAD,KAAI,IAAE,CAAA,GAAA,qKAAA,CAAA,cAAC,AAAD,KAAI,EAAC,IAAG,IAAE,CAAC,uBAAuB,EAAE,GAAG,EAAC,GAAG,GAAE,GAAC,GAAE,IAAE,KAAI,IAAE,CAAA,GAAA,6KAAA,CAAA,cAAC,AAAD,EAAE;IAAG,CAAA,GAAA,yLAAA,CAAA,sBAAC,AAAD,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAG;QAAC;QAAE,EAAE,QAAQ;KAAC;IAAE,IAAI,IAAE,KAAG,CAAC,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC;YAAC,GAAG,EAAE,IAAI;YAAC,UAAS;QAAC,CAAC,GAAE;QAAC,EAAE,IAAI;QAAC;KAAE,GAAE,IAAE;QAAC,KAAI;QAAE,GAAG,EAAE,KAAK;QAAC,IAAG;IAAC;IAAE,OAAO,CAAA,GAAA,gKAAA,CAAA,YAAC,AAAD,IAAI;QAAC,UAAS;QAAE,YAAW;QAAE,MAAK;QAAE,YAAW;QAAE,MAAK,EAAE,IAAI,IAAE;IAAa;AAAE;AAAC,IAAI,IAAE,CAAA,GAAA,gKAAA,CAAA,mBAAC,AAAD,EAAE,IAAG,IAAE,OAAO,MAAM,CAAC,GAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1752, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/hooks/use-disposables.js"], "sourcesContent": ["import{useEffect as s,useState as o}from\"react\";import{disposables as t}from'../utils/disposables.js';function p(){let[e]=o(t);return s(()=>()=>e.dispose(),[e]),e}export{p as useDisposables};\n"], "names": [], "mappings": ";;;AAAA;AAAgD;;;AAAsD,SAAS;IAAI,IAAG,CAAC,EAAE,GAAC,CAAA,GAAA,qMAAA,CAAA,WAAC,AAAD,EAAE,qKAAA,CAAA,cAAC;IAAE,OAAO,CAAA,GAAA,qMAAA,CAAA,YAAC,AAAD,EAAE,IAAI,IAAI,EAAE,OAAO,IAAG;QAAC;KAAE,GAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1770, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/hooks/use-is-mounted.js"], "sourcesContent": ["import{useRef as r}from\"react\";import{useIsoMorphicEffect as t}from'./use-iso-morphic-effect.js';function f(){let e=r(!1);return t(()=>(e.current=!0,()=>{e.current=!1}),[]),e}export{f as useIsMounted};\n"], "names": [], "mappings": ";;;AAAA;AAA+B;;;AAAkE,SAAS;IAAI,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,SAAC,AAAD,EAAE,CAAC;IAAG,OAAO,CAAA,GAAA,yLAAA,CAAA,sBAAC,AAAD,EAAE,IAAI,CAAC,EAAE,OAAO,GAAC,CAAC,GAAE;YAAK,EAAE,OAAO,GAAC,CAAC;QAAC,CAAC,GAAE,EAAE,GAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1788, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/hooks/use-on-unmount.js"], "sourcesContent": ["import{useEffect as u,useRef as n}from\"react\";import{microTask as o}from'../utils/micro-task.js';import{useEvent as f}from'./use-event.js';function c(t){let r=f(t),e=n(!1);u(()=>(e.current=!1,()=>{e.current=!0,o(()=>{e.current&&r()})}),[r])}export{c as useOnUnmount};\n"], "names": [], "mappings": ";;;AAAA;AAA8C;AAAmD;;;;AAA0C,SAAS,EAAE,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE,IAAG,IAAE,CAAA,GAAA,qMAAA,CAAA,SAAC,AAAD,EAAE,CAAC;IAAG,CAAA,GAAA,qMAAA,CAAA,YAAC,AAAD,EAAE,IAAI,CAAC,EAAE,OAAO,GAAC,CAAC,GAAE;YAAK,EAAE,OAAO,GAAC,CAAC,GAAE,CAAA,GAAA,uKAAA,CAAA,YAAC,AAAD,EAAE;gBAAK,EAAE,OAAO,IAAE;YAAG;QAAE,CAAC,GAAE;QAAC;KAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1812, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/hooks/use-tab-direction.js"], "sourcesContent": ["import{useRef as o}from\"react\";import{useWindowEvent as t}from'./use-window-event.js';var a=(r=>(r[r.Forwards=0]=\"Forwards\",r[r.Backwards=1]=\"Backwards\",r))(a||{});function u(){let e=o(0);return t(!0,\"keydown\",r=>{r.key===\"Tab\"&&(e.current=r.shiftKey?1:0)},!0),e}export{a as Direction,u as useTabDirection};\n"], "names": [], "mappings": ";;;;AAAA;AAA+B;;;AAAuD,IAAI,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,QAAQ,GAAC,EAAE,GAAC,YAAW,CAAC,CAAC,EAAE,SAAS,GAAC,EAAE,GAAC,aAAY,CAAC,CAAC,EAAE,KAAG,CAAC;AAAG,SAAS;IAAI,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,SAAC,AAAD,EAAE;IAAG,OAAO,CAAA,GAAA,gLAAA,CAAA,iBAAC,AAAD,EAAE,CAAC,GAAE,WAAU,CAAA;QAAI,EAAE,GAAG,KAAG,SAAO,CAAC,EAAE,OAAO,GAAC,EAAE,QAAQ,GAAC,IAAE,CAAC;IAAC,GAAE,CAAC,IAAG;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1832, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/hooks/use-watch.js"], "sourcesContent": ["import{useEffect as f,useRef as s}from\"react\";import{useEvent as i}from'./use-event.js';function m(u,t){let e=s([]),r=i(u);f(()=>{let o=[...e.current];for(let[a,l]of t.entries())if(e.current[a]!==l){let n=r(t,o);return e.current=t,n}},[r,...t])}export{m as useWatch};\n"], "names": [], "mappings": ";;;AAAA;AAA8C;;;AAA0C,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,SAAC,AAAD,EAAE,EAAE,GAAE,IAAE,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE;IAAG,CAAA,GAAA,qMAAA,CAAA,YAAC,AAAD,EAAE;QAAK,IAAI,IAAE;eAAI,EAAE,OAAO;SAAC;QAAC,KAAI,IAAG,CAAC,GAAE,EAAE,IAAG,EAAE,OAAO,GAAG,IAAG,EAAE,OAAO,CAAC,EAAE,KAAG,GAAE;YAAC,IAAI,IAAE,EAAE,GAAE;YAAG,OAAO,EAAE,OAAO,GAAC,GAAE;QAAC;IAAC,GAAE;QAAC;WAAK;KAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1859, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/utils/document-ready.js"], "sourcesContent": ["function t(n){function e(){document.readyState!==\"loading\"&&(n(),document.removeEventListener(\"DOMContentLoaded\",e))}typeof window!=\"undefined\"&&typeof document!=\"undefined\"&&(document.addEventListener(\"DOMContentLoaded\",e),e())}export{t as onDocumentReady};\n"], "names": [], "mappings": ";;;AAAA,SAAS,EAAE,CAAC;IAAE,SAAS;QAAI,SAAS,UAAU,KAAG,aAAW,CAAC,KAAI,SAAS,mBAAmB,CAAC,oBAAmB,EAAE;IAAC;IAAC,eAAe,eAAa,OAAO,YAAU,eAAa,CAAC,SAAS,gBAAgB,CAAC,oBAAmB,IAAG,GAAG;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1873, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/utils/active-element-history.js"], "sourcesContent": ["import{onDocumentReady as d}from'./document-ready.js';import*as u from'./dom.js';import{focusableSelector as i}from'./focus-management.js';let n=[];d(()=>{function e(t){if(!u.isHTMLorSVGElement(t.target)||t.target===document.body||n[0]===t.target)return;let r=t.target;r=r.closest(i),n.unshift(r!=null?r:t.target),n=n.filter(o=>o!=null&&o.isConnected),n.splice(10)}window.addEventListener(\"click\",e,{capture:!0}),window.addEventListener(\"mousedown\",e,{capture:!0}),window.addEventListener(\"focus\",e,{capture:!0}),document.body.addEventListener(\"click\",e,{capture:!0}),document.body.addEventListener(\"mousedown\",e,{capture:!0}),document.body.addEventListener(\"focus\",e,{capture:!0})});export{n as history};\n"], "names": [], "mappings": ";;;AAAA;AAAsD;AAA2B;;;;AAA0D,IAAI,IAAE,EAAE;AAAC,CAAA,GAAA,2KAAA,CAAA,kBAAC,AAAD,EAAE;IAAK,SAAS,EAAE,CAAC;QAAE,IAAG,CAAC,6JAAA,CAAA,qBAAoB,CAAC,EAAE,MAAM,KAAG,EAAE,MAAM,KAAG,SAAS,IAAI,IAAE,CAAC,CAAC,EAAE,KAAG,EAAE,MAAM,EAAC;QAAO,IAAI,IAAE,EAAE,MAAM;QAAC,IAAE,EAAE,OAAO,CAAC,6KAAA,CAAA,oBAAC,GAAE,EAAE,OAAO,CAAC,KAAG,OAAK,IAAE,EAAE,MAAM,GAAE,IAAE,EAAE,MAAM,CAAC,CAAA,IAAG,KAAG,QAAM,EAAE,WAAW,GAAE,EAAE,MAAM,CAAC;IAAG;IAAC,OAAO,gBAAgB,CAAC,SAAQ,GAAE;QAAC,SAAQ,CAAC;IAAC,IAAG,OAAO,gBAAgB,CAAC,aAAY,GAAE;QAAC,SAAQ,CAAC;IAAC,IAAG,OAAO,gBAAgB,CAAC,SAAQ,GAAE;QAAC,SAAQ,CAAC;IAAC,IAAG,SAAS,IAAI,CAAC,gBAAgB,CAAC,SAAQ,GAAE;QAAC,SAAQ,CAAC;IAAC,IAAG,SAAS,IAAI,CAAC,gBAAgB,CAAC,aAAY,GAAE;QAAC,SAAQ,CAAC;IAAC,IAAG,SAAS,IAAI,CAAC,gBAAgB,CAAC,SAAQ,GAAE;QAAC,SAAQ,CAAC;IAAC;AAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1908, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/components/focus-trap/focus-trap.js"], "sourcesContent": ["\"use client\";import F,{useRef as M}from\"react\";import{useDisposables as W}from'../../hooks/use-disposables.js';import{useEvent as O}from'../../hooks/use-event.js';import{useEventListener as K}from'../../hooks/use-event-listener.js';import{useIsMounted as P}from'../../hooks/use-is-mounted.js';import{useIsTopLayer as C}from'../../hooks/use-is-top-layer.js';import{useOnUnmount as q}from'../../hooks/use-on-unmount.js';import{useOwnerDocument as J}from'../../hooks/use-owner.js';import{useServerHandoffComplete as X}from'../../hooks/use-server-handoff-complete.js';import{useSyncRefs as z}from'../../hooks/use-sync-refs.js';import{Direction as y,useTabDirection as Q}from'../../hooks/use-tab-direction.js';import{useWatch as R}from'../../hooks/use-watch.js';import{Hidden as _,HiddenFeatures as S}from'../../internal/hidden.js';import{history as H}from'../../utils/active-element-history.js';import*as T from'../../utils/dom.js';import{Focus as i,FocusResult as h,focusElement as p,focusIn as d}from'../../utils/focus-management.js';import{match as j}from'../../utils/match.js';import{microTask as U}from'../../utils/micro-task.js';import{forwardRefWithAs as Y,useRender as Z}from'../../utils/render.js';function x(s){if(!s)return new Set;if(typeof s==\"function\")return new Set(s());let e=new Set;for(let t of s.current)T.isElement(t.current)&&e.add(t.current);return e}let $=\"div\";var G=(n=>(n[n.None=0]=\"None\",n[n.InitialFocus=1]=\"InitialFocus\",n[n.TabLock=2]=\"TabLock\",n[n.FocusLock=4]=\"FocusLock\",n[n.RestoreFocus=8]=\"RestoreFocus\",n[n.AutoFocus=16]=\"AutoFocus\",n))(G||{});function D(s,e){let t=M(null),r=z(t,e),{initialFocus:o,initialFocusFallback:a,containers:n,features:u=15,...f}=s;X()||(u=0);let l=J(t);te(u,{ownerDocument:l});let m=re(u,{ownerDocument:l,container:t,initialFocus:o,initialFocusFallback:a});ne(u,{ownerDocument:l,container:t,containers:n,previousActiveElement:m});let g=Q(),v=O(c=>{if(!T.isHTMLElement(t.current))return;let E=t.current;(V=>V())(()=>{j(g.current,{[y.Forwards]:()=>{d(E,i.First,{skipElements:[c.relatedTarget,a]})},[y.Backwards]:()=>{d(E,i.Last,{skipElements:[c.relatedTarget,a]})}})})}),A=C(!!(u&2),\"focus-trap#tab-lock\"),N=W(),b=M(!1),k={ref:r,onKeyDown(c){c.key==\"Tab\"&&(b.current=!0,N.requestAnimationFrame(()=>{b.current=!1}))},onBlur(c){if(!(u&4))return;let E=x(n);T.isHTMLElement(t.current)&&E.add(t.current);let L=c.relatedTarget;T.isHTMLorSVGElement(L)&&L.dataset.headlessuiFocusGuard!==\"true\"&&(I(E,L)||(b.current?d(t.current,j(g.current,{[y.Forwards]:()=>i.Next,[y.Backwards]:()=>i.Previous})|i.WrapAround,{relativeTo:c.target}):T.isHTMLorSVGElement(c.target)&&p(c.target)))}},B=Z();return F.createElement(F.Fragment,null,A&&F.createElement(_,{as:\"button\",type:\"button\",\"data-headlessui-focus-guard\":!0,onFocus:v,features:S.Focusable}),B({ourProps:k,theirProps:f,defaultTag:$,name:\"FocusTrap\"}),A&&F.createElement(_,{as:\"button\",type:\"button\",\"data-headlessui-focus-guard\":!0,onFocus:v,features:S.Focusable}))}let w=Y(D),Re=Object.assign(w,{features:G});function ee(s=!0){let e=M(H.slice());return R(([t],[r])=>{r===!0&&t===!1&&U(()=>{e.current.splice(0)}),r===!1&&t===!0&&(e.current=H.slice())},[s,H,e]),O(()=>{var t;return(t=e.current.find(r=>r!=null&&r.isConnected))!=null?t:null})}function te(s,{ownerDocument:e}){let t=!!(s&8),r=ee(t);R(()=>{t||(e==null?void 0:e.activeElement)===(e==null?void 0:e.body)&&p(r())},[t]),q(()=>{t&&p(r())})}function re(s,{ownerDocument:e,container:t,initialFocus:r,initialFocusFallback:o}){let a=M(null),n=C(!!(s&1),\"focus-trap#initial-focus\"),u=P();return R(()=>{if(s===0)return;if(!n){o!=null&&o.current&&p(o.current);return}let f=t.current;f&&U(()=>{if(!u.current)return;let l=e==null?void 0:e.activeElement;if(r!=null&&r.current){if((r==null?void 0:r.current)===l){a.current=l;return}}else if(f.contains(l)){a.current=l;return}if(r!=null&&r.current)p(r.current);else{if(s&16){if(d(f,i.First|i.AutoFocus)!==h.Error)return}else if(d(f,i.First)!==h.Error)return;if(o!=null&&o.current&&(p(o.current),(e==null?void 0:e.activeElement)===o.current))return;console.warn(\"There are no focusable elements inside the <FocusTrap />\")}a.current=e==null?void 0:e.activeElement})},[o,n,s]),a}function ne(s,{ownerDocument:e,container:t,containers:r,previousActiveElement:o}){let a=P(),n=!!(s&4);K(e==null?void 0:e.defaultView,\"focus\",u=>{if(!n||!a.current)return;let f=x(r);T.isHTMLElement(t.current)&&f.add(t.current);let l=o.current;if(!l)return;let m=u.target;T.isHTMLElement(m)?I(f,m)?(o.current=m,p(m)):(u.preventDefault(),u.stopPropagation(),p(l)):p(o.current)},!0)}function I(s,e){for(let t of s)if(t.contains(e))return!0;return!1}export{Re as FocusTrap,G as FocusTrapFeatures};\n"], "names": [], "mappings": ";;;;AAAa;AAAkC;AAAgE;AAAoD;AAAqE;AAA6D;AAAgE;AAA6D;AAA4D;AAAsF;AAA2D;AAAkF;AAAoD;AAAsE;AAAgE;AAAqC;AAAwG;AAA6C;AAAsD;AAA3mC;;;;;;;;;;;;;;;;;;;;AAAmrC,SAAS,EAAE,CAAC;IAAE,IAAG,CAAC,GAAE,OAAO,IAAI;IAAI,IAAG,OAAO,KAAG,YAAW,OAAO,IAAI,IAAI;IAAK,IAAI,IAAE,IAAI;IAAI,KAAI,IAAI,KAAK,EAAE,OAAO,CAAC,6JAAA,CAAA,YAAW,CAAC,EAAE,OAAO,KAAG,EAAE,GAAG,CAAC,EAAE,OAAO;IAAE,OAAO;AAAC;AAAC,IAAI,IAAE;AAAM,IAAI,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,IAAI,GAAC,EAAE,GAAC,QAAO,CAAC,CAAC,EAAE,YAAY,GAAC,EAAE,GAAC,gBAAe,CAAC,CAAC,EAAE,OAAO,GAAC,EAAE,GAAC,WAAU,CAAC,CAAC,EAAE,SAAS,GAAC,EAAE,GAAC,aAAY,CAAC,CAAC,EAAE,YAAY,GAAC,EAAE,GAAC,gBAAe,CAAC,CAAC,EAAE,SAAS,GAAC,GAAG,GAAC,aAAY,CAAC,CAAC,EAAE,KAAG,CAAC;AAAG,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,SAAC,AAAD,EAAE,OAAM,IAAE,CAAA,GAAA,6KAAA,CAAA,cAAC,AAAD,EAAE,GAAE,IAAG,EAAC,cAAa,CAAC,EAAC,sBAAqB,CAAC,EAAC,YAAW,CAAC,EAAC,UAAS,IAAE,EAAE,EAAC,GAAG,GAAE,GAAC;IAAE,CAAA,GAAA,8LAAA,CAAA,2BAAC,AAAD,OAAK,CAAC,IAAE,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,sKAAA,CAAA,mBAAC,AAAD,EAAE;IAAG,GAAG,GAAE;QAAC,eAAc;IAAC;IAAG,IAAI,IAAE,GAAG,GAAE;QAAC,eAAc;QAAE,WAAU;QAAE,cAAa;QAAE,sBAAqB;IAAC;IAAG,GAAG,GAAE;QAAC,eAAc;QAAE,WAAU;QAAE,YAAW;QAAE,uBAAsB;IAAC;IAAG,IAAI,IAAE,CAAA,GAAA,iLAAA,CAAA,kBAAC,AAAD,KAAI,IAAE,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,IAAG,CAAC,6JAAA,CAAA,gBAAe,CAAC,EAAE,OAAO,GAAE;QAAO,IAAI,IAAE,EAAE,OAAO;QAAC,CAAC,CAAA,IAAG,GAAG,EAAE;YAAK,CAAA,GAAA,+JAAA,CAAA,QAAC,AAAD,EAAE,EAAE,OAAO,EAAC;gBAAC,CAAC,iLAAA,CAAA,YAAC,CAAC,QAAQ,CAAC,EAAC;oBAAK,CAAA,GAAA,6KAAA,CAAA,UAAC,AAAD,EAAE,GAAE,6KAAA,CAAA,QAAC,CAAC,KAAK,EAAC;wBAAC,cAAa;4BAAC,EAAE,aAAa;4BAAC;yBAAE;oBAAA;gBAAE;gBAAE,CAAC,iLAAA,CAAA,YAAC,CAAC,SAAS,CAAC,EAAC;oBAAK,CAAA,GAAA,6KAAA,CAAA,UAAC,AAAD,EAAE,GAAE,6KAAA,CAAA,QAAC,CAAC,IAAI,EAAC;wBAAC,cAAa;4BAAC,EAAE,aAAa;4BAAC;yBAAE;oBAAA;gBAAE;YAAC;QAAE;IAAE,IAAG,IAAE,CAAA,GAAA,mLAAA,CAAA,gBAAC,AAAD,EAAE,CAAC,CAAC,CAAC,IAAE,CAAC,GAAE,wBAAuB,IAAE,CAAA,GAAA,4KAAA,CAAA,iBAAC,AAAD,KAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,SAAC,AAAD,EAAE,CAAC,IAAG,IAAE;QAAC,KAAI;QAAE,WAAU,CAAC;YAAE,EAAE,GAAG,IAAE,SAAO,CAAC,EAAE,OAAO,GAAC,CAAC,GAAE,EAAE,qBAAqB,CAAC;gBAAK,EAAE,OAAO,GAAC,CAAC;YAAC,EAAE;QAAC;QAAE,QAAO,CAAC;YAAE,IAAG,CAAC,CAAC,IAAE,CAAC,GAAE;YAAO,IAAI,IAAE,EAAE;YAAG,6JAAA,CAAA,gBAAe,CAAC,EAAE,OAAO,KAAG,EAAE,GAAG,CAAC,EAAE,OAAO;YAAE,IAAI,IAAE,EAAE,aAAa;YAAC,6JAAA,CAAA,qBAAoB,CAAC,MAAI,EAAE,OAAO,CAAC,oBAAoB,KAAG,UAAQ,CAAC,EAAE,GAAE,MAAI,CAAC,EAAE,OAAO,GAAC,CAAA,GAAA,6KAAA,CAAA,UAAC,AAAD,EAAE,EAAE,OAAO,EAAC,CAAA,GAAA,+JAAA,CAAA,QAAC,AAAD,EAAE,EAAE,OAAO,EAAC;gBAAC,CAAC,iLAAA,CAAA,YAAC,CAAC,QAAQ,CAAC,EAAC,IAAI,6KAAA,CAAA,QAAC,CAAC,IAAI;gBAAC,CAAC,iLAAA,CAAA,YAAC,CAAC,SAAS,CAAC,EAAC,IAAI,6KAAA,CAAA,QAAC,CAAC,QAAQ;YAAA,KAAG,6KAAA,CAAA,QAAC,CAAC,UAAU,EAAC;gBAAC,YAAW,EAAE,MAAM;YAAA,KAAG,6JAAA,CAAA,qBAAoB,CAAC,EAAE,MAAM,KAAG,CAAA,GAAA,6KAAA,CAAA,eAAC,AAAD,EAAE,EAAE,MAAM,CAAC,CAAC;QAAC;IAAC,GAAE,IAAE,CAAA,GAAA,gKAAA,CAAA,YAAC,AAAD;IAAI,OAAO,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,qMAAA,CAAA,UAAC,CAAC,QAAQ,EAAC,MAAK,KAAG,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,mKAAA,CAAA,SAAC,EAAC;QAAC,IAAG;QAAS,MAAK;QAAS,+BAA8B,CAAC;QAAE,SAAQ;QAAE,UAAS,mKAAA,CAAA,iBAAC,CAAC,SAAS;IAAA,IAAG,EAAE;QAAC,UAAS;QAAE,YAAW;QAAE,YAAW;QAAE,MAAK;IAAW,IAAG,KAAG,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,mKAAA,CAAA,SAAC,EAAC;QAAC,IAAG;QAAS,MAAK;QAAS,+BAA8B,CAAC;QAAE,SAAQ;QAAE,UAAS,mKAAA,CAAA,iBAAC,CAAC,SAAS;IAAA;AAAG;AAAC,IAAI,IAAE,CAAA,GAAA,gKAAA,CAAA,mBAAC,AAAD,EAAE,IAAG,KAAG,OAAO,MAAM,CAAC,GAAE;IAAC,UAAS;AAAC;AAAG,SAAS,GAAG,IAAE,CAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,SAAC,AAAD,EAAE,sLAAA,CAAA,UAAC,CAAC,KAAK;IAAI,OAAO,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE,CAAC,CAAC,EAAE,EAAC,CAAC,EAAE;QAAI,MAAI,CAAC,KAAG,MAAI,CAAC,KAAG,CAAA,GAAA,uKAAA,CAAA,YAAC,AAAD,EAAE;YAAK,EAAE,OAAO,CAAC,MAAM,CAAC;QAAE,IAAG,MAAI,CAAC,KAAG,MAAI,CAAC,KAAG,CAAC,EAAE,OAAO,GAAC,sLAAA,CAAA,UAAC,CAAC,KAAK,EAAE;IAAC,GAAE;QAAC;QAAE,sLAAA,CAAA,UAAC;QAAC;KAAE,GAAE,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE;QAAK,IAAI;QAAE,OAAM,CAAC,IAAE,EAAE,OAAO,CAAC,IAAI,CAAC,CAAA,IAAG,KAAG,QAAM,EAAE,WAAW,CAAC,KAAG,OAAK,IAAE;IAAI;AAAE;AAAC,SAAS,GAAG,CAAC,EAAC,EAAC,eAAc,CAAC,EAAC;IAAE,IAAI,IAAE,CAAC,CAAC,CAAC,IAAE,CAAC,GAAE,IAAE,GAAG;IAAG,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE;QAAK,KAAG,CAAC,KAAG,OAAK,KAAK,IAAE,EAAE,aAAa,MAAI,CAAC,KAAG,OAAK,KAAK,IAAE,EAAE,IAAI,KAAG,CAAA,GAAA,6KAAA,CAAA,eAAC,AAAD,EAAE;IAAI,GAAE;QAAC;KAAE,GAAE,CAAA,GAAA,8KAAA,CAAA,eAAC,AAAD,EAAE;QAAK,KAAG,CAAA,GAAA,6KAAA,CAAA,eAAC,AAAD,EAAE;IAAI;AAAE;AAAC,SAAS,GAAG,CAAC,EAAC,EAAC,eAAc,CAAC,EAAC,WAAU,CAAC,EAAC,cAAa,CAAC,EAAC,sBAAqB,CAAC,EAAC;IAAE,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,SAAC,AAAD,EAAE,OAAM,IAAE,CAAA,GAAA,mLAAA,CAAA,gBAAC,AAAD,EAAE,CAAC,CAAC,CAAC,IAAE,CAAC,GAAE,6BAA4B,IAAE,CAAA,GAAA,8KAAA,CAAA,eAAC,AAAD;IAAI,OAAO,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE;QAAK,IAAG,MAAI,GAAE;QAAO,IAAG,CAAC,GAAE;YAAC,KAAG,QAAM,EAAE,OAAO,IAAE,CAAA,GAAA,6KAAA,CAAA,eAAC,AAAD,EAAE,EAAE,OAAO;YAAE;QAAM;QAAC,IAAI,IAAE,EAAE,OAAO;QAAC,KAAG,CAAA,GAAA,uKAAA,CAAA,YAAC,AAAD,EAAE;YAAK,IAAG,CAAC,EAAE,OAAO,EAAC;YAAO,IAAI,IAAE,KAAG,OAAK,KAAK,IAAE,EAAE,aAAa;YAAC,IAAG,KAAG,QAAM,EAAE,OAAO,EAAC;gBAAC,IAAG,CAAC,KAAG,OAAK,KAAK,IAAE,EAAE,OAAO,MAAI,GAAE;oBAAC,EAAE,OAAO,GAAC;oBAAE;gBAAM;YAAC,OAAM,IAAG,EAAE,QAAQ,CAAC,IAAG;gBAAC,EAAE,OAAO,GAAC;gBAAE;YAAM;YAAC,IAAG,KAAG,QAAM,EAAE,OAAO,EAAC,CAAA,GAAA,6KAAA,CAAA,eAAC,AAAD,EAAE,EAAE,OAAO;iBAAM;gBAAC,IAAG,IAAE,IAAG;oBAAC,IAAG,CAAA,GAAA,6KAAA,CAAA,UAAC,AAAD,EAAE,GAAE,6KAAA,CAAA,QAAC,CAAC,KAAK,GAAC,6KAAA,CAAA,QAAC,CAAC,SAAS,MAAI,6KAAA,CAAA,cAAC,CAAC,KAAK,EAAC;gBAAM,OAAM,IAAG,CAAA,GAAA,6KAAA,CAAA,UAAC,AAAD,EAAE,GAAE,6KAAA,CAAA,QAAC,CAAC,KAAK,MAAI,6KAAA,CAAA,cAAC,CAAC,KAAK,EAAC;gBAAO,IAAG,KAAG,QAAM,EAAE,OAAO,IAAE,CAAC,CAAA,GAAA,6KAAA,CAAA,eAAC,AAAD,EAAE,EAAE,OAAO,GAAE,CAAC,KAAG,OAAK,KAAK,IAAE,EAAE,aAAa,MAAI,EAAE,OAAO,GAAE;gBAAO,QAAQ,IAAI,CAAC;YAA2D;YAAC,EAAE,OAAO,GAAC,KAAG,OAAK,KAAK,IAAE,EAAE,aAAa;QAAA;IAAE,GAAE;QAAC;QAAE;QAAE;KAAE,GAAE;AAAC;AAAC,SAAS,GAAG,CAAC,EAAC,EAAC,eAAc,CAAC,EAAC,WAAU,CAAC,EAAC,YAAW,CAAC,EAAC,uBAAsB,CAAC,EAAC;IAAE,IAAI,IAAE,CAAA,GAAA,8KAAA,CAAA,eAAC,AAAD,KAAI,IAAE,CAAC,CAAC,CAAC,IAAE,CAAC;IAAE,CAAA,GAAA,kLAAA,CAAA,mBAAC,AAAD,EAAE,KAAG,OAAK,KAAK,IAAE,EAAE,WAAW,EAAC,SAAQ,CAAA;QAAI,IAAG,CAAC,KAAG,CAAC,EAAE,OAAO,EAAC;QAAO,IAAI,IAAE,EAAE;QAAG,6JAAA,CAAA,gBAAe,CAAC,EAAE,OAAO,KAAG,EAAE,GAAG,CAAC,EAAE,OAAO;QAAE,IAAI,IAAE,EAAE,OAAO;QAAC,IAAG,CAAC,GAAE;QAAO,IAAI,IAAE,EAAE,MAAM;QAAC,6JAAA,CAAA,gBAAe,CAAC,KAAG,EAAE,GAAE,KAAG,CAAC,EAAE,OAAO,GAAC,GAAE,CAAA,GAAA,6KAAA,CAAA,eAAC,AAAD,EAAE,EAAE,IAAE,CAAC,EAAE,cAAc,IAAG,EAAE,eAAe,IAAG,CAAA,GAAA,6KAAA,CAAA,eAAC,AAAD,EAAE,EAAE,IAAE,CAAA,GAAA,6KAAA,CAAA,eAAC,AAAD,EAAE,EAAE,OAAO;IAAC,GAAE,CAAC;AAAE;AAAC,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,KAAI,IAAI,KAAK,EAAE,IAAG,EAAE,QAAQ,CAAC,IAAG,OAAM,CAAC;IAAE,OAAM,CAAC;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2127, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/components/portal/portal.js"], "sourcesContent": ["\"use client\";import T,{Fragment as E,createContext as A,useContext as d,useEffect as G,useMemo as x,useRef as L,useState as c}from\"react\";import{createPortal as h}from\"react-dom\";import{useEvent as _}from'../../hooks/use-event.js';import{useIsoMorphicEffect as C}from'../../hooks/use-iso-morphic-effect.js';import{useOnUnmount as F}from'../../hooks/use-on-unmount.js';import{useOwnerDocument as U}from'../../hooks/use-owner.js';import{useServerHandoffComplete as N}from'../../hooks/use-server-handoff-complete.js';import{optionalRef as S,useSyncRefs as m}from'../../hooks/use-sync-refs.js';import{usePortalRoot as W}from'../../internal/portal-force-root.js';import*as j from'../../utils/dom.js';import{env as v}from'../../utils/env.js';import{forwardRefWithAs as y,useRender as R}from'../../utils/render.js';function I(e){let l=W(),o=d(H),[r,u]=c(()=>{var i;if(!l&&o!==null)return(i=o.current)!=null?i:null;if(v.isServer)return null;let t=e==null?void 0:e.getElementById(\"headlessui-portal-root\");if(t)return t;if(e===null)return null;let a=e.createElement(\"div\");return a.setAttribute(\"id\",\"headlessui-portal-root\"),e.body.appendChild(a)});return G(()=>{r!==null&&(e!=null&&e.body.contains(r)||e==null||e.body.appendChild(r))},[r,e]),G(()=>{l||o!==null&&u(o.current)},[o,u,l]),r}let M=E,D=y(function(l,o){let{ownerDocument:r=null,...u}=l,t=L(null),a=m(S(s=>{t.current=s}),o),i=U(t),f=r!=null?r:i,p=I(f),[n]=c(()=>{var s;return v.isServer?null:(s=f==null?void 0:f.createElement(\"div\"))!=null?s:null}),P=d(g),O=N();C(()=>{!p||!n||p.contains(n)||(n.setAttribute(\"data-headlessui-portal\",\"\"),p.appendChild(n))},[p,n]),C(()=>{if(n&&P)return P.register(n)},[P,n]),F(()=>{var s;!p||!n||(j.isNode(n)&&p.contains(n)&&p.removeChild(n),p.childNodes.length<=0&&((s=p.parentElement)==null||s.removeChild(p)))});let b=R();return O?!p||!n?null:h(b({ourProps:{ref:a},theirProps:u,slot:{},defaultTag:M,name:\"Portal\"}),n):null});function J(e,l){let o=m(l),{enabled:r=!0,ownerDocument:u,...t}=e,a=R();return r?T.createElement(D,{...t,ownerDocument:u,ref:o}):a({ourProps:{ref:o},theirProps:t,slot:{},defaultTag:M,name:\"Portal\"})}let X=E,H=A(null);function k(e,l){let{target:o,...r}=e,t={ref:m(l)},a=R();return T.createElement(H.Provider,{value:o},a({ourProps:t,theirProps:r,defaultTag:X,name:\"Popover.Group\"}))}let g=A(null);function oe(){let e=d(g),l=L([]),o=_(t=>(l.current.push(t),e&&e.register(t),()=>r(t))),r=_(t=>{let a=l.current.indexOf(t);a!==-1&&l.current.splice(a,1),e&&e.unregister(t)}),u=x(()=>({register:o,unregister:r,portals:l}),[o,r,l]);return[l,x(()=>function({children:a}){return T.createElement(g.Provider,{value:u},a)},[u])]}let B=y(J),q=y(k),ne=Object.assign(B,{Group:q});export{ne as Portal,q as PortalGroup,oe as useNestedPortals};\n"], "names": [], "mappings": ";;;;;AAAa;AAA6H;AAAyC;AAAoD;AAA4E;AAA6D;AAA4D;AAAsF;AAA4E;AAAoE;AAAqC;AAAyC;AAAhuB;;;;;;;;;;;;;AAAwyB,SAAS,EAAE,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,oLAAA,CAAA,gBAAC,AAAD,KAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,aAAC,AAAD,EAAE,IAAG,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,qMAAA,CAAA,WAAC,AAAD,EAAE;QAAK,IAAI;QAAE,IAAG,CAAC,KAAG,MAAI,MAAK,OAAM,CAAC,IAAE,EAAE,OAAO,KAAG,OAAK,IAAE;QAAK,IAAG,6JAAA,CAAA,MAAC,CAAC,QAAQ,EAAC,OAAO;QAAK,IAAI,IAAE,KAAG,OAAK,KAAK,IAAE,EAAE,cAAc,CAAC;QAA0B,IAAG,GAAE,OAAO;QAAE,IAAG,MAAI,MAAK,OAAO;QAAK,IAAI,IAAE,EAAE,aAAa,CAAC;QAAO,OAAO,EAAE,YAAY,CAAC,MAAK,2BAA0B,EAAE,IAAI,CAAC,WAAW,CAAC;IAAE;IAAG,OAAO,CAAA,GAAA,qMAAA,CAAA,YAAC,AAAD,EAAE;QAAK,MAAI,QAAM,CAAC,KAAG,QAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAI,KAAG,QAAM,EAAE,IAAI,CAAC,WAAW,CAAC,EAAE;IAAC,GAAE;QAAC;QAAE;KAAE,GAAE,CAAA,GAAA,qMAAA,CAAA,YAAC,AAAD,EAAE;QAAK,KAAG,MAAI,QAAM,EAAE,EAAE,OAAO;IAAC,GAAE;QAAC;QAAE;QAAE;KAAE,GAAE;AAAC;AAAC,IAAI,IAAE,qMAAA,CAAA,WAAC,EAAC,IAAE,CAAA,GAAA,gKAAA,CAAA,mBAAC,AAAD,EAAE,SAAS,CAAC,EAAC,CAAC;IAAE,IAAG,EAAC,eAAc,IAAE,IAAI,EAAC,GAAG,GAAE,GAAC,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,SAAC,AAAD,EAAE,OAAM,IAAE,CAAA,GAAA,6KAAA,CAAA,cAAC,AAAD,EAAE,CAAA,GAAA,6KAAA,CAAA,cAAC,AAAD,EAAE,CAAA;QAAI,EAAE,OAAO,GAAC;IAAC,IAAG,IAAG,IAAE,CAAA,GAAA,sKAAA,CAAA,mBAAC,AAAD,EAAE,IAAG,IAAE,KAAG,OAAK,IAAE,GAAE,IAAE,EAAE,IAAG,CAAC,EAAE,GAAC,CAAA,GAAA,qMAAA,CAAA,WAAC,AAAD,EAAE;QAAK,IAAI;QAAE,OAAO,6JAAA,CAAA,MAAC,CAAC,QAAQ,GAAC,OAAK,CAAC,IAAE,KAAG,OAAK,KAAK,IAAE,EAAE,aAAa,CAAC,MAAM,KAAG,OAAK,IAAE;IAAI,IAAG,IAAE,CAAA,GAAA,qMAAA,CAAA,aAAC,AAAD,EAAE,IAAG,IAAE,CAAA,GAAA,8LAAA,CAAA,2BAAC,AAAD;IAAI,CAAA,GAAA,yLAAA,CAAA,sBAAC,AAAD,EAAE;QAAK,CAAC,KAAG,CAAC,KAAG,EAAE,QAAQ,CAAC,MAAI,CAAC,EAAE,YAAY,CAAC,0BAAyB,KAAI,EAAE,WAAW,CAAC,EAAE;IAAC,GAAE;QAAC;QAAE;KAAE,GAAE,CAAA,GAAA,yLAAA,CAAA,sBAAC,AAAD,EAAE;QAAK,IAAG,KAAG,GAAE,OAAO,EAAE,QAAQ,CAAC;IAAE,GAAE;QAAC;QAAE;KAAE,GAAE,CAAA,GAAA,8KAAA,CAAA,eAAC,AAAD,EAAE;QAAK,IAAI;QAAE,CAAC,KAAG,CAAC,KAAG,CAAC,6JAAA,CAAA,SAAQ,CAAC,MAAI,EAAE,QAAQ,CAAC,MAAI,EAAE,WAAW,CAAC,IAAG,EAAE,UAAU,CAAC,MAAM,IAAE,KAAG,CAAC,CAAC,IAAE,EAAE,aAAa,KAAG,QAAM,EAAE,WAAW,CAAC,EAAE,CAAC;IAAC;IAAG,IAAI,IAAE,CAAA,GAAA,gKAAA,CAAA,YAAC,AAAD;IAAI,OAAO,IAAE,CAAC,KAAG,CAAC,IAAE,OAAK,CAAA,GAAA,4MAAA,CAAA,eAAC,AAAD,EAAE,EAAE;QAAC,UAAS;YAAC,KAAI;QAAC;QAAE,YAAW;QAAE,MAAK,CAAC;QAAE,YAAW;QAAE,MAAK;IAAQ,IAAG,KAAG;AAAI;AAAG,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,6KAAA,CAAA,cAAC,AAAD,EAAE,IAAG,EAAC,SAAQ,IAAE,CAAC,CAAC,EAAC,eAAc,CAAC,EAAC,GAAG,GAAE,GAAC,GAAE,IAAE,CAAA,GAAA,gKAAA,CAAA,YAAC,AAAD;IAAI,OAAO,IAAE,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,GAAE;QAAC,GAAG,CAAC;QAAC,eAAc;QAAE,KAAI;IAAC,KAAG,EAAE;QAAC,UAAS;YAAC,KAAI;QAAC;QAAE,YAAW;QAAE,MAAK,CAAC;QAAE,YAAW;QAAE,MAAK;IAAQ;AAAE;AAAC,IAAI,IAAE,qMAAA,CAAA,WAAC,EAAC,IAAE,CAAA,GAAA,qMAAA,CAAA,gBAAC,AAAD,EAAE;AAAM,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,IAAG,EAAC,QAAO,CAAC,EAAC,GAAG,GAAE,GAAC,GAAE,IAAE;QAAC,KAAI,CAAA,GAAA,6KAAA,CAAA,cAAC,AAAD,EAAE;IAAE,GAAE,IAAE,CAAA,GAAA,gKAAA,CAAA,YAAC,AAAD;IAAI,OAAO,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAC;QAAC,OAAM;IAAC,GAAE,EAAE;QAAC,UAAS;QAAE,YAAW;QAAE,YAAW;QAAE,MAAK;IAAe;AAAG;AAAC,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,gBAAC,AAAD,EAAE;AAAM,SAAS;IAAK,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,aAAC,AAAD,EAAE,IAAG,IAAE,CAAA,GAAA,qMAAA,CAAA,SAAC,AAAD,EAAE,EAAE,GAAE,IAAE,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE,CAAA,IAAG,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,IAAG,KAAG,EAAE,QAAQ,CAAC,IAAG,IAAI,EAAE,EAAE,IAAG,IAAE,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,IAAI,IAAE,EAAE,OAAO,CAAC,OAAO,CAAC;QAAG,MAAI,CAAC,KAAG,EAAE,OAAO,CAAC,MAAM,CAAC,GAAE,IAAG,KAAG,EAAE,UAAU,CAAC;IAAE,IAAG,IAAE,CAAA,GAAA,qMAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC;YAAC,UAAS;YAAE,YAAW;YAAE,SAAQ;QAAC,CAAC,GAAE;QAAC;QAAE;QAAE;KAAE;IAAE,OAAM;QAAC;QAAE,CAAA,GAAA,qMAAA,CAAA,UAAC,AAAD,EAAE,IAAI,SAAS,EAAC,UAAS,CAAC,EAAC;gBAAE,OAAO,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAC;oBAAC,OAAM;gBAAC,GAAE;YAAE,GAAE;YAAC;SAAE;KAAE;AAAA;AAAC,IAAI,IAAE,CAAA,GAAA,gKAAA,CAAA,mBAAC,AAAD,EAAE,IAAG,IAAE,CAAA,GAAA,gKAAA,CAAA,mBAAC,AAAD,EAAE,IAAG,KAAG,OAAO,MAAM,CAAC,GAAE;IAAC,OAAM;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2276, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/hooks/use-flags.js"], "sourcesContent": ["import{useCallback as r,useState as b}from\"react\";function c(u=0){let[t,l]=b(u),g=r(e=>l(e),[t]),s=r(e=>l(a=>a|e),[t]),m=r(e=>(t&e)===e,[t]),n=r(e=>l(a=>a&~e),[l]),F=r(e=>l(a=>a^e),[l]);return{flags:t,setFlag:g,addFlag:s,hasFlag:m,removeFlag:n,toggleFlag:F}}export{c as useFlags};\n"], "names": [], "mappings": ";;;AAAA;;AAAkD,SAAS,EAAE,IAAE,CAAC;IAAE,IAAG,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,qMAAA,CAAA,WAAC,AAAD,EAAE,IAAG,IAAE,CAAA,GAAA,qMAAA,CAAA,cAAC,AAAD,EAAE,CAAA,IAAG,EAAE,IAAG;QAAC;KAAE,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,cAAC,AAAD,EAAE,CAAA,IAAG,EAAE,CAAA,IAAG,IAAE,IAAG;QAAC;KAAE,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,cAAC,AAAD,EAAE,CAAA,IAAG,CAAC,IAAE,CAAC,MAAI,GAAE;QAAC;KAAE,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,cAAC,AAAD,EAAE,CAAA,IAAG,EAAE,CAAA,IAAG,IAAE,CAAC,IAAG;QAAC;KAAE,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,cAAC,AAAD,EAAE,CAAA,IAAG,EAAE,CAAA,IAAG,IAAE,IAAG;QAAC;KAAE;IAAE,OAAM;QAAC,OAAM;QAAE,SAAQ;QAAE,SAAQ;QAAE,SAAQ;QAAE,YAAW;QAAE,YAAW;IAAC;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2307, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/hooks/use-transition.js"], "sourcesContent": ["var T,b;import{useRef as c,useState as S}from\"react\";import{disposables as m}from'../utils/disposables.js';import{useDisposables as g}from'./use-disposables.js';import{useFlags as y}from'./use-flags.js';import{useIsoMorphicEffect as A}from'./use-iso-morphic-effect.js';typeof process!=\"undefined\"&&typeof globalThis!=\"undefined\"&&typeof Element!=\"undefined\"&&((T=process==null?void 0:process.env)==null?void 0:T[\"NODE_ENV\"])===\"test\"&&typeof((b=Element==null?void 0:Element.prototype)==null?void 0:b.getAnimations)==\"undefined\"&&(Element.prototype.getAnimations=function(){return console.warn([\"Headless UI has polyfilled `Element.prototype.getAnimations` for your tests.\",\"Please install a proper polyfill e.g. `jsdom-testing-mocks`, to silence these warnings.\",\"\",\"Example usage:\",\"```js\",\"import { mockAnimationsApi } from 'jsdom-testing-mocks'\",\"mockAnimationsApi()\",\"```\"].join(`\n`)),[]});var L=(r=>(r[r.None=0]=\"None\",r[r.Closed=1]=\"Closed\",r[r.Enter=2]=\"Enter\",r[r.Leave=4]=\"Leave\",r))(L||{});function R(t){let n={};for(let e in t)t[e]===!0&&(n[`data-${e}`]=\"\");return n}function x(t,n,e,i){let[r,o]=S(e),{hasFlag:s,addFlag:a,removeFlag:l}=y(t&&r?3:0),u=c(!1),f=c(!1),E=g();return A(()=>{var d;if(t){if(e&&o(!0),!n){e&&a(3);return}return(d=i==null?void 0:i.start)==null||d.call(i,e),C(n,{inFlight:u,prepare(){f.current?f.current=!1:f.current=u.current,u.current=!0,!f.current&&(e?(a(3),l(4)):(a(4),l(2)))},run(){f.current?e?(l(3),a(4)):(l(4),a(3)):e?l(1):a(1)},done(){var p;f.current&&typeof n.getAnimations==\"function\"&&n.getAnimations().length>0||(u.current=!1,l(7),e||o(!1),(p=i==null?void 0:i.end)==null||p.call(i,e))}})}},[t,e,n,E]),t?[r,{closed:s(1),enter:s(2),leave:s(4),transition:s(2)||s(4)}]:[e,{closed:void 0,enter:void 0,leave:void 0,transition:void 0}]}function C(t,{prepare:n,run:e,done:i,inFlight:r}){let o=m();return j(t,{prepare:n,inFlight:r}),o.nextFrame(()=>{e(),o.requestAnimationFrame(()=>{o.add(M(t,i))})}),o.dispose}function M(t,n){var o,s;let e=m();if(!t)return e.dispose;let i=!1;e.add(()=>{i=!0});let r=(s=(o=t.getAnimations)==null?void 0:o.call(t).filter(a=>a instanceof CSSTransition))!=null?s:[];return r.length===0?(n(),e.dispose):(Promise.allSettled(r.map(a=>a.finished)).then(()=>{i||n()}),e.dispose)}function j(t,{inFlight:n,prepare:e}){if(n!=null&&n.current){e();return}let i=t.style.transition;t.style.transition=\"none\",e(),t.offsetHeight,t.style.transition=i}export{R as transitionDataAttributes,x as useTransition};\n"], "names": [], "mappings": ";;;;AAAQ;AAA6C;AAAsD;AAAsD;AAA0C;AAA3M,IAAI,GAAE;;;;;;AAAuQ,OAAO,WAAS,eAAa,OAAO,cAAY,eAAa,OAAO,WAAS,eAAa,CAAC,CAAC,IAAE,WAAS,OAAK,KAAK,IAAE,QAAQ,GAAG,KAAG,OAAK,KAAK,IAAE,CAAC,CAAC,WAAW,MAAI,UAAQ,OAAM,CAAC,CAAC,IAAE,WAAS,OAAK,KAAK,IAAE,QAAQ,SAAS,KAAG,OAAK,KAAK,IAAE,EAAE,aAAa,KAAG,eAAa,CAAC,QAAQ,SAAS,CAAC,aAAa,GAAC;IAAW,OAAO,QAAQ,IAAI,CAAC;QAAC;QAA+E;QAA0F;QAAG;QAAiB;QAAQ;QAA0D;QAAsB;KAAM,CAAC,IAAI,CAAC,CAAC;AACp3B,CAAC,IAAG,EAAE;AAAA,CAAC;AAAE,IAAI,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,IAAI,GAAC,EAAE,GAAC,QAAO,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE,GAAC,UAAS,CAAC,CAAC,EAAE,KAAK,GAAC,EAAE,GAAC,SAAQ,CAAC,CAAC,EAAE,KAAK,GAAC,EAAE,GAAC,SAAQ,CAAC,CAAC,EAAE,KAAG,CAAC;AAAG,SAAS,EAAE,CAAC;IAAE,IAAI,IAAE,CAAC;IAAE,IAAI,IAAI,KAAK,EAAE,CAAC,CAAC,EAAE,KAAG,CAAC,KAAG,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,GAAG,CAAC,GAAC,EAAE;IAAE,OAAO;AAAC;AAAC,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;IAAE,IAAG,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,qMAAA,CAAA,WAAC,AAAD,EAAE,IAAG,EAAC,SAAQ,CAAC,EAAC,SAAQ,CAAC,EAAC,YAAW,CAAC,EAAC,GAAC,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE,KAAG,IAAE,IAAE,IAAG,IAAE,CAAA,GAAA,qMAAA,CAAA,SAAC,AAAD,EAAE,CAAC,IAAG,IAAE,CAAA,GAAA,qMAAA,CAAA,SAAC,AAAD,EAAE,CAAC,IAAG,IAAE,CAAA,GAAA,4KAAA,CAAA,iBAAC,AAAD;IAAI,OAAO,CAAA,GAAA,yLAAA,CAAA,sBAAC,AAAD,EAAE;QAAK,IAAI;QAAE,IAAG,GAAE;YAAC,IAAG,KAAG,EAAE,CAAC,IAAG,CAAC,GAAE;gBAAC,KAAG,EAAE;gBAAG;YAAM;YAAC,OAAM,CAAC,IAAE,KAAG,OAAK,KAAK,IAAE,EAAE,KAAK,KAAG,QAAM,EAAE,IAAI,CAAC,GAAE,IAAG,EAAE,GAAE;gBAAC,UAAS;gBAAE;oBAAU,EAAE,OAAO,GAAC,EAAE,OAAO,GAAC,CAAC,IAAE,EAAE,OAAO,GAAC,EAAE,OAAO,EAAC,EAAE,OAAO,GAAC,CAAC,GAAE,CAAC,EAAE,OAAO,IAAE,CAAC,IAAE,CAAC,EAAE,IAAG,EAAE,EAAE,IAAE,CAAC,EAAE,IAAG,EAAE,EAAE,CAAC;gBAAC;gBAAE;oBAAM,EAAE,OAAO,GAAC,IAAE,CAAC,EAAE,IAAG,EAAE,EAAE,IAAE,CAAC,EAAE,IAAG,EAAE,EAAE,IAAE,IAAE,EAAE,KAAG,EAAE;gBAAE;gBAAE;oBAAO,IAAI;oBAAE,EAAE,OAAO,IAAE,OAAO,EAAE,aAAa,IAAE,cAAY,EAAE,aAAa,GAAG,MAAM,GAAC,KAAG,CAAC,EAAE,OAAO,GAAC,CAAC,GAAE,EAAE,IAAG,KAAG,EAAE,CAAC,IAAG,CAAC,IAAE,KAAG,OAAK,KAAK,IAAE,EAAE,GAAG,KAAG,QAAM,EAAE,IAAI,CAAC,GAAE,EAAE;gBAAC;YAAC;QAAE;IAAC,GAAE;QAAC;QAAE;QAAE;QAAE;KAAE,GAAE,IAAE;QAAC;QAAE;YAAC,QAAO,EAAE;YAAG,OAAM,EAAE;YAAG,OAAM,EAAE;YAAG,YAAW,EAAE,MAAI,EAAE;QAAE;KAAE,GAAC;QAAC;QAAE;YAAC,QAAO,KAAK;YAAE,OAAM,KAAK;YAAE,OAAM,KAAK;YAAE,YAAW,KAAK;QAAC;KAAE;AAAA;AAAC,SAAS,EAAE,CAAC,EAAC,EAAC,SAAQ,CAAC,EAAC,KAAI,CAAC,EAAC,MAAK,CAAC,EAAC,UAAS,CAAC,EAAC;IAAE,IAAI,IAAE,CAAA,GAAA,qKAAA,CAAA,cAAC,AAAD;IAAI,OAAO,EAAE,GAAE;QAAC,SAAQ;QAAE,UAAS;IAAC,IAAG,EAAE,SAAS,CAAC;QAAK,KAAI,EAAE,qBAAqB,CAAC;YAAK,EAAE,GAAG,CAAC,EAAE,GAAE;QAAG;IAAE,IAAG,EAAE,OAAO;AAAA;AAAC,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,IAAI,GAAE;IAAE,IAAI,IAAE,CAAA,GAAA,qKAAA,CAAA,cAAC,AAAD;IAAI,IAAG,CAAC,GAAE,OAAO,EAAE,OAAO;IAAC,IAAI,IAAE,CAAC;IAAE,EAAE,GAAG,CAAC;QAAK,IAAE,CAAC;IAAC;IAAG,IAAI,IAAE,CAAC,IAAE,CAAC,IAAE,EAAE,aAAa,KAAG,OAAK,KAAK,IAAE,EAAE,IAAI,CAAC,GAAG,MAAM,CAAC,CAAA,IAAG,aAAa,cAAc,KAAG,OAAK,IAAE,EAAE;IAAC,OAAO,EAAE,MAAM,KAAG,IAAE,CAAC,KAAI,EAAE,OAAO,IAAE,CAAC,QAAQ,UAAU,CAAC,EAAE,GAAG,CAAC,CAAA,IAAG,EAAE,QAAQ,GAAG,IAAI,CAAC;QAAK,KAAG;IAAG,IAAG,EAAE,OAAO;AAAC;AAAC,SAAS,EAAE,CAAC,EAAC,EAAC,UAAS,CAAC,EAAC,SAAQ,CAAC,EAAC;IAAE,IAAG,KAAG,QAAM,EAAE,OAAO,EAAC;QAAC;QAAI;IAAM;IAAC,IAAI,IAAE,EAAE,KAAK,CAAC,UAAU;IAAC,EAAE,KAAK,CAAC,UAAU,GAAC,QAAO,KAAI,EAAE,YAAY,EAAC,EAAE,KAAK,CAAC,UAAU,GAAC;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2424, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/components/transition/transition.js"], "sourcesContent": ["\"use client\";import c,{Fragment as O,createContext as ne,useContext as q,useEffect as ge,useMemo as ie,useRef as b,useState as V}from\"react\";import{useDisposables as ve}from'../../hooks/use-disposables.js';import{useEvent as E}from'../../hooks/use-event.js';import{useIsMounted as be}from'../../hooks/use-is-mounted.js';import{useIsoMorphicEffect as D}from'../../hooks/use-iso-morphic-effect.js';import{useLatestValue as Ee}from'../../hooks/use-latest-value.js';import{useServerHandoffComplete as re}from'../../hooks/use-server-handoff-complete.js';import{useSyncRefs as oe}from'../../hooks/use-sync-refs.js';import{transitionDataAttributes as Se,useTransition as Re}from'../../hooks/use-transition.js';import{OpenClosedProvider as ye,State as x,useOpenClosed as se}from'../../internal/open-closed.js';import{classNames as Pe}from'../../utils/class-names.js';import{match as le}from'../../utils/match.js';import{RenderFeatures as xe,RenderStrategy as P,compact as Ne,forwardRefWithAs as J,useRender as ae}from'../../utils/render.js';function ue(e){var t;return!!(e.enter||e.enterFrom||e.enterTo||e.leave||e.leaveFrom||e.leaveTo)||((t=e.as)!=null?t:de)!==O||c.Children.count(e.children)===1}let w=ne(null);w.displayName=\"TransitionContext\";var _e=(n=>(n.Visible=\"visible\",n.Hidden=\"hidden\",n))(_e||{});function De(){let e=q(w);if(e===null)throw new Error(\"A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.\");return e}function He(){let e=q(M);if(e===null)throw new Error(\"A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.\");return e}let M=ne(null);M.displayName=\"NestingContext\";function U(e){return\"children\"in e?U(e.children):e.current.filter(({el:t})=>t.current!==null).filter(({state:t})=>t===\"visible\").length>0}function Te(e,t){let n=Ee(e),l=b([]),S=be(),R=ve(),d=E((o,i=P.Hidden)=>{let a=l.current.findIndex(({el:s})=>s===o);a!==-1&&(le(i,{[P.Unmount](){l.current.splice(a,1)},[P.Hidden](){l.current[a].state=\"hidden\"}}),R.microTask(()=>{var s;!U(l)&&S.current&&((s=n.current)==null||s.call(n))}))}),y=E(o=>{let i=l.current.find(({el:a})=>a===o);return i?i.state!==\"visible\"&&(i.state=\"visible\"):l.current.push({el:o,state:\"visible\"}),()=>d(o,P.Unmount)}),C=b([]),p=b(Promise.resolve()),h=b({enter:[],leave:[]}),g=E((o,i,a)=>{C.current.splice(0),t&&(t.chains.current[i]=t.chains.current[i].filter(([s])=>s!==o)),t==null||t.chains.current[i].push([o,new Promise(s=>{C.current.push(s)})]),t==null||t.chains.current[i].push([o,new Promise(s=>{Promise.all(h.current[i].map(([r,f])=>f)).then(()=>s())})]),i===\"enter\"?p.current=p.current.then(()=>t==null?void 0:t.wait.current).then(()=>a(i)):a(i)}),v=E((o,i,a)=>{Promise.all(h.current[i].splice(0).map(([s,r])=>r)).then(()=>{var s;(s=C.current.shift())==null||s()}).then(()=>a(i))});return ie(()=>({children:l,register:y,unregister:d,onStart:g,onStop:v,wait:p,chains:h}),[y,d,l,g,v,h,p])}let de=O,fe=xe.RenderStrategy;function Ae(e,t){var ee,te;let{transition:n=!0,beforeEnter:l,afterEnter:S,beforeLeave:R,afterLeave:d,enter:y,enterFrom:C,enterTo:p,entered:h,leave:g,leaveFrom:v,leaveTo:o,...i}=e,[a,s]=V(null),r=b(null),f=ue(e),j=oe(...f?[r,t,s]:t===null?[]:[t]),H=(ee=i.unmount)==null||ee?P.Unmount:P.Hidden,{show:u,appear:z,initial:K}=De(),[m,G]=V(u?\"visible\":\"hidden\"),Q=He(),{register:A,unregister:I}=Q;D(()=>A(r),[A,r]),D(()=>{if(H===P.Hidden&&r.current){if(u&&m!==\"visible\"){G(\"visible\");return}return le(m,{[\"hidden\"]:()=>I(r),[\"visible\"]:()=>A(r)})}},[m,r,A,I,u,H]);let B=re();D(()=>{if(f&&B&&m===\"visible\"&&r.current===null)throw new Error(\"Did you forget to passthrough the `ref` to the actual DOM node?\")},[r,m,B,f]);let ce=K&&!z,Y=z&&u&&K,W=b(!1),L=Te(()=>{W.current||(G(\"hidden\"),I(r))},Q),Z=E(k=>{W.current=!0;let F=k?\"enter\":\"leave\";L.onStart(r,F,_=>{_===\"enter\"?l==null||l():_===\"leave\"&&(R==null||R())})}),$=E(k=>{let F=k?\"enter\":\"leave\";W.current=!1,L.onStop(r,F,_=>{_===\"enter\"?S==null||S():_===\"leave\"&&(d==null||d())}),F===\"leave\"&&!U(L)&&(G(\"hidden\"),I(r))});ge(()=>{f&&n||(Z(u),$(u))},[u,f,n]);let pe=(()=>!(!n||!f||!B||ce))(),[,T]=Re(pe,a,u,{start:Z,end:$}),Ce=Ne({ref:j,className:((te=Pe(i.className,Y&&y,Y&&C,T.enter&&y,T.enter&&T.closed&&C,T.enter&&!T.closed&&p,T.leave&&g,T.leave&&!T.closed&&v,T.leave&&T.closed&&o,!T.transition&&u&&h))==null?void 0:te.trim())||void 0,...Se(T)}),N=0;m===\"visible\"&&(N|=x.Open),m===\"hidden\"&&(N|=x.Closed),u&&m===\"hidden\"&&(N|=x.Opening),!u&&m===\"visible\"&&(N|=x.Closing);let he=ae();return c.createElement(M.Provider,{value:L},c.createElement(ye,{value:N},he({ourProps:Ce,theirProps:i,defaultTag:de,features:fe,visible:m===\"visible\",name:\"Transition.Child\"})))}function Ie(e,t){let{show:n,appear:l=!1,unmount:S=!0,...R}=e,d=b(null),y=ue(e),C=oe(...y?[d,t]:t===null?[]:[t]);re();let p=se();if(n===void 0&&p!==null&&(n=(p&x.Open)===x.Open),n===void 0)throw new Error(\"A <Transition /> is used but it is missing a `show={true | false}` prop.\");let[h,g]=V(n?\"visible\":\"hidden\"),v=Te(()=>{n||g(\"hidden\")}),[o,i]=V(!0),a=b([n]);D(()=>{o!==!1&&a.current[a.current.length-1]!==n&&(a.current.push(n),i(!1))},[a,n]);let s=ie(()=>({show:n,appear:l,initial:o}),[n,l,o]);D(()=>{n?g(\"visible\"):!U(v)&&d.current!==null&&g(\"hidden\")},[n,v]);let r={unmount:S},f=E(()=>{var u;o&&i(!1),(u=e.beforeEnter)==null||u.call(e)}),j=E(()=>{var u;o&&i(!1),(u=e.beforeLeave)==null||u.call(e)}),H=ae();return c.createElement(M.Provider,{value:v},c.createElement(w.Provider,{value:s},H({ourProps:{...r,as:O,children:c.createElement(me,{ref:C,...r,...R,beforeEnter:f,beforeLeave:j})},theirProps:{},defaultTag:O,features:fe,visible:h===\"visible\",name:\"Transition\"})))}function Le(e,t){let n=q(w)!==null,l=se()!==null;return c.createElement(c.Fragment,null,!n&&l?c.createElement(X,{ref:t,...e}):c.createElement(me,{ref:t,...e}))}let X=J(Ie),me=J(Ae),Fe=J(Le),ze=Object.assign(X,{Child:Fe,Root:X});export{ze as Transition,Fe as TransitionChild};\n"], "names": [], "mappings": ";;;;AAAa;AAAgI;AAAiE;AAAoD;AAA8D;AAA4E;AAAkE;AAAuF;AAA4D;AAA8F;AAAmG;AAAyD;AAA8C;AAAz4B;;;;;;;;;;;;;;AAAygC,SAAS,GAAG,CAAC;IAAE,IAAI;IAAE,OAAM,CAAC,CAAC,CAAC,EAAE,KAAK,IAAE,EAAE,SAAS,IAAE,EAAE,OAAO,IAAE,EAAE,KAAK,IAAE,EAAE,SAAS,IAAE,EAAE,OAAO,KAAG,CAAC,CAAC,IAAE,EAAE,EAAE,KAAG,OAAK,IAAE,EAAE,MAAI,qMAAA,CAAA,WAAC,IAAE,qMAAA,CAAA,UAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,QAAQ,MAAI;AAAC;AAAC,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,gBAAE,AAAD,EAAE;AAAM,EAAE,WAAW,GAAC;AAAoB,IAAI,KAAG,CAAC,CAAA,IAAG,CAAC,EAAE,OAAO,GAAC,WAAU,EAAE,MAAM,GAAC,UAAS,CAAC,CAAC,EAAE,MAAI,CAAC;AAAG,SAAS;IAAK,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,aAAC,AAAD,EAAE;IAAG,IAAG,MAAI,MAAK,MAAM,IAAI,MAAM;IAAoG,OAAO;AAAC;AAAC,SAAS;IAAK,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,aAAC,AAAD,EAAE;IAAG,IAAG,MAAI,MAAK,MAAM,IAAI,MAAM;IAAoG,OAAO;AAAC;AAAC,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,gBAAE,AAAD,EAAE;AAAM,EAAE,WAAW,GAAC;AAAiB,SAAS,EAAE,CAAC;IAAE,OAAM,cAAa,IAAE,EAAE,EAAE,QAAQ,IAAE,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,EAAC,IAAG,CAAC,EAAC,GAAG,EAAE,OAAO,KAAG,MAAM,MAAM,CAAC,CAAC,EAAC,OAAM,CAAC,EAAC,GAAG,MAAI,WAAW,MAAM,GAAC;AAAC;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,gLAAA,CAAA,iBAAE,AAAD,EAAE,IAAG,IAAE,CAAA,GAAA,qMAAA,CAAA,SAAC,AAAD,EAAE,EAAE,GAAE,IAAE,CAAA,GAAA,8KAAA,CAAA,eAAE,AAAD,KAAI,IAAE,CAAA,GAAA,4KAAA,CAAA,iBAAE,AAAD,KAAI,IAAE,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE,CAAC,GAAE,IAAE,gKAAA,CAAA,iBAAC,CAAC,MAAM;QAAI,IAAI,IAAE,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC,EAAC,IAAG,CAAC,EAAC,GAAG,MAAI;QAAG,MAAI,CAAC,KAAG,CAAC,CAAA,GAAA,+JAAA,CAAA,QAAE,AAAD,EAAE,GAAE;YAAC,CAAC,gKAAA,CAAA,iBAAC,CAAC,OAAO,CAAC;gBAAG,EAAE,OAAO,CAAC,MAAM,CAAC,GAAE;YAAE;YAAE,CAAC,gKAAA,CAAA,iBAAC,CAAC,MAAM,CAAC;gBAAG,EAAE,OAAO,CAAC,EAAE,CAAC,KAAK,GAAC;YAAQ;QAAC,IAAG,EAAE,SAAS,CAAC;YAAK,IAAI;YAAE,CAAC,EAAE,MAAI,EAAE,OAAO,IAAE,CAAC,CAAC,IAAE,EAAE,OAAO,KAAG,QAAM,EAAE,IAAI,CAAC,EAAE;QAAC,EAAE;IAAC,IAAG,IAAE,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,IAAI,IAAE,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,EAAC,IAAG,CAAC,EAAC,GAAG,MAAI;QAAG,OAAO,IAAE,EAAE,KAAK,KAAG,aAAW,CAAC,EAAE,KAAK,GAAC,SAAS,IAAE,EAAE,OAAO,CAAC,IAAI,CAAC;YAAC,IAAG;YAAE,OAAM;QAAS,IAAG,IAAI,EAAE,GAAE,gKAAA,CAAA,iBAAC,CAAC,OAAO;IAAC,IAAG,IAAE,CAAA,GAAA,qMAAA,CAAA,SAAC,AAAD,EAAE,EAAE,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,SAAC,AAAD,EAAE,QAAQ,OAAO,KAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,SAAC,AAAD,EAAE;QAAC,OAAM,EAAE;QAAC,OAAM,EAAE;IAAA,IAAG,IAAE,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE,CAAC,GAAE,GAAE;QAAK,EAAE,OAAO,CAAC,MAAM,CAAC,IAAG,KAAG,CAAC,EAAE,MAAM,CAAC,OAAO,CAAC,EAAE,GAAC,EAAE,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,MAAI,EAAE,GAAE,KAAG,QAAM,EAAE,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC;YAAC;YAAE,IAAI,QAAQ,CAAA;gBAAI,EAAE,OAAO,CAAC,IAAI,CAAC;YAAE;SAAG,GAAE,KAAG,QAAM,EAAE,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC;YAAC;YAAE,IAAI,QAAQ,CAAA;gBAAI,QAAQ,GAAG,CAAC,EAAE,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,GAAE,EAAE,GAAG,IAAI,IAAI,CAAC,IAAI;YAAI;SAAG,GAAE,MAAI,UAAQ,EAAE,OAAO,GAAC,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI,KAAG,OAAK,KAAK,IAAE,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,EAAE,MAAI,EAAE;IAAE,IAAG,IAAE,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE,CAAC,GAAE,GAAE;QAAK,QAAQ,GAAG,CAAC,EAAE,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAE,EAAE,GAAG,IAAI,IAAI,CAAC;YAAK,IAAI;YAAE,CAAC,IAAE,EAAE,OAAO,CAAC,KAAK,EAAE,KAAG,QAAM;QAAG,GAAG,IAAI,CAAC,IAAI,EAAE;IAAG;IAAG,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAE,AAAD,EAAE,IAAI,CAAC;YAAC,UAAS;YAAE,UAAS;YAAE,YAAW;YAAE,SAAQ;YAAE,QAAO;YAAE,MAAK;YAAE,QAAO;QAAC,CAAC,GAAE;QAAC;QAAE;QAAE;QAAE;QAAE;QAAE;QAAE;KAAE;AAAC;AAAC,IAAI,KAAG,qMAAA,CAAA,WAAC,EAAC,KAAG,gKAAA,CAAA,iBAAE,CAAC,cAAc;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAI,IAAG;IAAG,IAAG,EAAC,YAAW,IAAE,CAAC,CAAC,EAAC,aAAY,CAAC,EAAC,YAAW,CAAC,EAAC,aAAY,CAAC,EAAC,YAAW,CAAC,EAAC,OAAM,CAAC,EAAC,WAAU,CAAC,EAAC,SAAQ,CAAC,EAAC,SAAQ,CAAC,EAAC,OAAM,CAAC,EAAC,WAAU,CAAC,EAAC,SAAQ,CAAC,EAAC,GAAG,GAAE,GAAC,GAAE,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,qMAAA,CAAA,WAAC,AAAD,EAAE,OAAM,IAAE,CAAA,GAAA,qMAAA,CAAA,SAAC,AAAD,EAAE,OAAM,IAAE,GAAG,IAAG,IAAE,CAAA,GAAA,6KAAA,CAAA,cAAE,AAAD,KAAK,IAAE;QAAC;QAAE;QAAE;KAAE,GAAC,MAAI,OAAK,EAAE,GAAC;QAAC;KAAE,GAAE,IAAE,CAAC,KAAG,EAAE,OAAO,KAAG,QAAM,KAAG,gKAAA,CAAA,iBAAC,CAAC,OAAO,GAAC,gKAAA,CAAA,iBAAC,CAAC,MAAM,EAAC,EAAC,MAAK,CAAC,EAAC,QAAO,CAAC,EAAC,SAAQ,CAAC,EAAC,GAAC,MAAK,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,qMAAA,CAAA,WAAC,AAAD,EAAE,IAAE,YAAU,WAAU,IAAE,MAAK,EAAC,UAAS,CAAC,EAAC,YAAW,CAAC,EAAC,GAAC;IAAE,CAAA,GAAA,yLAAA,CAAA,sBAAC,AAAD,EAAE,IAAI,EAAE,IAAG;QAAC;QAAE;KAAE,GAAE,CAAA,GAAA,yLAAA,CAAA,sBAAC,AAAD,EAAE;QAAK,IAAG,MAAI,gKAAA,CAAA,iBAAC,CAAC,MAAM,IAAE,EAAE,OAAO,EAAC;YAAC,IAAG,KAAG,MAAI,WAAU;gBAAC,EAAE;gBAAW;YAAM;YAAC,OAAO,CAAA,GAAA,+JAAA,CAAA,QAAE,AAAD,EAAE,GAAE;gBAAC,CAAC,SAAS,EAAC,IAAI,EAAE;gBAAG,CAAC,UAAU,EAAC,IAAI,EAAE;YAAE;QAAE;IAAC,GAAE;QAAC;QAAE;QAAE;QAAE;QAAE;QAAE;KAAE;IAAE,IAAI,IAAE,CAAA,GAAA,8LAAA,CAAA,2BAAE,AAAD;IAAI,CAAA,GAAA,yLAAA,CAAA,sBAAC,AAAD,EAAE;QAAK,IAAG,KAAG,KAAG,MAAI,aAAW,EAAE,OAAO,KAAG,MAAK,MAAM,IAAI,MAAM;IAAkE,GAAE;QAAC;QAAE;QAAE;QAAE;KAAE;IAAE,IAAI,KAAG,KAAG,CAAC,GAAE,IAAE,KAAG,KAAG,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,SAAC,AAAD,EAAE,CAAC,IAAG,IAAE,GAAG;QAAK,EAAE,OAAO,IAAE,CAAC,EAAE,WAAU,EAAE,EAAE;IAAC,GAAE,IAAG,IAAE,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,EAAE,OAAO,GAAC,CAAC;QAAE,IAAI,IAAE,IAAE,UAAQ;QAAQ,EAAE,OAAO,CAAC,GAAE,GAAE,CAAA;YAAI,MAAI,UAAQ,KAAG,QAAM,MAAI,MAAI,WAAS,CAAC,KAAG,QAAM,GAAG;QAAC;IAAE,IAAG,IAAE,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,IAAI,IAAE,IAAE,UAAQ;QAAQ,EAAE,OAAO,GAAC,CAAC,GAAE,EAAE,MAAM,CAAC,GAAE,GAAE,CAAA;YAAI,MAAI,UAAQ,KAAG,QAAM,MAAI,MAAI,WAAS,CAAC,KAAG,QAAM,GAAG;QAAC,IAAG,MAAI,WAAS,CAAC,EAAE,MAAI,CAAC,EAAE,WAAU,EAAE,EAAE;IAAC;IAAG,CAAA,GAAA,qMAAA,CAAA,YAAE,AAAD,EAAE;QAAK,KAAG,KAAG,CAAC,EAAE,IAAG,EAAE,EAAE;IAAC,GAAE;QAAC;QAAE;QAAE;KAAE;IAAE,IAAI,KAAG,CAAC,IAAI,CAAC,CAAC,CAAC,KAAG,CAAC,KAAG,CAAC,KAAG,EAAE,CAAC,KAAI,GAAE,EAAE,GAAC,CAAA,GAAA,2KAAA,CAAA,gBAAE,AAAD,EAAE,IAAG,GAAE,GAAE;QAAC,OAAM;QAAE,KAAI;IAAC,IAAG,KAAG,CAAA,GAAA,gKAAA,CAAA,UAAE,AAAD,EAAE;QAAC,KAAI;QAAE,WAAU,CAAC,CAAC,KAAG,CAAA,GAAA,wKAAA,CAAA,aAAE,AAAD,EAAE,EAAE,SAAS,EAAC,KAAG,GAAE,KAAG,GAAE,EAAE,KAAK,IAAE,GAAE,EAAE,KAAK,IAAE,EAAE,MAAM,IAAE,GAAE,EAAE,KAAK,IAAE,CAAC,EAAE,MAAM,IAAE,GAAE,EAAE,KAAK,IAAE,GAAE,EAAE,KAAK,IAAE,CAAC,EAAE,MAAM,IAAE,GAAE,EAAE,KAAK,IAAE,EAAE,MAAM,IAAE,GAAE,CAAC,EAAE,UAAU,IAAE,KAAG,EAAE,KAAG,OAAK,KAAK,IAAE,GAAG,IAAI,EAAE,KAAG,KAAK;QAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,2BAAE,AAAD,EAAE,EAAE;IAAA,IAAG,IAAE;IAAE,MAAI,aAAW,CAAC,KAAG,2KAAA,CAAA,QAAC,CAAC,IAAI,GAAE,MAAI,YAAU,CAAC,KAAG,2KAAA,CAAA,QAAC,CAAC,MAAM,GAAE,KAAG,MAAI,YAAU,CAAC,KAAG,2KAAA,CAAA,QAAC,CAAC,OAAO,GAAE,CAAC,KAAG,MAAI,aAAW,CAAC,KAAG,2KAAA,CAAA,QAAC,CAAC,OAAO;IAAE,IAAI,KAAG,CAAA,GAAA,gKAAA,CAAA,YAAE,AAAD;IAAI,OAAO,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAC;QAAC,OAAM;IAAC,GAAE,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,2KAAA,CAAA,qBAAE,EAAC;QAAC,OAAM;IAAC,GAAE,GAAG;QAAC,UAAS;QAAG,YAAW;QAAE,YAAW;QAAG,UAAS;QAAG,SAAQ,MAAI;QAAU,MAAK;IAAkB;AAAI;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAG,EAAC,MAAK,CAAC,EAAC,QAAO,IAAE,CAAC,CAAC,EAAC,SAAQ,IAAE,CAAC,CAAC,EAAC,GAAG,GAAE,GAAC,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,SAAC,AAAD,EAAE,OAAM,IAAE,GAAG,IAAG,IAAE,CAAA,GAAA,6KAAA,CAAA,cAAE,AAAD,KAAK,IAAE;QAAC;QAAE;KAAE,GAAC,MAAI,OAAK,EAAE,GAAC;QAAC;KAAE;IAAE,CAAA,GAAA,8LAAA,CAAA,2BAAE,AAAD;IAAI,IAAI,IAAE,CAAA,GAAA,2KAAA,CAAA,gBAAE,AAAD;IAAI,IAAG,MAAI,KAAK,KAAG,MAAI,QAAM,CAAC,IAAE,CAAC,IAAE,2KAAA,CAAA,QAAC,CAAC,IAAI,MAAI,2KAAA,CAAA,QAAC,CAAC,IAAI,GAAE,MAAI,KAAK,GAAE,MAAM,IAAI,MAAM;IAA4E,IAAG,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,qMAAA,CAAA,WAAC,AAAD,EAAE,IAAE,YAAU,WAAU,IAAE,GAAG;QAAK,KAAG,EAAE;IAAS,IAAG,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,qMAAA,CAAA,WAAC,AAAD,EAAE,CAAC,IAAG,IAAE,CAAA,GAAA,qMAAA,CAAA,SAAC,AAAD,EAAE;QAAC;KAAE;IAAE,CAAA,GAAA,yLAAA,CAAA,sBAAC,AAAD,EAAE;QAAK,MAAI,CAAC,KAAG,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,MAAM,GAAC,EAAE,KAAG,KAAG,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,IAAG,EAAE,CAAC,EAAE;IAAC,GAAE;QAAC;QAAE;KAAE;IAAE,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,UAAE,AAAD,EAAE,IAAI,CAAC;YAAC,MAAK;YAAE,QAAO;YAAE,SAAQ;QAAC,CAAC,GAAE;QAAC;QAAE;QAAE;KAAE;IAAE,CAAA,GAAA,yLAAA,CAAA,sBAAC,AAAD,EAAE;QAAK,IAAE,EAAE,aAAW,CAAC,EAAE,MAAI,EAAE,OAAO,KAAG,QAAM,EAAE;IAAS,GAAE;QAAC;QAAE;KAAE;IAAE,IAAI,IAAE;QAAC,SAAQ;IAAC,GAAE,IAAE,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE;QAAK,IAAI;QAAE,KAAG,EAAE,CAAC,IAAG,CAAC,IAAE,EAAE,WAAW,KAAG,QAAM,EAAE,IAAI,CAAC;IAAE,IAAG,IAAE,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE;QAAK,IAAI;QAAE,KAAG,EAAE,CAAC,IAAG,CAAC,IAAE,EAAE,WAAW,KAAG,QAAM,EAAE,IAAI,CAAC;IAAE,IAAG,IAAE,CAAA,GAAA,gKAAA,CAAA,YAAE,AAAD;IAAI,OAAO,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAC;QAAC,OAAM;IAAC,GAAE,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAC;QAAC,OAAM;IAAC,GAAE,EAAE;QAAC,UAAS;YAAC,GAAG,CAAC;YAAC,IAAG,qMAAA,CAAA,WAAC;YAAC,UAAS,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,IAAG;gBAAC,KAAI;gBAAE,GAAG,CAAC;gBAAC,GAAG,CAAC;gBAAC,aAAY;gBAAE,aAAY;YAAC;QAAE;QAAE,YAAW,CAAC;QAAE,YAAW,qMAAA,CAAA,WAAC;QAAC,UAAS;QAAG,SAAQ,MAAI;QAAU,MAAK;IAAY;AAAI;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,aAAC,AAAD,EAAE,OAAK,MAAK,IAAE,CAAA,GAAA,2KAAA,CAAA,gBAAE,AAAD,QAAM;IAAK,OAAO,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,qMAAA,CAAA,UAAC,CAAC,QAAQ,EAAC,MAAK,CAAC,KAAG,IAAE,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,GAAE;QAAC,KAAI;QAAE,GAAG,CAAC;IAAA,KAAG,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,IAAG;QAAC,KAAI;QAAE,GAAG,CAAC;IAAA;AAAG;AAAC,IAAI,IAAE,CAAA,GAAA,gKAAA,CAAA,mBAAC,AAAD,EAAE,KAAI,KAAG,CAAA,GAAA,gKAAA,CAAA,mBAAC,AAAD,EAAE,KAAI,KAAG,CAAA,GAAA,gKAAA,CAAA,mBAAC,AAAD,EAAE,KAAI,KAAG,OAAO,MAAM,CAAC,GAAE;IAAC,OAAM;IAAG,MAAK;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2708, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/%40headlessui/react/dist/components/dialog/dialog.js"], "sourcesContent": ["\"use client\";import l,{Fragment as $,createContext as se,createRef as pe,use<PERSON><PERSON>back as de,useContext as ue,useEffect as fe,useMemo as A,useReducer as Te,useRef as j}from\"react\";import{useEscape as ge}from'../../hooks/use-escape.js';import{useEvent as _}from'../../hooks/use-event.js';import{useId as k}from'../../hooks/use-id.js';import{useInertOthers as ce}from'../../hooks/use-inert-others.js';import{useIsTouchDevice as me}from'../../hooks/use-is-touch-device.js';import{useIsoMorphicEffect as De}from'../../hooks/use-iso-morphic-effect.js';import{useOnDisappear as Pe}from'../../hooks/use-on-disappear.js';import{useOutsideClick as ye}from'../../hooks/use-outside-click.js';import{useOwnerDocument as Ee}from'../../hooks/use-owner.js';import{MainTreeProvider as Y,useMainTreeNode as Ae,useRootContainers as _e}from'../../hooks/use-root-containers.js';import{useScrollLock as Ce}from'../../hooks/use-scroll-lock.js';import{useServerHandoffComplete as Re}from'../../hooks/use-server-handoff-complete.js';import{useSyncRefs as G}from'../../hooks/use-sync-refs.js';import{CloseProvider as Fe}from'../../internal/close-provider.js';import{ResetOpenClosedProvider as be,State as x,useOpenClosed as J}from'../../internal/open-closed.js';import{ForcePortalRoot as K}from'../../internal/portal-force-root.js';import{stackMachines as ve}from'../../machines/stack-machine.js';import{useSlice as Le}from'../../react-glue.js';import{match as xe}from'../../utils/match.js';import{RenderFeatures as X,forwardRefWithAs as C,useRender as h}from'../../utils/render.js';import{Description as V,useDescriptions as he}from'../description/description.js';import{FocusTrap as Oe,FocusTrapFeatures as R}from'../focus-trap/focus-trap.js';import{Portal as Se,PortalGroup as Ie,useNestedPortals as Me}from'../portal/portal.js';import{Transition as ke,TransitionChild as q}from'../transition/transition.js';var Ge=(o=>(o[o.Open=0]=\"Open\",o[o.Closed=1]=\"Closed\",o))(Ge||{}),we=(t=>(t[t.SetTitleId=0]=\"SetTitleId\",t))(we||{});let Be={[0](e,t){return e.titleId===t.id?e:{...e,titleId:t.id}}},w=se(null);w.displayName=\"DialogContext\";function O(e){let t=ue(w);if(t===null){let o=new Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(o,O),o}return t}function Ue(e,t){return xe(t.type,Be,e,t)}let z=C(function(t,o){let a=k(),{id:n=`headlessui-dialog-${a}`,open:i,onClose:s,initialFocus:d,role:p=\"dialog\",autoFocus:T=!0,__demoMode:u=!1,unmount:y=!1,...S}=t,F=j(!1);p=function(){return p===\"dialog\"||p===\"alertdialog\"?p:(F.current||(F.current=!0,console.warn(`Invalid role [${p}] passed to <Dialog />. Only \\`dialog\\` and and \\`alertdialog\\` are supported. Using \\`dialog\\` instead.`)),\"dialog\")}();let c=J();i===void 0&&c!==null&&(i=(c&x.Open)===x.Open);let f=j(null),I=G(f,o),b=Ee(f),g=i?0:1,[v,Q]=Te(Ue,{titleId:null,descriptionId:null,panelRef:pe()}),m=_(()=>s(!1)),B=_(r=>Q({type:0,id:r})),D=Re()?g===0:!1,[Z,ee]=Me(),te={get current(){var r;return(r=v.panelRef.current)!=null?r:f.current}},L=Ae(),{resolveContainers:M}=_e({mainTreeNode:L,portals:Z,defaultContainers:[te]}),U=c!==null?(c&x.Closing)===x.Closing:!1;ce(u||U?!1:D,{allowed:_(()=>{var r,W;return[(W=(r=f.current)==null?void 0:r.closest(\"[data-headlessui-portal]\"))!=null?W:null]}),disallowed:_(()=>{var r;return[(r=L==null?void 0:L.closest(\"body > *:not(#headlessui-portal-root)\"))!=null?r:null]})});let P=ve.get(null);De(()=>{if(D)return P.actions.push(n),()=>P.actions.pop(n)},[P,n,D]);let H=Le(P,de(r=>P.selectors.isTop(r,n),[P,n]));ye(H,M,r=>{r.preventDefault(),m()}),ge(H,b==null?void 0:b.defaultView,r=>{r.preventDefault(),r.stopPropagation(),document.activeElement&&\"blur\"in document.activeElement&&typeof document.activeElement.blur==\"function\"&&document.activeElement.blur(),m()}),Ce(u||U?!1:D,b,M),Pe(D,f,m);let[oe,ne]=he(),re=A(()=>[{dialogState:g,close:m,setTitleId:B,unmount:y},v],[g,v,m,B,y]),N=A(()=>({open:g===0}),[g]),le={ref:I,id:n,role:p,tabIndex:-1,\"aria-modal\":u?void 0:g===0?!0:void 0,\"aria-labelledby\":v.titleId,\"aria-describedby\":oe,unmount:y},ae=!me(),E=R.None;D&&!u&&(E|=R.RestoreFocus,E|=R.TabLock,T&&(E|=R.AutoFocus),ae&&(E|=R.InitialFocus));let ie=h();return l.createElement(be,null,l.createElement(K,{force:!0},l.createElement(Se,null,l.createElement(w.Provider,{value:re},l.createElement(Ie,{target:f},l.createElement(K,{force:!1},l.createElement(ne,{slot:N},l.createElement(ee,null,l.createElement(Oe,{initialFocus:d,initialFocusFallback:f,containers:M,features:E},l.createElement(Fe,{value:m},ie({ourProps:le,theirProps:S,slot:N,defaultTag:He,features:Ne,visible:g===0,name:\"Dialog\"})))))))))))}),He=\"div\",Ne=X.RenderStrategy|X.Static;function We(e,t){let{transition:o=!1,open:a,...n}=e,i=J(),s=e.hasOwnProperty(\"open\")||i!==null,d=e.hasOwnProperty(\"onClose\");if(!s&&!d)throw new Error(\"You have to provide an `open` and an `onClose` prop to the `Dialog` component.\");if(!s)throw new Error(\"You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.\");if(!d)throw new Error(\"You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.\");if(!i&&typeof e.open!=\"boolean\")throw new Error(`You provided an \\`open\\` prop to the \\`Dialog\\`, but the value is not a boolean. Received: ${e.open}`);if(typeof e.onClose!=\"function\")throw new Error(`You provided an \\`onClose\\` prop to the \\`Dialog\\`, but the value is not a function. Received: ${e.onClose}`);return(a!==void 0||o)&&!n.static?l.createElement(Y,null,l.createElement(ke,{show:a,transition:o,unmount:n.unmount},l.createElement(z,{ref:t,...n}))):l.createElement(Y,null,l.createElement(z,{ref:t,open:a,...n}))}let $e=\"div\";function je(e,t){let o=k(),{id:a=`headlessui-dialog-panel-${o}`,transition:n=!1,...i}=e,[{dialogState:s,unmount:d},p]=O(\"Dialog.Panel\"),T=G(t,p.panelRef),u=A(()=>({open:s===0}),[s]),y=_(I=>{I.stopPropagation()}),S={ref:T,id:a,onClick:y},F=n?q:$,c=n?{unmount:d}:{},f=h();return l.createElement(F,{...c},f({ourProps:S,theirProps:i,slot:u,defaultTag:$e,name:\"Dialog.Panel\"}))}let Ye=\"div\";function Je(e,t){let{transition:o=!1,...a}=e,[{dialogState:n,unmount:i}]=O(\"Dialog.Backdrop\"),s=A(()=>({open:n===0}),[n]),d={ref:t,\"aria-hidden\":!0},p=o?q:$,T=o?{unmount:i}:{},u=h();return l.createElement(p,{...T},u({ourProps:d,theirProps:a,slot:s,defaultTag:Ye,name:\"Dialog.Backdrop\"}))}let Ke=\"h2\";function Xe(e,t){let o=k(),{id:a=`headlessui-dialog-title-${o}`,...n}=e,[{dialogState:i,setTitleId:s}]=O(\"Dialog.Title\"),d=G(t);fe(()=>(s(a),()=>s(null)),[a,s]);let p=A(()=>({open:i===0}),[i]),T={ref:d,id:a};return h()({ourProps:T,theirProps:n,slot:p,defaultTag:Ke,name:\"Dialog.Title\"})}let Ve=C(We),qe=C(je),bt=C(Je),ze=C(Xe),vt=V,Lt=Object.assign(Ve,{Panel:qe,Title:ze,Description:V});export{Lt as Dialog,bt as DialogBackdrop,vt as DialogDescription,qe as DialogPanel,ze as DialogTitle};\n"], "names": [], "mappings": ";;;;;;;AAAa;AAAqK;AAAuD;AAAkG;AAAkE;AAAuE;AAA6E;AAAkE;AAAoE;AAA6D;AAAoH;AAAgE;AAAuF;AAA2D;AAAkE;AAAuG;AAAsE;AAAiE;AAAgD;AAA8C;AAA4F;AAAkF;AAAgF;AAAuF;AAA7wD;;;;;;;;;;;;;;;;;;;;;;;;;;AAA41D,IAAI,KAAG,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,IAAI,GAAC,EAAE,GAAC,QAAO,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE,GAAC,UAAS,CAAC,CAAC,EAAE,MAAI,CAAC,IAAG,KAAG,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,UAAU,GAAC,EAAE,GAAC,cAAa,CAAC,CAAC,EAAE,MAAI,CAAC;AAAG,IAAI,KAAG;IAAC,CAAC,EAAE,EAAC,CAAC,EAAC,CAAC;QAAE,OAAO,EAAE,OAAO,KAAG,EAAE,EAAE,GAAC,IAAE;YAAC,GAAG,CAAC;YAAC,SAAQ,EAAE,EAAE;QAAA;IAAC;AAAC,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,gBAAE,AAAD,EAAE;AAAM,EAAE,WAAW,GAAC;AAAgB,SAAS,EAAE,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,aAAE,AAAD,EAAE;IAAG,IAAG,MAAI,MAAK;QAAC,IAAI,IAAE,IAAI,MAAM,CAAC,CAAC,EAAE,EAAE,6CAA6C,CAAC;QAAE,MAAM,MAAM,iBAAiB,IAAE,MAAM,iBAAiB,CAAC,GAAE,IAAG;IAAC;IAAC,OAAO;AAAC;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,OAAO,CAAA,GAAA,+JAAA,CAAA,QAAE,AAAD,EAAE,EAAE,IAAI,EAAC,IAAG,GAAE;AAAE;AAAC,IAAI,IAAE,CAAA,GAAA,gKAAA,CAAA,mBAAC,AAAD,EAAE,SAAS,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,QAAC,AAAD,KAAI,EAAC,IAAG,IAAE,CAAC,kBAAkB,EAAE,GAAG,EAAC,MAAK,CAAC,EAAC,SAAQ,CAAC,EAAC,cAAa,CAAC,EAAC,MAAK,IAAE,QAAQ,EAAC,WAAU,IAAE,CAAC,CAAC,EAAC,YAAW,IAAE,CAAC,CAAC,EAAC,SAAQ,IAAE,CAAC,CAAC,EAAC,GAAG,GAAE,GAAC,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,SAAC,AAAD,EAAE,CAAC;IAAG,IAAE;QAAW,OAAO,MAAI,YAAU,MAAI,gBAAc,IAAE,CAAC,EAAE,OAAO,IAAE,CAAC,EAAE,OAAO,GAAC,CAAC,GAAE,QAAQ,IAAI,CAAC,CAAC,cAAc,EAAE,EAAE,wGAAwG,CAAC,CAAC,GAAE,QAAQ;IAAC;IAAI,IAAI,IAAE,CAAA,GAAA,2KAAA,CAAA,gBAAC,AAAD;IAAI,MAAI,KAAK,KAAG,MAAI,QAAM,CAAC,IAAE,CAAC,IAAE,2KAAA,CAAA,QAAC,CAAC,IAAI,MAAI,2KAAA,CAAA,QAAC,CAAC,IAAI;IAAE,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,SAAC,AAAD,EAAE,OAAM,IAAE,CAAA,GAAA,6KAAA,CAAA,cAAC,AAAD,EAAE,GAAE,IAAG,IAAE,CAAA,GAAA,sKAAA,CAAA,mBAAE,AAAD,EAAE,IAAG,IAAE,IAAE,IAAE,GAAE,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,qMAAA,CAAA,aAAE,AAAD,EAAE,IAAG;QAAC,SAAQ;QAAK,eAAc;QAAK,UAAS,CAAA,GAAA,qMAAA,CAAA,YAAE,AAAD;IAAG,IAAG,IAAE,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE,IAAI,EAAE,CAAC,KAAI,IAAE,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE,CAAA,IAAG,EAAE;YAAC,MAAK;YAAE,IAAG;QAAC,KAAI,IAAE,CAAA,GAAA,8LAAA,CAAA,2BAAE,AAAD,MAAI,MAAI,IAAE,CAAC,GAAE,CAAC,GAAE,GAAG,GAAC,CAAA,GAAA,+KAAA,CAAA,mBAAE,AAAD,KAAI,KAAG;QAAC,IAAI,WAAS;YAAC,IAAI;YAAE,OAAM,CAAC,IAAE,EAAE,QAAQ,CAAC,OAAO,KAAG,OAAK,IAAE,EAAE,OAAO;QAAA;IAAC,GAAE,IAAE,CAAA,GAAA,mLAAA,CAAA,kBAAE,AAAD,KAAI,EAAC,mBAAkB,CAAC,EAAC,GAAC,CAAA,GAAA,mLAAA,CAAA,oBAAE,AAAD,EAAE;QAAC,cAAa;QAAE,SAAQ;QAAE,mBAAkB;YAAC;SAAG;IAAA,IAAG,IAAE,MAAI,OAAK,CAAC,IAAE,2KAAA,CAAA,QAAC,CAAC,OAAO,MAAI,2KAAA,CAAA,QAAC,CAAC,OAAO,GAAC,CAAC;IAAE,CAAA,GAAA,gLAAA,CAAA,iBAAE,AAAD,EAAE,KAAG,IAAE,CAAC,IAAE,GAAE;QAAC,SAAQ,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE;YAAK,IAAI,GAAE;YAAE,OAAM;gBAAC,CAAC,IAAE,CAAC,IAAE,EAAE,OAAO,KAAG,OAAK,KAAK,IAAE,EAAE,OAAO,CAAC,2BAA2B,KAAG,OAAK,IAAE;aAAK;QAAA;QAAG,YAAW,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE;YAAK,IAAI;YAAE,OAAM;gBAAC,CAAC,IAAE,KAAG,OAAK,KAAK,IAAE,EAAE,OAAO,CAAC,wCAAwC,KAAG,OAAK,IAAE;aAAK;QAAA;IAAE;IAAG,IAAI,IAAE,6KAAA,CAAA,gBAAE,CAAC,GAAG,CAAC;IAAM,CAAA,GAAA,yLAAA,CAAA,sBAAE,AAAD,EAAE;QAAK,IAAG,GAAE,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,IAAG,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC;IAAE,GAAE;QAAC;QAAE;QAAE;KAAE;IAAE,IAAI,IAAE,CAAA,GAAA,8JAAA,CAAA,WAAE,AAAD,EAAE,GAAE,CAAA,GAAA,qMAAA,CAAA,cAAE,AAAD,EAAE,CAAA,IAAG,EAAE,SAAS,CAAC,KAAK,CAAC,GAAE,IAAG;QAAC;QAAE;KAAE;IAAG,CAAA,GAAA,iLAAA,CAAA,kBAAE,AAAD,EAAE,GAAE,GAAE,CAAA;QAAI,EAAE,cAAc,IAAG;IAAG,IAAG,CAAA,GAAA,uKAAA,CAAA,YAAE,AAAD,EAAE,GAAE,KAAG,OAAK,KAAK,IAAE,EAAE,WAAW,EAAC,CAAA;QAAI,EAAE,cAAc,IAAG,EAAE,eAAe,IAAG,SAAS,aAAa,IAAE,UAAS,SAAS,aAAa,IAAE,OAAO,SAAS,aAAa,CAAC,IAAI,IAAE,cAAY,SAAS,aAAa,CAAC,IAAI,IAAG;IAAG,IAAG,CAAA,GAAA,+KAAA,CAAA,gBAAE,AAAD,EAAE,KAAG,IAAE,CAAC,IAAE,GAAE,GAAE,IAAG,CAAA,GAAA,gLAAA,CAAA,iBAAE,AAAD,EAAE,GAAE,GAAE;IAAG,IAAG,CAAC,IAAG,GAAG,GAAC,CAAA,GAAA,yLAAA,CAAA,kBAAE,AAAD,KAAI,KAAG,CAAA,GAAA,qMAAA,CAAA,UAAC,AAAD,EAAE,IAAI;YAAC;gBAAC,aAAY;gBAAE,OAAM;gBAAE,YAAW;gBAAE,SAAQ;YAAC;YAAE;SAAE,EAAC;QAAC;QAAE;QAAE;QAAE;QAAE;KAAE,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC;YAAC,MAAK,MAAI;QAAC,CAAC,GAAE;QAAC;KAAE,GAAE,KAAG;QAAC,KAAI;QAAE,IAAG;QAAE,MAAK;QAAE,UAAS,CAAC;QAAE,cAAa,IAAE,KAAK,IAAE,MAAI,IAAE,CAAC,IAAE,KAAK;QAAE,mBAAkB,EAAE,OAAO;QAAC,oBAAmB;QAAG,SAAQ;IAAC,GAAE,KAAG,CAAC,CAAA,GAAA,sLAAA,CAAA,mBAAE,AAAD,KAAI,IAAE,6LAAA,CAAA,oBAAC,CAAC,IAAI;IAAC,KAAG,CAAC,KAAG,CAAC,KAAG,6LAAA,CAAA,oBAAC,CAAC,YAAY,EAAC,KAAG,6LAAA,CAAA,oBAAC,CAAC,OAAO,EAAC,KAAG,CAAC,KAAG,6LAAA,CAAA,oBAAC,CAAC,SAAS,GAAE,MAAI,CAAC,KAAG,6LAAA,CAAA,oBAAC,CAAC,YAAY,CAAC;IAAE,IAAI,KAAG,CAAA,GAAA,gKAAA,CAAA,YAAC,AAAD;IAAI,OAAO,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,2KAAA,CAAA,0BAAE,EAAC,MAAK,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,oLAAA,CAAA,kBAAC,EAAC;QAAC,OAAM,CAAC;IAAC,GAAE,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,+KAAA,CAAA,SAAE,EAAC,MAAK,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAC;QAAC,OAAM;IAAE,GAAE,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,+KAAA,CAAA,cAAE,EAAC;QAAC,QAAO;IAAC,GAAE,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,oLAAA,CAAA,kBAAC,EAAC;QAAC,OAAM,CAAC;IAAC,GAAE,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,IAAG;QAAC,MAAK;IAAC,GAAE,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,IAAG,MAAK,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,6LAAA,CAAA,YAAE,EAAC;QAAC,cAAa;QAAE,sBAAqB;QAAE,YAAW;QAAE,UAAS;IAAC,GAAE,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,8KAAA,CAAA,gBAAE,EAAC;QAAC,OAAM;IAAC,GAAE,GAAG;QAAC,UAAS;QAAG,YAAW;QAAE,MAAK;QAAE,YAAW;QAAG,UAAS;QAAG,SAAQ,MAAI;QAAE,MAAK;IAAQ;AAAY,IAAG,KAAG,OAAM,KAAG,gKAAA,CAAA,iBAAC,CAAC,cAAc,GAAC,gKAAA,CAAA,iBAAC,CAAC,MAAM;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAG,EAAC,YAAW,IAAE,CAAC,CAAC,EAAC,MAAK,CAAC,EAAC,GAAG,GAAE,GAAC,GAAE,IAAE,CAAA,GAAA,2KAAA,CAAA,gBAAC,AAAD,KAAI,IAAE,EAAE,cAAc,CAAC,WAAS,MAAI,MAAK,IAAE,EAAE,cAAc,CAAC;IAAW,IAAG,CAAC,KAAG,CAAC,GAAE,MAAM,IAAI,MAAM;IAAkF,IAAG,CAAC,GAAE,MAAM,IAAI,MAAM;IAA8E,IAAG,CAAC,GAAE,MAAM,IAAI,MAAM;IAA8E,IAAG,CAAC,KAAG,OAAO,EAAE,IAAI,IAAE,WAAU,MAAM,IAAI,MAAM,CAAC,2FAA2F,EAAE,EAAE,IAAI,EAAE;IAAE,IAAG,OAAO,EAAE,OAAO,IAAE,YAAW,MAAM,IAAI,MAAM,CAAC,+FAA+F,EAAE,EAAE,OAAO,EAAE;IAAE,OAAM,CAAC,MAAI,KAAK,KAAG,CAAC,KAAG,CAAC,EAAE,MAAM,GAAC,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,mLAAA,CAAA,mBAAC,EAAC,MAAK,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,uLAAA,CAAA,aAAE,EAAC;QAAC,MAAK;QAAE,YAAW;QAAE,SAAQ,EAAE,OAAO;IAAA,GAAE,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,GAAE;QAAC,KAAI;QAAE,GAAG,CAAC;IAAA,OAAK,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,mLAAA,CAAA,mBAAC,EAAC,MAAK,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,GAAE;QAAC,KAAI;QAAE,MAAK;QAAE,GAAG,CAAC;IAAA;AAAG;AAAC,IAAI,KAAG;AAAM,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,QAAC,AAAD,KAAI,EAAC,IAAG,IAAE,CAAC,wBAAwB,EAAE,GAAG,EAAC,YAAW,IAAE,CAAC,CAAC,EAAC,GAAG,GAAE,GAAC,GAAE,CAAC,EAAC,aAAY,CAAC,EAAC,SAAQ,CAAC,EAAC,EAAC,EAAE,GAAC,EAAE,iBAAgB,IAAE,CAAA,GAAA,6KAAA,CAAA,cAAC,AAAD,EAAE,GAAE,EAAE,QAAQ,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC;YAAC,MAAK,MAAI;QAAC,CAAC,GAAE;QAAC;KAAE,GAAE,IAAE,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,EAAE,eAAe;IAAE,IAAG,IAAE;QAAC,KAAI;QAAE,IAAG;QAAE,SAAQ;IAAC,GAAE,IAAE,IAAE,uLAAA,CAAA,kBAAC,GAAC,qMAAA,CAAA,WAAC,EAAC,IAAE,IAAE;QAAC,SAAQ;IAAC,IAAE,CAAC,GAAE,IAAE,CAAA,GAAA,gKAAA,CAAA,YAAC,AAAD;IAAI,OAAO,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,GAAE;QAAC,GAAG,CAAC;IAAA,GAAE,EAAE;QAAC,UAAS;QAAE,YAAW;QAAE,MAAK;QAAE,YAAW;QAAG,MAAK;IAAc;AAAG;AAAC,IAAI,KAAG;AAAM,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAG,EAAC,YAAW,IAAE,CAAC,CAAC,EAAC,GAAG,GAAE,GAAC,GAAE,CAAC,EAAC,aAAY,CAAC,EAAC,SAAQ,CAAC,EAAC,CAAC,GAAC,EAAE,oBAAmB,IAAE,CAAA,GAAA,qMAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC;YAAC,MAAK,MAAI;QAAC,CAAC,GAAE;QAAC;KAAE,GAAE,IAAE;QAAC,KAAI;QAAE,eAAc,CAAC;IAAC,GAAE,IAAE,IAAE,uLAAA,CAAA,kBAAC,GAAC,qMAAA,CAAA,WAAC,EAAC,IAAE,IAAE;QAAC,SAAQ;IAAC,IAAE,CAAC,GAAE,IAAE,CAAA,GAAA,gKAAA,CAAA,YAAC,AAAD;IAAI,OAAO,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,GAAE;QAAC,GAAG,CAAC;IAAA,GAAE,EAAE;QAAC,UAAS;QAAE,YAAW;QAAE,MAAK;QAAE,YAAW;QAAG,MAAK;IAAiB;AAAG;AAAC,IAAI,KAAG;AAAK,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,QAAC,AAAD,KAAI,EAAC,IAAG,IAAE,CAAC,wBAAwB,EAAE,GAAG,EAAC,GAAG,GAAE,GAAC,GAAE,CAAC,EAAC,aAAY,CAAC,EAAC,YAAW,CAAC,EAAC,CAAC,GAAC,EAAE,iBAAgB,IAAE,CAAA,GAAA,6KAAA,CAAA,cAAC,AAAD,EAAE;IAAG,CAAA,GAAA,qMAAA,CAAA,YAAE,AAAD,EAAE,IAAI,CAAC,EAAE,IAAG,IAAI,EAAE,KAAK,GAAE;QAAC;QAAE;KAAE;IAAE,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC;YAAC,MAAK,MAAI;QAAC,CAAC,GAAE;QAAC;KAAE,GAAE,IAAE;QAAC,KAAI;QAAE,IAAG;IAAC;IAAE,OAAO,CAAA,GAAA,gKAAA,CAAA,YAAC,AAAD,IAAI;QAAC,UAAS;QAAE,YAAW;QAAE,MAAK;QAAE,YAAW;QAAG,MAAK;IAAc;AAAE;AAAC,IAAI,KAAG,CAAA,GAAA,gKAAA,CAAA,mBAAC,AAAD,EAAE,KAAI,KAAG,CAAA,GAAA,gKAAA,CAAA,mBAAC,AAAD,EAAE,KAAI,KAAG,CAAA,GAAA,gKAAA,CAAA,mBAAC,AAAD,EAAE,KAAI,KAAG,CAAA,GAAA,gKAAA,CAAA,mBAAC,AAAD,EAAE,KAAI,KAAG,yLAAA,CAAA,cAAC,EAAC,KAAG,OAAO,MAAM,CAAC,IAAG;IAAC,OAAM;IAAG,OAAM;IAAG,aAAY,yLAAA,CAAA,cAAC;AAAA", "ignoreList": [0], "debugId": null}}]}