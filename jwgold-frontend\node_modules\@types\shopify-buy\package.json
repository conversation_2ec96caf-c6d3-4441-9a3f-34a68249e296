{"name": "@types/shopify-buy", "version": "3.0.0", "description": "TypeScript definitions for shopify-buy", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/shopify-buy", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "openminder", "url": "https://github.com/openminder"}, {"name": "<PERSON>", "githubUsername": "straiforos", "url": "https://github.com/straiforos"}, {"name": "<PERSON>", "githubUsername": "kilink<PERSON>", "url": "https://github.com/kilinkis"}, {"name": "<PERSON>", "githubUsername": "chris<PERSON><PERSON>-pela", "url": "https://github.com/chris<PERSON>an-pela"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON>iekBaro<PERSON>", "url": "https://github.com/MaciekBaron"}, {"name": "<PERSON>", "githubUsername": "martin-badin", "url": "https://github.com/martin-badin"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/shopify-buy"}, "scripts": {}, "dependencies": {}, "peerDependencies": {}, "typesPublisherContentHash": "ae4729ad860f263be67c9c3866d0fb3bedf2d6f1f8e76329cac260f44b7f6651", "typeScriptVersion": "5.0"}