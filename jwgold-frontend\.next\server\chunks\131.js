exports.id=131,exports.ids=[131],exports.modules={721:(a,b,c)=>{Promise.resolve().then(c.bind(c,7182)),Promise.resolve().then(c.bind(c,4237)),Promise.resolve().then(c.t.bind(c,4536,23))},1423:(a,b,c)=>{"use strict";c.d(b,{CartProvider:()=>k,_:()=>l});var d=c(687),e=c(3210),f=c(7158),g=c(5659);let h={items:[],checkoutId:null,checkoutUrl:null,isLoading:!1,totalPrice:{amount:"0",currencyCode:"USD"},totalQuantity:0};function i(a,b){switch(b.type){case"SET_LOADING":return{...a,isLoading:b.payload};case"SET_CHECKOUT":return{...a,checkoutId:b.payload.checkoutId,checkoutUrl:b.payload.checkoutUrl};case"ADD_ITEM":if(a.items.find(a=>a.variantId===b.payload.variantId))return{...a,items:a.items.map(a=>a.variantId===b.payload.variantId?{...a,quantity:a.quantity+b.payload.quantity}:a)};return{...a,items:[...a.items,b.payload]};case"UPDATE_ITEM":return{...a,items:a.items.map(a=>a.id===b.payload.id?{...a,quantity:b.payload.quantity}:a).filter(a=>a.quantity>0)};case"REMOVE_ITEM":return{...a,items:a.items.filter(a=>a.id!==b.payload)};case"CLEAR_CART":return{...a,items:[]};case"SET_CART":return{...a,items:b.payload};case"UPDATE_TOTALS":return{...a,totalPrice:b.payload.totalPrice,totalQuantity:b.payload.totalQuantity};default:return a}}let j=(0,e.createContext)(void 0);function k({children:a}){let[b,c]=(0,e.useReducer)(i,h),k=async()=>{try{c({type:"SET_LOADING",payload:!0});let a=await f.h.createCheckout();a&&(c({type:"SET_CHECKOUT",payload:{checkoutId:a.id,checkoutUrl:a.webUrl}}),g.A.set("checkoutId",a.id,{expires:7}))}catch(a){console.error("Error initializing checkout:",a)}finally{c({type:"SET_LOADING",payload:!1})}},l=async a=>{if(c({type:"ADD_ITEM",payload:{...a,id:`${a.variantId}-${Date.now()}`}}),b.checkoutId)try{let c=[{variantId:a.variantId,quantity:a.quantity}];await f.h.addToCheckout(b.checkoutId,c)}catch(a){console.error("Error adding to Shopify checkout:",a)}},m=async(a,b)=>{c({type:"UPDATE_ITEM",payload:{id:a,quantity:b}})},n=async a=>{c({type:"REMOVE_ITEM",payload:a})};return(0,d.jsx)(j.Provider,{value:{...b,addToCart:l,updateCartItem:m,removeFromCart:n,clearCart:()=>{c({type:"CLEAR_CART"}),g.A.remove("cart")},initializeCheckout:k},children:a})}function l(){let a=(0,e.useContext)(j);if(void 0===a)throw Error("useCart must be used within a CartProvider");return a}},1910:(a,b,c)=>{"use strict";c.d(b,{default:()=>u});var d=c(687),e=c(3210),f=c(5814),g=c.n(f),h=c(1423),i=c(6510),j=c(922),k=c(6719),l=c(1836),m=c(3516),n=c(8908),o=c(7337),p=c(7891),q=c(7158),r=c(474);function s({open:a,setOpen:b}){let{items:c,totalPrice:f,totalQuantity:i,updateCartItem:j,removeFromCart:k,checkoutUrl:s}=(0,h._)(),t=(a,b)=>{b<=0?k(a):j(a,b)};return(0,d.jsx)(m.e.Root,{show:a,as:e.Fragment,children:(0,d.jsxs)(n.lG,{as:"div",className:"relative z-50",onClose:b,children:[(0,d.jsx)(m.e.Child,{as:e.Fragment,enter:"ease-in-out duration-500",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in-out duration-500",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,d.jsx)("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"})}),(0,d.jsx)("div",{className:"fixed inset-0 overflow-hidden",children:(0,d.jsx)("div",{className:"absolute inset-0 overflow-hidden",children:(0,d.jsx)("div",{className:"pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10",children:(0,d.jsx)(m.e.Child,{as:e.Fragment,enter:"transform transition ease-in-out duration-500 sm:duration-700",enterFrom:"translate-x-full",enterTo:"translate-x-0",leave:"transform transition ease-in-out duration-500 sm:duration-700",leaveFrom:"translate-x-0",leaveTo:"translate-x-full",children:(0,d.jsx)(n.lG.Panel,{className:"pointer-events-auto w-screen max-w-md",children:(0,d.jsxs)("div",{className:"flex h-full flex-col overflow-y-scroll bg-white shadow-xl",children:[(0,d.jsxs)("div",{className:"flex-1 overflow-y-auto px-4 py-6 sm:px-6",children:[(0,d.jsxs)("div",{className:"flex items-start justify-between",children:[(0,d.jsx)(n.lG.Title,{className:"text-lg font-medium text-gray-900",children:"Shopping cart"}),(0,d.jsx)("div",{className:"ml-3 flex h-7 items-center",children:(0,d.jsxs)("button",{type:"button",className:"-m-2 p-2 text-gray-400 hover:text-gray-500",onClick:()=>b(!1),children:[(0,d.jsx)("span",{className:"sr-only",children:"Close panel"}),(0,d.jsx)(l.A,{className:"h-6 w-6","aria-hidden":"true"})]})})]}),(0,d.jsx)("div",{className:"mt-8",children:(0,d.jsx)("div",{className:"flow-root",children:0===c.length?(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)("p",{className:"text-gray-500 mb-4",children:"Your cart is empty"}),(0,d.jsx)(g(),{href:"/collections",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700",onClick:()=>b(!1),children:"Continue Shopping"})]}):(0,d.jsx)("ul",{role:"list",className:"-my-6 divide-y divide-gray-200",children:c.map(a=>(0,d.jsxs)("li",{className:"flex py-6",children:[(0,d.jsx)("div",{className:"h-24 w-24 flex-shrink-0 overflow-hidden rounded-md border border-gray-200",children:a.image?(0,d.jsx)(r.default,{src:a.image.src,alt:a.image.altText||a.title,width:96,height:96,className:"h-full w-full object-cover object-center"}):(0,d.jsx)("div",{className:"h-full w-full bg-gray-200 flex items-center justify-center",children:(0,d.jsx)("span",{className:"text-gray-400 text-xs",children:"No image"})})}),(0,d.jsxs)("div",{className:"ml-4 flex flex-1 flex-col",children:[(0,d.jsx)("div",{children:(0,d.jsxs)("div",{className:"flex justify-between text-base font-medium text-gray-900",children:[(0,d.jsx)("h3",{children:(0,d.jsx)(g(),{href:`/products/${a.handle}`,onClick:()=>b(!1),children:a.title})}),(0,d.jsx)("p",{className:"ml-4",children:q.h.formatPrice(a.price)})]})}),(0,d.jsxs)("div",{className:"flex flex-1 items-end justify-between text-sm",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("button",{type:"button",className:"p-1 text-gray-400 hover:text-gray-500",onClick:()=>t(a.id,a.quantity-1),children:(0,d.jsx)(o.A,{className:"h-4 w-4"})}),(0,d.jsxs)("span",{className:"text-gray-500 min-w-[2rem] text-center",children:["Qty ",a.quantity]}),(0,d.jsx)("button",{type:"button",className:"p-1 text-gray-400 hover:text-gray-500",onClick:()=>t(a.id,a.quantity+1),children:(0,d.jsx)(p.A,{className:"h-4 w-4"})})]}),(0,d.jsx)("div",{className:"flex",children:(0,d.jsx)("button",{type:"button",className:"font-medium text-indigo-600 hover:text-indigo-500",onClick:()=>k(a.id),children:"Remove"})})]})]})]},a.id))})})})]}),c.length>0&&(0,d.jsxs)("div",{className:"border-t border-gray-200 px-4 py-6 sm:px-6",children:[(0,d.jsxs)("div",{className:"flex justify-between text-base font-medium text-gray-900",children:[(0,d.jsx)("p",{children:"Subtotal"}),(0,d.jsx)("p",{children:q.h.formatPrice(f)})]}),(0,d.jsx)("p",{className:"mt-0.5 text-sm text-gray-500",children:"Shipping and taxes calculated at checkout."}),(0,d.jsx)("div",{className:"mt-6",children:s?(0,d.jsx)("a",{href:s,className:"flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-6 py-3 text-base font-medium text-white shadow-sm hover:bg-indigo-700 transition-colors duration-200",children:"Checkout"}):(0,d.jsx)("button",{disabled:!0,className:"flex items-center justify-center rounded-md border border-transparent bg-gray-300 px-6 py-3 text-base font-medium text-gray-500 cursor-not-allowed",children:"Loading..."})}),(0,d.jsx)("div",{className:"mt-6 flex justify-center text-center text-sm text-gray-500",children:(0,d.jsxs)("p",{children:["or"," ",(0,d.jsxs)("button",{type:"button",className:"font-medium text-indigo-600 hover:text-indigo-500",onClick:()=>b(!1),children:["Continue Shopping",(0,d.jsx)("span",{"aria-hidden":"true",children:" →"})]})]})})]})]})})})})})})]})})}let t=[{name:"Home",href:"/"},{name:"Collections",href:"/collections"},{name:"Rings",href:"/collections/rings"},{name:"Necklaces",href:"/collections/necklaces"},{name:"Earrings",href:"/collections/earrings"},{name:"Bracelets",href:"/collections/bracelets"},{name:"About",href:"/about"},{name:"Contact",href:"/contact"}];function u(){let[a,b]=(0,e.useState)(!1),[c,f]=(0,e.useState)(!1),{totalQuantity:o}=(0,h._)();return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("header",{className:"bg-white shadow-sm sticky top-0 z-50",children:[(0,d.jsx)("nav",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8","aria-label":"Top",children:(0,d.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,d.jsx)("div",{className:"flex lg:hidden",children:(0,d.jsxs)("button",{type:"button",className:"-ml-2 rounded-md bg-white p-2 text-gray-400",onClick:()=>b(!0),children:[(0,d.jsx)("span",{className:"sr-only",children:"Open menu"}),(0,d.jsx)(i.A,{className:"h-6 w-6","aria-hidden":"true"})]})}),(0,d.jsx)("div",{className:"flex lg:flex-1",children:(0,d.jsxs)(g(),{href:"/",className:"-m-1.5 p-1.5",children:[(0,d.jsx)("span",{className:"sr-only",children:"JW GOLD"}),(0,d.jsx)("h1",{className:"text-2xl font-bold font-playfair text-gray-900 tracking-wide",children:"JW GOLD"})]})}),(0,d.jsx)("div",{className:"hidden lg:flex lg:gap-x-8",children:t.map(a=>(0,d.jsx)(g(),{href:a.href,className:"text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors duration-200",children:a.name},a.name))}),(0,d.jsxs)("div",{className:"flex items-center gap-x-4 lg:flex-1 lg:justify-end",children:[(0,d.jsxs)("button",{type:"button",className:"rounded-md bg-white p-2 text-gray-400 hover:text-gray-500",children:[(0,d.jsx)("span",{className:"sr-only",children:"Search"}),(0,d.jsx)(j.A,{className:"h-6 w-6","aria-hidden":"true"})]}),(0,d.jsxs)("button",{type:"button",className:"relative rounded-md bg-white p-2 text-gray-400 hover:text-gray-500",onClick:()=>f(!0),children:[(0,d.jsx)("span",{className:"sr-only",children:"Shopping cart"}),(0,d.jsx)(k.A,{className:"h-6 w-6","aria-hidden":"true"}),o>0&&(0,d.jsx)("span",{className:"absolute -top-1 -right-1 h-5 w-5 rounded-full bg-indigo-600 flex items-center justify-center text-xs font-medium text-white",children:o})]})]})]})}),(0,d.jsx)(m.e.Root,{show:a,as:e.Fragment,children:(0,d.jsxs)(n.lG,{as:"div",className:"relative z-50 lg:hidden",onClose:b,children:[(0,d.jsx)(m.e.Child,{as:e.Fragment,enter:"transition-opacity ease-linear duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"transition-opacity ease-linear duration-300",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),(0,d.jsx)("div",{className:"fixed inset-0 z-50 flex",children:(0,d.jsx)(m.e.Child,{as:e.Fragment,enter:"transition ease-in-out duration-300 transform",enterFrom:"-translate-x-full",enterTo:"translate-x-0",leave:"transition ease-in-out duration-300 transform",leaveFrom:"translate-x-0",leaveTo:"-translate-x-full",children:(0,d.jsxs)(n.lG.Panel,{className:"relative flex w-full max-w-xs flex-col overflow-y-auto bg-white pb-12 shadow-xl",children:[(0,d.jsx)("div",{className:"flex px-4 pb-2 pt-5",children:(0,d.jsxs)("button",{type:"button",className:"-m-2 inline-flex items-center justify-center rounded-md p-2 text-gray-400",onClick:()=>b(!1),children:[(0,d.jsx)("span",{className:"sr-only",children:"Close menu"}),(0,d.jsx)(l.A,{className:"h-6 w-6","aria-hidden":"true"})]})}),(0,d.jsx)("div",{className:"space-y-6 border-t border-gray-200 px-4 py-6",children:t.map(a=>(0,d.jsx)("div",{className:"flow-root",children:(0,d.jsx)(g(),{href:a.href,className:"-m-2 block p-2 font-medium text-gray-900",onClick:()=>b(!1),children:a.name})},a.name))})]})})})]})})]}),(0,d.jsx)(s,{open:c,setOpen:f})]})}},2007:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>p,metadata:()=>o});var d=c(7413),e=c(9141),f=c.n(e),g=c(9858),h=c.n(g);c(2704);var i=c(4237),j=c(7182),k=c(4536),l=c.n(k);let m={shop:[{name:"All Collections",href:"/collections"},{name:"Rings",href:"/collections/rings"},{name:"Necklaces",href:"/collections/necklaces"},{name:"Earrings",href:"/collections/earrings"},{name:"Bracelets",href:"/collections/bracelets"}],company:[{name:"About Us",href:"/about"},{name:"Contact",href:"/contact"},{name:"Size Guide",href:"/size-guide"},{name:"Care Instructions",href:"/care"}],support:[{name:"Shipping & Returns",href:"/shipping"},{name:"FAQ",href:"/faq"},{name:"Privacy Policy",href:"/privacy"},{name:"Terms of Service",href:"/terms"}],social:[{name:"Facebook",href:"#",icon:a=>(0,d.jsx)("svg",{fill:"currentColor",viewBox:"0 0 24 24",...a,children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z",clipRule:"evenodd"})})},{name:"Instagram",href:"#",icon:a=>(0,d.jsx)("svg",{fill:"currentColor",viewBox:"0 0 24 24",...a,children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987s11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.418-3.323c.875-.875 2.026-1.297 3.323-1.297s2.448.422 3.323 1.297c.928.875 1.418 2.026 1.418 3.323s-.49 2.448-1.418 3.244c-.875.807-2.026 1.297-3.323 1.297zm7.83-9.405c-.49 0-.875-.385-.875-.875s.385-.875.875-.875.875.385.875.875-.385.875-.875.875zm-3.323 9.405c-2.026 0-3.708-1.682-3.708-3.708s1.682-3.708 3.708-3.708 3.708 1.682 3.708 3.708-1.682 3.708-3.708 3.708z",clipRule:"evenodd"})})},{name:"Twitter",href:"#",icon:a=>(0,d.jsx)("svg",{fill:"currentColor",viewBox:"0 0 24 24",...a,children:(0,d.jsx)("path",{d:"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"})})}]};function n(){return(0,d.jsxs)("footer",{className:"bg-gray-900","aria-labelledby":"footer-heading",children:[(0,d.jsx)("h2",{id:"footer-heading",className:"sr-only",children:"Footer"}),(0,d.jsxs)("div",{className:"mx-auto max-w-7xl px-6 pb-8 pt-16 sm:pt-24 lg:px-8 lg:pt-32",children:[(0,d.jsxs)("div",{className:"xl:grid xl:grid-cols-3 xl:gap-8",children:[(0,d.jsxs)("div",{className:"space-y-8",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-3xl font-bold font-playfair text-white tracking-wide",children:"JW GOLD"}),(0,d.jsx)("p",{className:"mt-4 text-sm leading-6 text-gray-300",children:"Crafting exquisite jewelry pieces that celebrate life's precious moments. Each piece is meticulously designed with the finest materials and attention to detail."})]}),(0,d.jsx)("div",{className:"flex space-x-6",children:m.social.map(a=>(0,d.jsxs)("a",{href:a.href,className:"text-gray-400 hover:text-gray-300",children:[(0,d.jsx)("span",{className:"sr-only",children:a.name}),(0,d.jsx)(a.icon,{className:"h-6 w-6","aria-hidden":"true"})]},a.name))})]}),(0,d.jsxs)("div",{className:"mt-16 grid grid-cols-2 gap-8 xl:col-span-2 xl:mt-0",children:[(0,d.jsxs)("div",{className:"md:grid md:grid-cols-2 md:gap-8",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-sm font-semibold leading-6 text-white",children:"Shop"}),(0,d.jsx)("ul",{role:"list",className:"mt-6 space-y-4",children:m.shop.map(a=>(0,d.jsx)("li",{children:(0,d.jsx)(l(),{href:a.href,className:"text-sm leading-6 text-gray-300 hover:text-white",children:a.name})},a.name))})]}),(0,d.jsxs)("div",{className:"mt-10 md:mt-0",children:[(0,d.jsx)("h3",{className:"text-sm font-semibold leading-6 text-white",children:"Company"}),(0,d.jsx)("ul",{role:"list",className:"mt-6 space-y-4",children:m.company.map(a=>(0,d.jsx)("li",{children:(0,d.jsx)(l(),{href:a.href,className:"text-sm leading-6 text-gray-300 hover:text-white",children:a.name})},a.name))})]})]}),(0,d.jsxs)("div",{className:"md:grid md:grid-cols-2 md:gap-8",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-sm font-semibold leading-6 text-white",children:"Support"}),(0,d.jsx)("ul",{role:"list",className:"mt-6 space-y-4",children:m.support.map(a=>(0,d.jsx)("li",{children:(0,d.jsx)(l(),{href:a.href,className:"text-sm leading-6 text-gray-300 hover:text-white",children:a.name})},a.name))})]}),(0,d.jsxs)("div",{className:"mt-10 md:mt-0",children:[(0,d.jsx)("h3",{className:"text-sm font-semibold leading-6 text-white",children:"Newsletter"}),(0,d.jsx)("p",{className:"mt-2 text-sm leading-6 text-gray-300",children:"Subscribe to get special offers, free giveaways, and exclusive deals."}),(0,d.jsxs)("form",{className:"mt-6 sm:flex sm:max-w-md",children:[(0,d.jsx)("label",{htmlFor:"email-address",className:"sr-only",children:"Email address"}),(0,d.jsx)("input",{type:"email",name:"email-address",id:"email-address",autoComplete:"email",required:!0,className:"w-full min-w-0 appearance-none rounded-md border-0 bg-white/5 px-3 py-1.5 text-base text-white shadow-sm ring-1 ring-inset ring-white/10 placeholder:text-gray-500 focus:ring-2 focus:ring-inset focus:ring-indigo-500 sm:w-64 sm:text-sm sm:leading-6 xl:w-full",placeholder:"Enter your email"}),(0,d.jsx)("div",{className:"mt-4 sm:ml-4 sm:mt-0 sm:flex-shrink-0",children:(0,d.jsx)("button",{type:"submit",className:"flex w-full items-center justify-center rounded-md bg-indigo-500 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-400 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-500",children:"Subscribe"})})]})]})]})]})]}),(0,d.jsx)("div",{className:"mt-16 border-t border-white/10 pt-8 sm:mt-20 lg:mt-24",children:(0,d.jsx)("p",{className:"text-xs leading-5 text-gray-400",children:"\xa9 2024 JW GOLD. All rights reserved."})})]})]})}let o={title:"JW GOLD - Luxury Jewelry Collection",description:"Discover exquisite handcrafted jewelry at JW GOLD. Premium gold, silver, and diamond pieces for every occasion.",keywords:"jewelry, gold, silver, diamonds, luxury, handcrafted, JW GOLD"};function p({children:a}){return(0,d.jsx)("html",{lang:"en",children:(0,d.jsx)("body",{className:`${f().variable} ${h().variable} antialiased bg-white text-gray-900`,children:(0,d.jsxs)(i.CartProvider,{children:[(0,d.jsx)(j.default,{}),(0,d.jsx)("main",{className:"min-h-screen",children:a}),(0,d.jsx)(n,{})]})})})}},2328:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,6133,23)),Promise.resolve().then(c.t.bind(c,6444,23)),Promise.resolve().then(c.t.bind(c,6042,23)),Promise.resolve().then(c.t.bind(c,9477,23)),Promise.resolve().then(c.t.bind(c,9345,23)),Promise.resolve().then(c.t.bind(c,2089,23)),Promise.resolve().then(c.t.bind(c,6577,23)),Promise.resolve().then(c.t.bind(c,1307,23)),Promise.resolve().then(c.t.bind(c,4817,23))},2704:()=>{},4237:(a,b,c)=>{"use strict";c.d(b,{CartProvider:()=>e});var d=c(1369);let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call CartProvider() from the server but CartProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\jwgold\\jwgold-frontend\\context\\CartContext.tsx","CartProvider");(0,d.registerClientReference)(function(){throw Error("Attempted to call useCart() from the server but useCart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\jwgold\\jwgold-frontend\\context\\CartContext.tsx","useCart")},6055:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(1658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},6150:(a,b,c)=>{Promise.resolve().then(c.bind(c,1910)),Promise.resolve().then(c.bind(c,1423)),Promise.resolve().then(c.t.bind(c,5814,23))},7158:(a,b,c)=>{"use strict";c.d(b,{h:()=>f});var d=c(1157);let e=c.n(d)().buildClient({domain:"your-store.myshopify.com",storefrontAccessToken:"your-storefront-access-token"}),f={async fetchProducts(a=20){try{return await e.product.fetchAll(a)}catch(a){return console.error("Error fetching products:",a),[]}},async fetchProductByHandle(a){try{return(await e.product.fetchAll()).find(b=>b.handle===a)}catch(a){return console.error("Error fetching product:",a),null}},async fetchCollections(a=10){try{return await e.collection.fetchAll(a)}catch(a){return console.error("Error fetching collections:",a),[]}},async createCheckout(){try{return await e.checkout.create()}catch(a){return console.error("Error creating checkout:",a),null}},async addToCheckout(a,b){try{return await e.checkout.addLineItems(a,b)}catch(a){return console.error("Error adding to checkout:",a),null}},async updateCheckout(a,b){try{return await e.checkout.updateLineItems(a,b)}catch(a){return console.error("Error updating checkout:",a),null}},async removeFromCheckout(a,b){try{return await e.checkout.removeLineItems(a,b)}catch(a){return console.error("Error removing from checkout:",a),null}},formatPrice:a=>new Intl.NumberFormat("en-US",{style:"currency",currency:a.currencyCode}).format(parseFloat(a.amount))}},7182:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\jwgold\\\\jwgold-frontend\\\\components\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\jwgold\\jwgold-frontend\\components\\Header.tsx","default")},9176:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,5227,23)),Promise.resolve().then(c.t.bind(c,6346,23)),Promise.resolve().then(c.t.bind(c,7924,23)),Promise.resolve().then(c.t.bind(c,99,23)),Promise.resolve().then(c.t.bind(c,8243,23)),Promise.resolve().then(c.t.bind(c,8827,23)),Promise.resolve().then(c.t.bind(c,2763,23)),Promise.resolve().then(c.t.bind(c,7173,23)),Promise.resolve().then(c.bind(c,5587))}};