(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{3595:(e,t,a)=>{"use strict";a.d(t,{default:()=>v});var r=a(5155),s=a(2115),n=a(6874),i=a.n(n),l=a(6515),c=a(9598),o=a(8046),d=a(527),m=a(4500),h=a(5939),u=a(280),x=a(7165),y=a(7765),p=a(7120),f=a(6766);function g(e){let{open:t,setOpen:a}=e,{items:n,totalPrice:c,totalQuantity:o,updateCartItem:d,removeFromCart:g,checkoutUrl:j}=(0,l._)(),v=(e,t)=>{t<=0?g(e):d(e,t)};return(0,r.jsx)(h.e.Root,{show:t,as:s.Fragment,children:(0,r.jsxs)(u.lG,{as:"div",className:"relative z-50",onClose:a,children:[(0,r.jsx)(h.e.Child,{as:s.Fragment,enter:"ease-in-out duration-500",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in-out duration-500",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,r.jsx)("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"})}),(0,r.jsx)("div",{className:"fixed inset-0 overflow-hidden",children:(0,r.jsx)("div",{className:"absolute inset-0 overflow-hidden",children:(0,r.jsx)("div",{className:"pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10",children:(0,r.jsx)(h.e.Child,{as:s.Fragment,enter:"transform transition ease-in-out duration-500 sm:duration-700",enterFrom:"translate-x-full",enterTo:"translate-x-0",leave:"transform transition ease-in-out duration-500 sm:duration-700",leaveFrom:"translate-x-0",leaveTo:"translate-x-full",children:(0,r.jsx)(u.lG.Panel,{className:"pointer-events-auto w-screen max-w-md",children:(0,r.jsxs)("div",{className:"flex h-full flex-col overflow-y-scroll bg-white shadow-xl",children:[(0,r.jsxs)("div",{className:"flex-1 overflow-y-auto px-4 py-6 sm:px-6",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsx)(u.lG.Title,{className:"text-lg font-medium text-gray-900",children:"Shopping cart"}),(0,r.jsx)("div",{className:"ml-3 flex h-7 items-center",children:(0,r.jsxs)("button",{type:"button",className:"-m-2 p-2 text-gray-400 hover:text-gray-500",onClick:()=>a(!1),children:[(0,r.jsx)("span",{className:"sr-only",children:"Close panel"}),(0,r.jsx)(m.A,{className:"h-6 w-6","aria-hidden":"true"})]})})]}),(0,r.jsx)("div",{className:"mt-8",children:(0,r.jsx)("div",{className:"flow-root",children:0===n.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("p",{className:"text-gray-500 mb-4",children:"Your cart is empty"}),(0,r.jsx)(i(),{href:"/collections",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700",onClick:()=>a(!1),children:"Continue Shopping"})]}):(0,r.jsx)("ul",{role:"list",className:"-my-6 divide-y divide-gray-200",children:n.map(e=>(0,r.jsxs)("li",{className:"flex py-6",children:[(0,r.jsx)("div",{className:"h-24 w-24 flex-shrink-0 overflow-hidden rounded-md border border-gray-200",children:e.image?(0,r.jsx)(f.default,{src:e.image.src,alt:e.image.altText||e.title,width:96,height:96,className:"h-full w-full object-cover object-center"}):(0,r.jsx)("div",{className:"h-full w-full bg-gray-200 flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-gray-400 text-xs",children:"No image"})})}),(0,r.jsxs)("div",{className:"ml-4 flex flex-1 flex-col",children:[(0,r.jsx)("div",{children:(0,r.jsxs)("div",{className:"flex justify-between text-base font-medium text-gray-900",children:[(0,r.jsx)("h3",{children:(0,r.jsx)(i(),{href:"/products/".concat(e.handle),onClick:()=>a(!1),children:e.title})}),(0,r.jsx)("p",{className:"ml-4",children:p.h.formatPrice(e.price)})]})}),(0,r.jsxs)("div",{className:"flex flex-1 items-end justify-between text-sm",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{type:"button",className:"p-1 text-gray-400 hover:text-gray-500",onClick:()=>v(e.id,e.quantity-1),children:(0,r.jsx)(x.A,{className:"h-4 w-4"})}),(0,r.jsxs)("span",{className:"text-gray-500 min-w-[2rem] text-center",children:["Qty ",e.quantity]}),(0,r.jsx)("button",{type:"button",className:"p-1 text-gray-400 hover:text-gray-500",onClick:()=>v(e.id,e.quantity+1),children:(0,r.jsx)(y.A,{className:"h-4 w-4"})})]}),(0,r.jsx)("div",{className:"flex",children:(0,r.jsx)("button",{type:"button",className:"font-medium text-indigo-600 hover:text-indigo-500",onClick:()=>g(e.id),children:"Remove"})})]})]})]},e.id))})})})]}),n.length>0&&(0,r.jsxs)("div",{className:"border-t border-gray-200 px-4 py-6 sm:px-6",children:[(0,r.jsxs)("div",{className:"flex justify-between text-base font-medium text-gray-900",children:[(0,r.jsx)("p",{children:"Subtotal"}),(0,r.jsx)("p",{children:p.h.formatPrice(c)})]}),(0,r.jsx)("p",{className:"mt-0.5 text-sm text-gray-500",children:"Shipping and taxes calculated at checkout."}),(0,r.jsx)("div",{className:"mt-6",children:j?(0,r.jsx)("a",{href:j,className:"flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-6 py-3 text-base font-medium text-white shadow-sm hover:bg-indigo-700 transition-colors duration-200",children:"Checkout"}):(0,r.jsx)("button",{disabled:!0,className:"flex items-center justify-center rounded-md border border-transparent bg-gray-300 px-6 py-3 text-base font-medium text-gray-500 cursor-not-allowed",children:"Loading..."})}),(0,r.jsx)("div",{className:"mt-6 flex justify-center text-center text-sm text-gray-500",children:(0,r.jsxs)("p",{children:["or"," ",(0,r.jsxs)("button",{type:"button",className:"font-medium text-indigo-600 hover:text-indigo-500",onClick:()=>a(!1),children:["Continue Shopping",(0,r.jsx)("span",{"aria-hidden":"true",children:" →"})]})]})})]})]})})})})})})]})})}let j=[{name:"Home",href:"/"},{name:"Collections",href:"/collections"},{name:"Rings",href:"/collections/rings"},{name:"Necklaces",href:"/collections/necklaces"},{name:"Earrings",href:"/collections/earrings"},{name:"Bracelets",href:"/collections/bracelets"},{name:"About",href:"/about"},{name:"Contact",href:"/contact"}];function v(){let[e,t]=(0,s.useState)(!1),[a,n]=(0,s.useState)(!1),{totalQuantity:x}=(0,l._)();return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("header",{className:"bg-white shadow-sm sticky top-0 z-50",children:[(0,r.jsx)("nav",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8","aria-label":"Top",children:(0,r.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,r.jsx)("div",{className:"flex lg:hidden",children:(0,r.jsxs)("button",{type:"button",className:"-ml-2 rounded-md bg-white p-2 text-gray-400",onClick:()=>t(!0),children:[(0,r.jsx)("span",{className:"sr-only",children:"Open menu"}),(0,r.jsx)(c.A,{className:"h-6 w-6","aria-hidden":"true"})]})}),(0,r.jsx)("div",{className:"flex lg:flex-1",children:(0,r.jsxs)(i(),{href:"/",className:"-m-1.5 p-1.5",children:[(0,r.jsx)("span",{className:"sr-only",children:"JW GOLD"}),(0,r.jsx)("h1",{className:"text-2xl font-bold font-playfair text-gray-900 tracking-wide",children:"JW GOLD"})]})}),(0,r.jsx)("div",{className:"hidden lg:flex lg:gap-x-8",children:j.map(e=>(0,r.jsx)(i(),{href:e.href,className:"text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors duration-200",children:e.name},e.name))}),(0,r.jsxs)("div",{className:"flex items-center gap-x-4 lg:flex-1 lg:justify-end",children:[(0,r.jsxs)("button",{type:"button",className:"rounded-md bg-white p-2 text-gray-400 hover:text-gray-500",children:[(0,r.jsx)("span",{className:"sr-only",children:"Search"}),(0,r.jsx)(o.A,{className:"h-6 w-6","aria-hidden":"true"})]}),(0,r.jsxs)("button",{type:"button",className:"relative rounded-md bg-white p-2 text-gray-400 hover:text-gray-500",onClick:()=>n(!0),children:[(0,r.jsx)("span",{className:"sr-only",children:"Shopping cart"}),(0,r.jsx)(d.A,{className:"h-6 w-6","aria-hidden":"true"}),x>0&&(0,r.jsx)("span",{className:"absolute -top-1 -right-1 h-5 w-5 rounded-full bg-indigo-600 flex items-center justify-center text-xs font-medium text-white",children:x})]})]})]})}),(0,r.jsx)(h.e.Root,{show:e,as:s.Fragment,children:(0,r.jsxs)(u.lG,{as:"div",className:"relative z-50 lg:hidden",onClose:t,children:[(0,r.jsx)(h.e.Child,{as:s.Fragment,enter:"transition-opacity ease-linear duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"transition-opacity ease-linear duration-300",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),(0,r.jsx)("div",{className:"fixed inset-0 z-50 flex",children:(0,r.jsx)(h.e.Child,{as:s.Fragment,enter:"transition ease-in-out duration-300 transform",enterFrom:"-translate-x-full",enterTo:"translate-x-0",leave:"transition ease-in-out duration-300 transform",leaveFrom:"translate-x-0",leaveTo:"-translate-x-full",children:(0,r.jsxs)(u.lG.Panel,{className:"relative flex w-full max-w-xs flex-col overflow-y-auto bg-white pb-12 shadow-xl",children:[(0,r.jsx)("div",{className:"flex px-4 pb-2 pt-5",children:(0,r.jsxs)("button",{type:"button",className:"-m-2 inline-flex items-center justify-center rounded-md p-2 text-gray-400",onClick:()=>t(!1),children:[(0,r.jsx)("span",{className:"sr-only",children:"Close menu"}),(0,r.jsx)(m.A,{className:"h-6 w-6","aria-hidden":"true"})]})}),(0,r.jsx)("div",{className:"space-y-6 border-t border-gray-200 px-4 py-6",children:j.map(e=>(0,r.jsx)("div",{className:"flow-root",children:(0,r.jsx)(i(),{href:e.href,className:"-m-2 block p-2 font-medium text-gray-900",onClick:()=>t(!1),children:e.name})},e.name))})]})})})]})})]}),(0,r.jsx)(g,{open:a,setOpen:n})]})}},4386:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,9324,23)),Promise.resolve().then(a.bind(a,3595)),Promise.resolve().then(a.bind(a,6515)),Promise.resolve().then(a.t.bind(a,6874,23)),Promise.resolve().then(a.t.bind(a,5080,23)),Promise.resolve().then(a.t.bind(a,3547,23))},6515:(e,t,a)=>{"use strict";a.d(t,{CartProvider:()=>d,_:()=>m});var r=a(5155),s=a(2115),n=a(7120),i=a(7383);let l={items:[],checkoutId:null,checkoutUrl:null,isLoading:!1,totalPrice:{amount:"0",currencyCode:"USD"},totalQuantity:0};function c(e,t){switch(t.type){case"SET_LOADING":return{...e,isLoading:t.payload};case"SET_CHECKOUT":return{...e,checkoutId:t.payload.checkoutId,checkoutUrl:t.payload.checkoutUrl};case"ADD_ITEM":if(e.items.find(e=>e.variantId===t.payload.variantId))return{...e,items:e.items.map(e=>e.variantId===t.payload.variantId?{...e,quantity:e.quantity+t.payload.quantity}:e)};return{...e,items:[...e.items,t.payload]};case"UPDATE_ITEM":return{...e,items:e.items.map(e=>e.id===t.payload.id?{...e,quantity:t.payload.quantity}:e).filter(e=>e.quantity>0)};case"REMOVE_ITEM":return{...e,items:e.items.filter(e=>e.id!==t.payload)};case"CLEAR_CART":return{...e,items:[]};case"SET_CART":return{...e,items:t.payload};case"UPDATE_TOTALS":return{...e,totalPrice:t.payload.totalPrice,totalQuantity:t.payload.totalQuantity};default:return e}}let o=(0,s.createContext)(void 0);function d(e){let{children:t}=e,[a,d]=(0,s.useReducer)(c,l);(0,s.useEffect)(()=>{m()},[]),(0,s.useEffect)(()=>{var e;let t=a.items.reduce((e,t)=>e+t.quantity,0);d({type:"UPDATE_TOTALS",payload:{totalPrice:{amount:a.items.reduce((e,t)=>e+parseFloat(t.price.amount)*t.quantity,0).toString(),currencyCode:(null==(e=a.items[0])?void 0:e.price.currencyCode)||"USD"},totalQuantity:t}})},[a.items]),(0,s.useEffect)(()=>{a.items.length>0?i.A.set("cart",JSON.stringify(a.items),{expires:7}):i.A.remove("cart")},[a.items]),(0,s.useEffect)(()=>{let e=i.A.get("cart");if(e)try{let t=JSON.parse(e);d({type:"SET_CART",payload:t})}catch(e){console.error("Error loading cart from cookies:",e)}},[]);let m=async()=>{try{d({type:"SET_LOADING",payload:!0});let e=await n.h.createCheckout();e&&(d({type:"SET_CHECKOUT",payload:{checkoutId:e.id,checkoutUrl:e.webUrl}}),i.A.set("checkoutId",e.id,{expires:7}))}catch(e){console.error("Error initializing checkout:",e)}finally{d({type:"SET_LOADING",payload:!1})}},h=async e=>{if(d({type:"ADD_ITEM",payload:{...e,id:"".concat(e.variantId,"-").concat(Date.now())}}),a.checkoutId)try{let t=[{variantId:e.variantId,quantity:e.quantity}];await n.h.addToCheckout(a.checkoutId,t)}catch(e){console.error("Error adding to Shopify checkout:",e)}},u=async(e,t)=>{d({type:"UPDATE_ITEM",payload:{id:e,quantity:t}})},x=async e=>{d({type:"REMOVE_ITEM",payload:e})};return(0,r.jsx)(o.Provider,{value:{...a,addToCart:h,updateCartItem:u,removeFromCart:x,clearCart:()=>{d({type:"CLEAR_CART"}),i.A.remove("cart")},initializeCheckout:m},children:t})}function m(){let e=(0,s.useContext)(o);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e}},7120:(e,t,a)=>{"use strict";a.d(t,{h:()=>n});var r=a(8325);let s=a.n(r)().buildClient({domain:"your-store.myshopify.com",storefrontAccessToken:"your-storefront-access-token"}),n={async fetchProducts(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:20;try{return await s.product.fetchAll(e)}catch(e){return console.error("Error fetching products:",e),[]}},async fetchProductByHandle(e){try{return(await s.product.fetchAll()).find(t=>t.handle===e)}catch(e){return console.error("Error fetching product:",e),null}},async fetchCollections(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10;try{return await s.collection.fetchAll(e)}catch(e){return console.error("Error fetching collections:",e),[]}},async createCheckout(){try{return await s.checkout.create()}catch(e){return console.error("Error creating checkout:",e),null}},async addToCheckout(e,t){try{return await s.checkout.addLineItems(e,t)}catch(e){return console.error("Error adding to checkout:",e),null}},async updateCheckout(e,t){try{return await s.checkout.updateLineItems(e,t)}catch(e){return console.error("Error updating checkout:",e),null}},async removeFromCheckout(e,t){try{return await s.checkout.removeLineItems(e,t)}catch(e){return console.error("Error removing from checkout:",e),null}},formatPrice:e=>new Intl.NumberFormat("en-US",{style:"currency",currency:e.currencyCode}).format(parseFloat(e.amount))}},9324:()=>{}},e=>{e.O(0,[154,204,707,532,441,964,358],()=>e(e.s=4386)),_N_E=e.O()}]);