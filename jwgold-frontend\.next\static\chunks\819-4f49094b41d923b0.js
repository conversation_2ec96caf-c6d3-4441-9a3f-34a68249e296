"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[819],{2900:(t,e,i)=>{let n;i.d(e,{P:()=>rd});var s=i(2115);let r=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],a=new Set(r),o=t=>180*t/Math.PI,l=t=>u(o(Math.atan2(t[1],t[0]))),h={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:l,rotateZ:l,skewX:t=>o(Math.atan(t[1])),skewY:t=>o(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},u=t=>((t%=360)<0&&(t+=360),t),d=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),c=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),p={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:d,scaleY:c,scale:t=>(d(t)+c(t))/2,rotateX:t=>u(o(Math.atan2(t[6],t[5]))),rotateY:t=>u(o(Math.atan2(-t[2],t[0]))),rotateZ:l,rotate:l,skewX:t=>o(Math.atan(t[4])),skewY:t=>o(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function m(t){return+!!t.includes("scale")}function f(t,e){let i,n;if(!t||"none"===t)return m(e);let s=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(s)i=p,n=s;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=h,n=e}if(!n)return m(e);let r=i[e],a=n[1].split(",").map(g);return"function"==typeof r?r(a):a[r]}function g(t){return parseFloat(t.trim())}let y=t=>e=>"string"==typeof e&&e.startsWith(t),v=y("--"),x=y("var(--"),w=t=>!!x(t)&&T.test(t.split("/*")[0].trim()),T=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu;function P({top:t,left:e,right:i,bottom:n}){return{x:{min:e,max:i},y:{min:t,max:n}}}let b=(t,e,i)=>t+(e-t)*i;function S(t){return void 0===t||1===t}function A({scale:t,scaleX:e,scaleY:i}){return!S(t)||!S(e)||!S(i)}function M(t){return A(t)||E(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function E(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function V(t,e,i,n,s){return void 0!==s&&(t=n+s*(t-n)),n+i*(t-n)+e}function C(t,e=0,i=1,n,s){t.min=V(t.min,e,i,n,s),t.max=V(t.max,e,i,n,s)}function D(t,{x:e,y:i}){C(t.x,e.translate,e.scale,e.originPoint),C(t.y,i.translate,i.scale,i.originPoint)}function k(t,e){t.min=t.min+e,t.max=t.max+e}function R(t,e,i,n,s=.5){let r=b(t.min,t.max,s);C(t,e,i,r,n)}function j(t,e){R(t.x,e.x,e.scaleX,e.scale,e.originX),R(t.y,e.y,e.scaleY,e.scale,e.originY)}function L(t,e){return P(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}(t.getBoundingClientRect(),e))}let F=new Set(["width","height","top","left","right","bottom",...r]),B=(t,e,i)=>i>e?e:i<t?t:i,O={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},I={...O,transform:t=>B(0,1,t)},U={...O,default:1},N=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),$=N("deg"),W=N("%"),Y=N("px"),z=N("vh"),H=N("vw"),X={...W,parse:t=>W.parse(t)/100,transform:t=>W.transform(100*t)},K=t=>e=>e.test(t),q=[O,Y,W,$,H,z,{test:t=>"auto"===t,parse:t=>t}],_=t=>q.find(K(t)),G=()=>{},Z=()=>{},J=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),Q=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,tt=t=>t===O||t===Y,te=new Set(["x","y","z"]),ti=r.filter(t=>!te.has(t)),tn={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>f(e,"x"),y:(t,{transform:e})=>f(e,"y")};tn.translateX=tn.x,tn.translateY=tn.y;let ts=t=>t,tr={},ta=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],to={value:null,addProjectionMetrics:null};function tl(t,e){let i=!1,n=!0,s={delta:0,timestamp:0,isProcessing:!1},r=()=>i=!0,a=ta.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,n=new Set,s=!1,r=!1,a=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1},l=0;function h(e){a.has(e)&&(u.schedule(e),t()),l++,e(o)}let u={schedule:(t,e=!1,r=!1)=>{let o=r&&s?i:n;return e&&a.add(t),o.has(t)||o.add(t),t},cancel:t=>{n.delete(t),a.delete(t)},process:t=>{if(o=t,s){r=!0;return}s=!0,[i,n]=[n,i],i.forEach(h),e&&to.value&&to.value.frameloop[e].push(l),l=0,i.clear(),s=!1,r&&(r=!1,u.process(t))}};return u}(r,e?i:void 0),t),{}),{setup:o,read:l,resolveKeyframes:h,preUpdate:u,update:d,preRender:c,render:p,postRender:m}=a,f=()=>{let r=tr.useManualTiming?s.timestamp:performance.now();i=!1,tr.useManualTiming||(s.delta=n?1e3/60:Math.max(Math.min(r-s.timestamp,40),1)),s.timestamp=r,s.isProcessing=!0,o.process(s),l.process(s),h.process(s),u.process(s),d.process(s),c.process(s),p.process(s),m.process(s),s.isProcessing=!1,i&&e&&(n=!1,t(f))};return{schedule:ta.reduce((e,r)=>{let o=a[r];return e[r]=(e,r=!1,a=!1)=>(!i&&(i=!0,n=!0,s.isProcessing||t(f)),o.schedule(e,r,a)),e},{}),cancel:t=>{for(let e=0;e<ta.length;e++)a[ta[e]].cancel(t)},state:s,steps:a}}let{schedule:th,cancel:tu,state:td,steps:tc}=tl("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:ts,!0),tp=new Set,tm=!1,tf=!1,tg=!1;function ty(){if(tf){let t=Array.from(tp).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return ti.forEach(i=>{let n=t.getValue(i);void 0!==n&&(e.push([i,n.get()]),n.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}tf=!1,tm=!1,tp.forEach(t=>t.complete(tg)),tp.clear()}function tv(){tp.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(tf=!0)})}class tx{constructor(t,e,i,n,s,r=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=n,this.element=s,this.isAsync=r}scheduleResolve(){this.state="scheduled",this.isAsync?(tp.add(this),tm||(tm=!0,th.read(tv),th.resolveKeyframes(ty))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:n}=this;if(null===t[0]){let s=n?.get(),r=t[t.length-1];if(void 0!==s)t[0]=s;else if(i&&e){let n=i.readValue(e,r);null!=n&&(t[0]=n)}void 0===t[0]&&(t[0]=r),n&&void 0===s&&n.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),tp.delete(this)}cancel(){"scheduled"===this.state&&(tp.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let tw=t=>/^0[^.\s]+$/u.test(t),tT=t=>Math.round(1e5*t)/1e5,tP=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,tb=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tS=(t,e)=>i=>!!("string"==typeof i&&tb.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),tA=(t,e,i)=>n=>{if("string"!=typeof n)return n;let[s,r,a,o]=n.match(tP);return{[t]:parseFloat(s),[e]:parseFloat(r),[i]:parseFloat(a),alpha:void 0!==o?parseFloat(o):1}},tM={...O,transform:t=>Math.round(B(0,255,t))},tE={test:tS("rgb","red"),parse:tA("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:n=1})=>"rgba("+tM.transform(t)+", "+tM.transform(e)+", "+tM.transform(i)+", "+tT(I.transform(n))+")"},tV={test:tS("#"),parse:function(t){let e="",i="",n="",s="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),n=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),n=t.substring(3,4),s=t.substring(4,5),e+=e,i+=i,n+=n,s+=s),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:s?parseInt(s,16)/255:1}},transform:tE.transform},tC={test:tS("hsl","hue"),parse:tA("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:n=1})=>"hsla("+Math.round(t)+", "+W.transform(tT(e))+", "+W.transform(tT(i))+", "+tT(I.transform(n))+")"},tD={test:t=>tE.test(t)||tV.test(t)||tC.test(t),parse:t=>tE.test(t)?tE.parse(t):tC.test(t)?tC.parse(t):tV.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tE.transform(t):tC.transform(t),getAnimatableNone:t=>{let e=tD.parse(t);return e.alpha=0,tD.transform(e)}},tk=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tR="number",tj="color",tL=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tF(t){let e=t.toString(),i=[],n={color:[],number:[],var:[]},s=[],r=0,a=e.replace(tL,t=>(tD.test(t)?(n.color.push(r),s.push(tj),i.push(tD.parse(t))):t.startsWith("var(")?(n.var.push(r),s.push("var"),i.push(t)):(n.number.push(r),s.push(tR),i.push(parseFloat(t))),++r,"${}")).split("${}");return{values:i,split:a,indexes:n,types:s}}function tB(t){return tF(t).values}function tO(t){let{split:e,types:i}=tF(t),n=e.length;return t=>{let s="";for(let r=0;r<n;r++)if(s+=e[r],void 0!==t[r]){let e=i[r];e===tR?s+=tT(t[r]):e===tj?s+=tD.transform(t[r]):s+=t[r]}return s}}let tI=t=>"number"==typeof t?0:tD.test(t)?tD.getAnimatableNone(t):t,tU={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(tP)?.length||0)+(t.match(tk)?.length||0)>0},parse:tB,createTransformer:tO,getAnimatableNone:function(t){let e=tB(t);return tO(t)(e.map(tI))}},tN=new Set(["brightness","contrast","saturate","opacity"]);function t$(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[n]=i.match(tP)||[];if(!n)return t;let s=i.replace(n,""),r=+!!tN.has(e);return n!==i&&(r*=100),e+"("+r+s+")"}let tW=/\b([a-z-]*)\(.*?\)/gu,tY={...tU,getAnimatableNone:t=>{let e=t.match(tW);return e?e.map(t$).join(" "):t}},tz={...O,transform:Math.round},tH={borderWidth:Y,borderTopWidth:Y,borderRightWidth:Y,borderBottomWidth:Y,borderLeftWidth:Y,borderRadius:Y,radius:Y,borderTopLeftRadius:Y,borderTopRightRadius:Y,borderBottomRightRadius:Y,borderBottomLeftRadius:Y,width:Y,maxWidth:Y,height:Y,maxHeight:Y,top:Y,right:Y,bottom:Y,left:Y,padding:Y,paddingTop:Y,paddingRight:Y,paddingBottom:Y,paddingLeft:Y,margin:Y,marginTop:Y,marginRight:Y,marginBottom:Y,marginLeft:Y,backgroundPositionX:Y,backgroundPositionY:Y,rotate:$,rotateX:$,rotateY:$,rotateZ:$,scale:U,scaleX:U,scaleY:U,scaleZ:U,skew:$,skewX:$,skewY:$,distance:Y,translateX:Y,translateY:Y,translateZ:Y,x:Y,y:Y,z:Y,perspective:Y,transformPerspective:Y,opacity:I,originX:X,originY:X,originZ:Y,zIndex:tz,fillOpacity:I,strokeOpacity:I,numOctaves:tz},tX={...tH,color:tD,backgroundColor:tD,outlineColor:tD,fill:tD,stroke:tD,borderColor:tD,borderTopColor:tD,borderRightColor:tD,borderBottomColor:tD,borderLeftColor:tD,filter:tY,WebkitFilter:tY},tK=t=>tX[t];function tq(t,e){let i=tK(t);return i!==tY&&(i=tU),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let t_=new Set(["auto","none","0"]);class tG extends tx{constructor(t,e,i,n,s){super(t,e,i,n,s,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let n=t[i];if("string"==typeof n&&w(n=n.trim())){let s=function t(e,i,n=1){Z(n<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`,"max-css-var-depth");let[s,r]=function(t){let e=Q.exec(t);if(!e)return[,];let[,i,n,s]=e;return[`--${i??n}`,s]}(e);if(!s)return;let a=window.getComputedStyle(i).getPropertyValue(s);if(a){let t=a.trim();return J(t)?parseFloat(t):t}return w(r)?t(r,i,n+1):r}(n,e.current);void 0!==s&&(t[i]=s),i===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!F.has(i)||2!==t.length)return;let[n,s]=t,r=_(n),a=_(s);if(r!==a)if(tt(r)&&tt(a))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else tn[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var n;(null===t[e]||("number"==typeof(n=t[e])?0===n:null===n||"none"===n||"0"===n||tw(n)))&&i.push(e)}i.length&&function(t,e,i){let n,s=0;for(;s<t.length&&!n;){let e=t[s];"string"==typeof e&&!t_.has(e)&&tF(e).values.length&&(n=t[s]),s++}if(n&&i)for(let s of e)t[s]=tq(i,n)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tn[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let n=e[e.length-1];void 0!==n&&t.getValue(i,n).jump(n,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let n=t.getValue(e);n&&n.jump(this.measuredOrigin,!1);let s=i.length-1,r=i[s];i[s]=tn[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let tZ=t=>!!(t&&t.getVelocity);function tJ(){n=void 0}let tQ={now:()=>(void 0===n&&tQ.set(td.isProcessing||tr.useManualTiming?td.timestamp:performance.now()),n),set:t=>{n=t,queueMicrotask(tJ)}};function t0(t,e){-1===t.indexOf(e)&&t.push(e)}function t1(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class t2{constructor(){this.subscriptions=[]}add(t){return t0(this.subscriptions,t),()=>t1(this.subscriptions,t)}notify(t,e,i){let n=this.subscriptions.length;if(n)if(1===n)this.subscriptions[0](t,e,i);else for(let s=0;s<n;s++){let n=this.subscriptions[s];n&&n(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let t5={current:void 0};class t3{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=t=>{let e=tQ.now();if(this.updatedAt!==e&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty()},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=tQ.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=!isNaN(parseFloat(this.current)))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new t2);let i=this.events[t].add(e);return"change"===t?()=>{i(),th.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t){this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return t5.current&&t5.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=tQ.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function t4(t,e){return new t3(t,e)}let t9=[...q,tD,tU],{schedule:t7}=tl(queueMicrotask,!1),t6={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},t8={};for(let t in t6)t8[t]={isEnabled:e=>t6[t].some(t=>!!e[t])};let et=()=>({translate:0,scale:1,origin:0,originPoint:0}),ee=()=>({x:et(),y:et()}),ei=()=>({min:0,max:0}),en=()=>({x:ei(),y:ei()}),es="undefined"!=typeof window,er={current:null},ea={current:!1},eo=new WeakMap;function el(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function eh(t){return"string"==typeof t||Array.isArray(t)}let eu=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ed=["initial",...eu];function ec(t){return el(t.animate)||ed.some(e=>eh(t[e]))}function ep(t){return!!(ec(t)||t.variants)}function em(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function ef(t,e,i,n){if("function"==typeof e){let[s,r]=em(n);e=e(void 0!==i?i:t.custom,s,r)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[s,r]=em(n);e=e(void 0!==i?i:t.custom,s,r)}return e}let eg=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class ey{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:n,blockInitialAnimation:s,visualState:r},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tx,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=tQ.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,th.render(this.render,!1,!0))};let{latestValues:o,renderState:l}=r;this.latestValues=o,this.baseTarget={...o},this.initialValues=e.initial?{...o}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=n,this.options=a,this.blockInitialAnimation=!!s,this.isControllingVariants=ec(e),this.isVariantNode=ep(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:h,...u}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in u){let e=u[t];void 0!==o[t]&&tZ(e)&&e.set(o[t])}}mount(t){this.current=t,eo.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),ea.current||function(){if(ea.current=!0,es)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>er.current=t.matches;t.addEventListener("change",e),e()}else er.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||er.current),this.parent?.addChild(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),tu(this.notifyUpdate),tu(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent?.removeChild(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}addChild(t){this.children.add(t),this.enteringChildren??(this.enteringChildren=new Set),this.enteringChildren.add(t)}removeChild(t){this.children.delete(t),this.enteringChildren&&this.enteringChildren.delete(t)}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let n=a.has(t);n&&this.onBindTransform&&this.onBindTransform();let s=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&th.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{s(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in t8){let e=t8[t];if(!e)continue;let{isEnabled:i,Feature:n}=e;if(!this.features[t]&&n&&i(this.props)&&(this.features[t]=new n(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):en()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<eg.length;e++){let i=eg[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let n=t["on"+i];n&&(this.propEventSubscriptions[i]=this.on(i,n))}this.prevMotionValues=function(t,e,i){for(let n in e){let s=e[n],r=i[n];if(tZ(s))t.addValue(n,s);else if(tZ(r))t.addValue(n,t4(s,{owner:t}));else if(r!==s)if(t.hasValue(n)){let e=t.getValue(n);!0===e.liveStyle?e.jump(s):e.hasAnimated||e.set(s)}else{let e=t.getStaticValue(n);t.addValue(n,t4(void 0!==e?e:s,{owner:t}))}}for(let n in i)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=t4(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];if(null!=i){if("string"==typeof i&&(J(i)||tw(i)))i=parseFloat(i);else{let n;n=i,!t9.find(K(n))&&tU.test(e)&&(i=tq(t,e))}this.setBaseTarget(t,tZ(i)?i.get():i)}return tZ(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let n=ef(this.props,i,this.presenceContext?.custom);n&&(e=n[t])}if(i&&void 0!==e)return e;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||tZ(n)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new t2),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}scheduleRenderMicrotask(){t7.render(this.render)}}class ev extends ey{constructor(){super(...arguments),this.KeyframeResolver=tG}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;tZ(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}let ex=(t,e)=>e&&"number"==typeof t?e.transform(t):t,ew={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},eT=r.length;function eP(t,e,i){let{style:n,vars:s,transformOrigin:o}=t,l=!1,h=!1;for(let t in e){let i=e[t];if(a.has(t)){l=!0;continue}if(v(t)){s[t]=i;continue}{let e=ex(i,tH[t]);t.startsWith("origin")?(h=!0,o[t]=e):n[t]=e}}if(!e.transform&&(l||i?n.transform=function(t,e,i){let n="",s=!0;for(let a=0;a<eT;a++){let o=r[a],l=t[o];if(void 0===l)continue;let h=!0;if(!(h="number"==typeof l?l===+!!o.startsWith("scale"):0===parseFloat(l))||i){let t=ex(l,tH[o]);if(!h){s=!1;let e=ew[o]||o;n+=`${e}(${t}) `}i&&(e[o]=t)}}return n=n.trim(),i?n=i(e,s?"":n):s&&(n="none"),n}(e,t.transform,i):n.transform&&(n.transform="none")),h){let{originX:t="50%",originY:e="50%",originZ:i=0}=o;n.transformOrigin=`${t} ${e} ${i}`}}function eb(t,{style:e,vars:i},n,s){let r,a=t.style;for(r in e)a[r]=e[r];for(r in s?.applyProjectionStyles(a,n),i)a.setProperty(r,i[r])}let eS={};function eA(t,{layout:e,layoutId:i}){return a.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!eS[t]||"opacity"===t)}function eM(t,e,i){let{style:n}=t,s={};for(let r in n)(tZ(n[r])||e.style&&tZ(e.style[r])||eA(r,t)||i?.getValue(r)?.liveStyle!==void 0)&&(s[r]=n[r]);return s}class eE extends ev{constructor(){super(...arguments),this.type="html",this.renderInstance=eb}readValueFromInstance(t,e){if(a.has(e))return this.projection?.isProjecting?m(e):((t,e)=>{let{transform:i="none"}=getComputedStyle(t);return f(i,e)})(t,e);{let i=window.getComputedStyle(t),n=(v(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(t,{transformPagePoint:e}){return L(t,e)}build(t,e,i){eP(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return eM(t,e,i)}}let eV=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),eC={offset:"stroke-dashoffset",array:"stroke-dasharray"},eD={offset:"strokeDashoffset",array:"strokeDasharray"};function ek(t,{attrX:e,attrY:i,attrScale:n,pathLength:s,pathSpacing:r=1,pathOffset:a=0,...o},l,h,u){if(eP(t,o,h),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:d,style:c}=t;d.transform&&(c.transform=d.transform,delete d.transform),(c.transform||d.transformOrigin)&&(c.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),c.transform&&(c.transformBox=u?.transformBox??"fill-box",delete d.transformBox),void 0!==e&&(d.x=e),void 0!==i&&(d.y=i),void 0!==n&&(d.scale=n),void 0!==s&&function(t,e,i=1,n=0,s=!0){t.pathLength=1;let r=s?eC:eD;t[r.offset]=Y.transform(-n);let a=Y.transform(e),o=Y.transform(i);t[r.array]=`${a} ${o}`}(d,s,r,a,!1)}let eR=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]),ej=t=>"string"==typeof t&&"svg"===t.toLowerCase();function eL(t,e,i){let n=eM(t,e,i);for(let i in t)(tZ(t[i])||tZ(e[i]))&&(n[-1!==r.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return n}class eF extends ev{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=en}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(a.has(e)){let t=tK(e);return t&&t.default||0}return e=eR.has(e)?e:eV(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return eL(t,e,i)}build(t,e,i){ek(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,n){for(let i in eb(t,e,void 0,n),e.attrs)t.setAttribute(eR.has(i)?i:eV(i),e.attrs[i])}mount(t){this.isSVGTag=ej(t.tagName),super.mount(t)}}let eB=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function eO(t){if("string"!=typeof t||t.includes("-"));else if(eB.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var eI=i(5155);let eU=(0,s.createContext)({}),eN=(0,s.createContext)({strict:!1}),e$=(0,s.createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),eW=(0,s.createContext)({});function eY(t){return Array.isArray(t)?t.join(" "):t}let ez=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function eH(t,e,i){for(let n in e)tZ(e[n])||eA(n,i)||(t[n]=e[n])}let eX=()=>({...ez(),attrs:{}}),eK=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function eq(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||eK.has(t)}let e_=t=>!eq(t);try{!function(t){"function"==typeof t&&(e_=e=>e.startsWith("on")?!eq(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let eG=(0,s.createContext)(null);function eZ(t){return tZ(t)?t.get():t}let eJ=t=>(e,i)=>{let n=(0,s.useContext)(eW),r=(0,s.useContext)(eG),a=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,n,s){return{latestValues:function(t,e,i,n){let s={},r=n(t,{});for(let t in r)s[t]=eZ(r[t]);let{initial:a,animate:o}=t,l=ec(t),h=ep(t);e&&h&&!l&&!1!==t.inherit&&(void 0===a&&(a=e.initial),void 0===o&&(o=e.animate));let u=!!i&&!1===i.initial,d=(u=u||!1===a)?o:a;if(d&&"boolean"!=typeof d&&!el(d)){let e=Array.isArray(d)?d:[d];for(let i=0;i<e.length;i++){let n=ef(t,e[i]);if(n){let{transitionEnd:t,transition:e,...i}=n;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=u?e.length-1:0;e=e[t]}null!==e&&(s[t]=e)}for(let e in t)s[e]=t[e]}}}return s}(i,n,s,t),renderState:e()}})(t,e,n,r);return i?a():function(t){let e=(0,s.useRef)(null);return null===e.current&&(e.current=t()),e.current}(a)},eQ=eJ({scrapeMotionValuesFromProps:eM,createRenderState:ez}),e0=eJ({scrapeMotionValuesFromProps:eL,createRenderState:eX}),e1=Symbol.for("motionComponentSymbol");function e2(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let e5="data-"+eV("framerAppearId"),e3=(0,s.createContext)({}),e4=es?s.useLayoutEffect:s.useEffect;function e9(t){var e,i;let{forwardMotionProps:n=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2?arguments[2]:void 0,a=arguments.length>3?arguments[3]:void 0;r&&function(t){for(let e in t)t8[e]={...t8[e],...t[e]}}(r);let o=eO(t)?e0:eQ;function l(e,i){var r;let l,h={...(0,s.useContext)(e$),...e,layoutId:function(t){let{layoutId:e}=t,i=(0,s.useContext)(eU).id;return i&&void 0!==e?i+"-"+e:e}(e)},{isStatic:u}=h,d=function(t){let{initial:e,animate:i}=function(t,e){if(ec(t)){let{initial:e,animate:i}=t;return{initial:!1===e||eh(e)?e:void 0,animate:eh(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,s.useContext)(eW));return(0,s.useMemo)(()=>({initial:e,animate:i}),[eY(e),eY(i)])}(e),c=o(e,u);if(!u&&es){(0,s.useContext)(eN).strict;let e=function(t){let{drag:e,layout:i}=t8;if(!e&&!i)return{};let n={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(h);l=e.MeasureLayout,d.visualElement=function(t,e,i,n,r){let{visualElement:a}=(0,s.useContext)(eW),o=(0,s.useContext)(eN),l=(0,s.useContext)(eG),h=(0,s.useContext)(e$).reducedMotion,u=(0,s.useRef)(null);n=n||o.renderer,!u.current&&n&&(u.current=n(t,{visualState:e,parent:a,props:i,presenceContext:l,blockInitialAnimation:!!l&&!1===l.initial,reducedMotionConfig:h}));let d=u.current,c=(0,s.useContext)(e3);d&&!d.projection&&r&&("html"===d.type||"svg"===d.type)&&function(t,e,i,n){let{layoutId:s,layout:r,drag:a,dragConstraints:o,layoutScroll:l,layoutRoot:h,layoutCrossfade:u}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:s,layout:r,alwaysMeasureLayout:!!a||o&&e2(o),visualElement:t,animationType:"string"==typeof r?r:"both",initialPromotionConfig:n,crossfade:u,layoutScroll:l,layoutRoot:h})}(u.current,i,r,c);let p=(0,s.useRef)(!1);(0,s.useInsertionEffect)(()=>{d&&p.current&&d.update(i,l)});let m=i[e5],f=(0,s.useRef)(!!m&&!window.MotionHandoffIsComplete?.(m)&&window.MotionHasOptimisedAnimation?.(m));return e4(()=>{d&&(p.current=!0,window.MotionIsMounted=!0,d.updateFeatures(),d.scheduleRenderMicrotask(),f.current&&d.animationState&&d.animationState.animateChanges())}),(0,s.useEffect)(()=>{d&&(!f.current&&d.animationState&&d.animationState.animateChanges(),f.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(m)}),f.current=!1),d.enteringChildren=void 0)}),d}(t,c,h,a,e.ProjectionNode)}return(0,eI.jsxs)(eW.Provider,{value:d,children:[l&&d.visualElement?(0,eI.jsx)(l,{visualElement:d.visualElement,...h}):null,function(t,e,i,{latestValues:n},r,a=!1){let o=(eO(t)?function(t,e,i,n){let r=(0,s.useMemo)(()=>{let i=eX();return ek(i,e,ej(n),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};eH(e,t.style,t),r.style={...e,...r.style}}return r}:function(t,e){let i={},n=function(t,e){let i=t.style||{},n={};return eH(n,i,t),Object.assign(n,function({transformTemplate:t},e){return(0,s.useMemo)(()=>{let i=ez();return eP(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),n}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=n,i})(e,n,r,t),l=function(t,e,i){let n={};for(let s in t)("values"!==s||"object"!=typeof t.values)&&(e_(s)||!0===i&&eq(s)||!e&&!eq(s)||t.draggable&&s.startsWith("onDrag"))&&(n[s]=t[s]);return n}(e,"string"==typeof t,a),h=t!==s.Fragment?{...l,...o,ref:i}:{},{children:u}=e,d=(0,s.useMemo)(()=>tZ(u)?u.get():u,[u]);return(0,s.createElement)(t,{...h,children:d})}(t,e,(r=d.visualElement,(0,s.useCallback)(t=>{t&&c.onMount&&c.onMount(t),r&&(t?r.mount(t):r.unmount()),i&&("function"==typeof i?i(t):e2(i)&&(i.current=t))},[r])),c,u,n)]})}l.displayName="motion.".concat("string"==typeof t?t:"create(".concat(null!=(i=null!=(e=t.displayName)?e:t.name)?i:"",")"));let h=(0,s.forwardRef)(l);return h[e1]=t,h}function e7(t,e,i){let n=t.getProps();return ef(n,e,void 0!==i?i:n.custom,t)}function e6(t,e){return t?.[e]??t?.default??t}let e8=t=>Array.isArray(t);function it(t,e){let i=t.getValue("willChange");if(tZ(i)&&i.add)return i.add(e);if(!i&&tr.WillChange){let i=new tr.WillChange("auto");t.addValue("willChange",i),i.add(e)}}function ie(t){t.duration=0,t.type}let ii=(t,e)=>i=>e(t(i)),is=(...t)=>t.reduce(ii),ir=t=>1e3*t,ia={layout:0,mainThread:0,waapi:0};function io(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function il(t,e){return i=>i>0?e:t}let ih=(t,e,i)=>{let n=t*t,s=i*(e*e-n)+n;return s<0?0:Math.sqrt(s)},iu=[tV,tE,tC];function id(t){let e=iu.find(e=>e.test(t));if(G(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`,"color-not-animatable"),!e)return!1;let i=e.parse(t);return e===tC&&(i=function({hue:t,saturation:e,lightness:i,alpha:n}){t/=360,i/=100;let s=0,r=0,a=0;if(e/=100){let n=i<.5?i*(1+e):i+e-i*e,o=2*i-n;s=io(o,n,t+1/3),r=io(o,n,t),a=io(o,n,t-1/3)}else s=r=a=i;return{red:Math.round(255*s),green:Math.round(255*r),blue:Math.round(255*a),alpha:n}}(i)),i}let ic=(t,e)=>{let i=id(t),n=id(e);if(!i||!n)return il(t,e);let s={...i};return t=>(s.red=ih(i.red,n.red,t),s.green=ih(i.green,n.green,t),s.blue=ih(i.blue,n.blue,t),s.alpha=b(i.alpha,n.alpha,t),tE.transform(s))},ip=new Set(["none","hidden"]);function im(t,e){return i=>b(t,e,i)}function ig(t){return"number"==typeof t?im:"string"==typeof t?w(t)?il:tD.test(t)?ic:ix:Array.isArray(t)?iy:"object"==typeof t?tD.test(t)?ic:iv:il}function iy(t,e){let i=[...t],n=i.length,s=t.map((t,i)=>ig(t)(t,e[i]));return t=>{for(let e=0;e<n;e++)i[e]=s[e](t);return i}}function iv(t,e){let i={...t,...e},n={};for(let s in i)void 0!==t[s]&&void 0!==e[s]&&(n[s]=ig(t[s])(t[s],e[s]));return t=>{for(let e in n)i[e]=n[e](t);return i}}let ix=(t,e)=>{let i=tU.createTransformer(e),n=tF(t),s=tF(e);return n.indexes.var.length===s.indexes.var.length&&n.indexes.color.length===s.indexes.color.length&&n.indexes.number.length>=s.indexes.number.length?ip.has(t)&&!s.values.length||ip.has(e)&&!n.values.length?function(t,e){return ip.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):is(iy(function(t,e){let i=[],n={color:0,var:0,number:0};for(let s=0;s<e.values.length;s++){let r=e.types[s],a=t.indexes[r][n[r]],o=t.values[a]??0;i[s]=o,n[r]++}return i}(n,s),s.values),i):(G(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`,"complex-values-different"),il(t,e))};function iw(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?b(t,e,i):ig(t)(t,e)}let iT=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>th.update(e,t),stop:()=>tu(e),now:()=>td.isProcessing?td.timestamp:tQ.now()}},iP=(t,e,i=10)=>{let n="",s=Math.max(Math.round(e/i),2);for(let e=0;e<s;e++)n+=Math.round(1e4*t(e/(s-1)))/1e4+", ";return`linear(${n.substring(0,n.length-2)})`};function ib(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function iS(t,e,i){var n,s;let r=Math.max(e-5,0);return n=i-t(r),(s=e-r)?1e3/s*n:0}let iA={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function iM(t,e){return t*Math.sqrt(1-e*e)}let iE=["duration","bounce"],iV=["stiffness","damping","mass"];function iC(t,e){return e.some(e=>void 0!==t[e])}function iD(t=iA.visualDuration,e=iA.bounce){let i,n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:s,restDelta:r}=n,a=n.keyframes[0],o=n.keyframes[n.keyframes.length-1],l={done:!1,value:a},{stiffness:h,damping:u,mass:d,duration:c,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:iA.velocity,stiffness:iA.stiffness,damping:iA.damping,mass:iA.mass,isResolvedFromDuration:!1,...t};if(!iC(t,iV)&&iC(t,iE))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),n=i*i,s=2*B(.05,1,1-(t.bounce||0))*Math.sqrt(n);e={...e,mass:iA.mass,stiffness:n,damping:s}}else{let i=function({duration:t=iA.duration,bounce:e=iA.bounce,velocity:i=iA.velocity,mass:n=iA.mass}){let s,r;G(t<=ir(iA.maxDuration),"Spring duration must be 10 seconds or less","spring-duration-limit");let a=1-e;a=B(iA.minDamping,iA.maxDamping,a),t=B(iA.minDuration,iA.maxDuration,t/1e3),a<1?(s=e=>{let n=e*a,s=n*t;return .001-(n-i)/iM(e,a)*Math.exp(-s)},r=e=>{let n=e*a*t,r=Math.pow(a,2)*Math.pow(e,2)*t,o=Math.exp(-n),l=iM(Math.pow(e,2),a);return(n*i+i-r)*o*(-s(e)+.001>0?-1:1)/l}):(s=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),r=e=>t*t*(i-e)*Math.exp(-e*t));let o=function(t,e,i){let n=i;for(let i=1;i<12;i++)n-=t(n)/e(n);return n}(s,r,5/t);if(t=ir(t),isNaN(o))return{stiffness:iA.stiffness,damping:iA.damping,duration:t};{let e=Math.pow(o,2)*n;return{stiffness:e,damping:2*a*Math.sqrt(n*e),duration:t}}}(t);(e={...e,...i,mass:iA.mass}).isResolvedFromDuration=!0}return e}({...n,velocity:-((n.velocity||0)/1e3)}),f=p||0,g=u/(2*Math.sqrt(h*d)),y=o-a,v=Math.sqrt(h/d)/1e3,x=5>Math.abs(y);if(s||(s=x?iA.restSpeed.granular:iA.restSpeed.default),r||(r=x?iA.restDelta.granular:iA.restDelta.default),g<1){let t=iM(v,g);i=e=>o-Math.exp(-g*v*e)*((f+g*v*y)/t*Math.sin(t*e)+y*Math.cos(t*e))}else if(1===g)i=t=>o-Math.exp(-v*t)*(y+(f+v*y)*t);else{let t=v*Math.sqrt(g*g-1);i=e=>{let i=Math.exp(-g*v*e),n=Math.min(t*e,300);return o-i*((f+g*v*y)*Math.sinh(n)+t*y*Math.cosh(n))/t}}let w={calculatedDuration:m&&c||null,next:t=>{let e=i(t);if(m)l.done=t>=c;else{let n=0===t?f:0;g<1&&(n=0===t?ir(f):iS(i,t,e));let a=Math.abs(o-e)<=r;l.done=Math.abs(n)<=s&&a}return l.value=l.done?o:e,l},toString:()=>{let t=Math.min(ib(w),2e4),e=iP(e=>w.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return w}function ik({keyframes:t,velocity:e=0,power:i=.8,timeConstant:n=325,bounceDamping:s=10,bounceStiffness:r=500,modifyTarget:a,min:o,max:l,restDelta:h=.5,restSpeed:u}){let d,c,p=t[0],m={done:!1,value:p},f=i*e,g=p+f,y=void 0===a?g:a(g);y!==g&&(f=y-p);let v=t=>-f*Math.exp(-t/n),x=t=>y+v(t),w=t=>{let e=v(t),i=x(t);m.done=Math.abs(e)<=h,m.value=m.done?y:i},T=t=>{let e;if(e=m.value,void 0!==o&&e<o||void 0!==l&&e>l){var i;d=t,c=iD({keyframes:[m.value,(i=m.value,void 0===o?l:void 0===l||Math.abs(o-i)<Math.abs(l-i)?o:l)],velocity:iS(x,t,m.value),damping:s,stiffness:r,restDelta:h,restSpeed:u})}};return T(0),{calculatedDuration:null,next:t=>{let e=!1;return(c||void 0!==d||(e=!0,w(t),T(t)),void 0!==d&&t>=d)?c.next(t-d):(e||w(t),m)}}}iD.applyToOptions=t=>{let e=function(t,e=100,i){let n=i({...t,keyframes:[0,e]}),s=Math.min(ib(n),2e4);return{type:"keyframes",ease:t=>n.next(s*t).value/e,duration:s/1e3}}(t,100,iD);return t.ease=e.ease,t.duration=ir(e.duration),t.type="keyframes",t};let iR=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function ij(t,e,i,n){return t===e&&i===n?ts:s=>0===s||1===s?s:iR(function(t,e,i,n,s){let r,a,o=0;do(r=iR(a=e+(i-e)/2,n,s)-t)>0?i=a:e=a;while(Math.abs(r)>1e-7&&++o<12);return a}(s,0,1,t,i),e,n)}let iL=ij(.42,0,1,1),iF=ij(0,0,.58,1),iB=ij(.42,0,.58,1),iO=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,iI=t=>e=>1-t(1-e),iU=ij(.33,1.53,.69,.99),iN=iI(iU),i$=iO(iN),iW=t=>(t*=2)<1?.5*iN(t):.5*(2-Math.pow(2,-10*(t-1))),iY=t=>1-Math.sin(Math.acos(t)),iz=iI(iY),iH=iO(iY),iX=t=>Array.isArray(t)&&"number"==typeof t[0],iK={linear:ts,easeIn:iL,easeInOut:iB,easeOut:iF,circIn:iY,circInOut:iH,circOut:iz,backIn:iN,backInOut:i$,backOut:iU,anticipate:iW},iq=t=>{if(iX(t)){Z(4===t.length,"Cubic bezier arrays must contain four numerical values.","cubic-bezier-length");let[e,i,n,s]=t;return ij(e,i,n,s)}return"string"==typeof t?(Z(void 0!==iK[t],`Invalid easing type '${t}'`,"invalid-easing-type"),iK[t]):t},i_=(t,e,i)=>{let n=e-t;return 0===n?1:(i-t)/n};function iG({duration:t=300,keyframes:e,times:i,ease:n="easeInOut"}){var s;let r=Array.isArray(n)&&"number"!=typeof n[0]?n.map(iq):iq(n),a={done:!1,value:e[0]},o=function(t,e,{clamp:i=!0,ease:n,mixer:s}={}){let r=t.length;if(Z(r===e.length,"Both input and output ranges must be the same length","range-length"),1===r)return()=>e[0];if(2===r&&e[0]===e[1])return()=>e[1];let a=t[0]===t[1];t[0]>t[r-1]&&(t=[...t].reverse(),e=[...e].reverse());let o=function(t,e,i){let n=[],s=i||tr.mix||iw,r=t.length-1;for(let i=0;i<r;i++){let r=s(t[i],t[i+1]);e&&(r=is(Array.isArray(e)?e[i]||ts:e,r)),n.push(r)}return n}(e,n,s),l=o.length,h=i=>{if(a&&i<t[0])return e[0];let n=0;if(l>1)for(;n<t.length-2&&!(i<t[n+1]);n++);let s=i_(t[n],t[n+1],i);return o[n](s)};return i?e=>h(B(t[0],t[r-1],e)):h}((s=i&&i.length===e.length?i:function(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let n=1;n<=e;n++){let s=i_(0,e,n);t.push(b(i,1,s))}}(e,t.length-1),e}(e),s.map(e=>e*t)),e,{ease:Array.isArray(r)?r:e.map(()=>r||iB).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(a.value=o(e),a.done=e>=t,a)}}let iZ=t=>null!==t;function iJ(t,{repeat:e,repeatType:i="loop"},n,s=1){let r=t.filter(iZ),a=s<0||e&&"loop"!==i&&e%2==1?0:r.length-1;return a&&void 0!==n?n:r[a]}let iQ={decay:ik,inertia:ik,tween:iG,keyframes:iG,spring:iD};function i0(t){"string"==typeof t.type&&(t.type=iQ[t.type])}class i1{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let i2=t=>t/100;class i5 extends i1{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==tQ.now()&&this.tick(tQ.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},ia.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;i0(t);let{type:e=iG,repeat:i=0,repeatDelay:n=0,repeatType:s,velocity:r=0}=t,{keyframes:a}=t,o=e||iG;o!==iG&&"number"!=typeof a[0]&&(this.mixKeyframes=is(i2,iw(a[0],a[1])),a=[0,100]);let l=o({...t,keyframes:a});"mirror"===s&&(this.mirroredGenerator=o({...t,keyframes:[...a].reverse(),velocity:-r})),null===l.calculatedDuration&&(l.calculatedDuration=ib(l));let{calculatedDuration:h}=l;this.calculatedDuration=h,this.resolvedDuration=h+n,this.totalDuration=this.resolvedDuration*(i+1)-n,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:n,mixKeyframes:s,mirroredGenerator:r,resolvedDuration:a,calculatedDuration:o}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:h,repeat:u,repeatType:d,repeatDelay:c,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-n/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>n;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let v=this.currentTime,x=i;if(u){let t=Math.min(this.currentTime,n)/a,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,u+1))%2&&("reverse"===d?(i=1-i,c&&(i-=c/a)):"mirror"===d&&(x=r)),v=B(0,1,i)*a}let w=y?{done:!1,value:h[0]}:x.next(v);s&&(w.value=s(w.value));let{done:T}=w;y||null===o||(T=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);let P=null===this.holdTime&&("finished"===this.state||"running"===this.state&&T);return P&&p!==ik&&(w.value=iJ(h,this.options,f,this.speed)),m&&m(w.value),P&&this.finish(),w}then(t,e){return this.finished.then(t,e)}get duration(){return this.calculatedDuration/1e3}get time(){return this.currentTime/1e3}set time(t){t=ir(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(tQ.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=this.currentTime/1e3)}play(){if(this.isStopped)return;let{driver:t=iT,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(tQ.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,ia.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}function i3(t){let e;return()=>(void 0===e&&(e=t()),e)}let i4=i3(()=>void 0!==window.ScrollTimeline),i9={},i7=function(t,e){let i=i3(t);return()=>i9[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),i6=([t,e,i,n])=>`cubic-bezier(${t}, ${e}, ${i}, ${n})`,i8={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:i6([0,.65,.55,1]),circOut:i6([.55,0,1,.45]),backIn:i6([.31,.01,.66,-.59]),backOut:i6([.33,1.53,.69,.99])};function nt(t){return"function"==typeof t&&"applyToOptions"in t}class ne extends i1{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:n,pseudoElement:s,allowFlatten:r=!1,finalKeyframe:a,onComplete:o}=t;this.isPseudoElement=!!s,this.allowFlatten=r,this.options=t,Z("string"!=typeof t.type,'Mini animate() doesn\'t support "type" as a string.',"mini-spring");let l=function({type:t,...e}){return nt(t)&&i7()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:n=0,duration:s=300,repeat:r=0,repeatType:a="loop",ease:o="easeOut",times:l}={},h){let u={[e]:i};l&&(u.offset=l);let d=function t(e,i){if(e)return"function"==typeof e?i7()?iP(e,i):"ease-out":iX(e)?i6(e):Array.isArray(e)?e.map(e=>t(e,i)||i8.easeOut):i8[e]}(o,s);Array.isArray(d)&&(u.easing=d),to.value&&ia.waapi++;let c={delay:n,duration:s,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:r+1,direction:"reverse"===a?"alternate":"normal"};h&&(c.pseudoElement=h);let p=t.animate(u,c);return to.value&&p.finished.finally(()=>{ia.waapi--}),p}(e,i,n,l,s),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!s){let t=iJ(n,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){e.startsWith("--")?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}o?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return Number(this.animation.effect?.getComputedTiming?.().duration||0)/1e3}get time(){return(Number(this.animation.currentTime)||0)/1e3}set time(t){this.finishedTime=null,this.animation.currentTime=ir(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&i4())?(this.animation.timeline=t,ts):e(this)}}let ni={anticipate:iW,backInOut:i$,circInOut:iH};class nn extends ne{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in ni&&(t.ease=ni[t.ease])}(t),i0(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:n,element:s,...r}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let a=new i5({...r,autoplay:!1}),o=ir(this.finishedTime??this.time);e.setWithVelocity(a.sample(o-10).value,a.sample(o).value,10),a.stop()}}let ns=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tU.test(t)||"0"===t)&&!t.startsWith("url(")),nr=new Set(["opacity","clipPath","filter","transform"]),na=i3(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class no extends i1{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:n=0,repeatDelay:s=0,repeatType:r="loop",keyframes:a,name:o,motionValue:l,element:h,...u}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=tQ.now();let d={autoplay:t,delay:e,type:i,repeat:n,repeatDelay:s,repeatType:r,name:o,motionValue:l,element:h,...u},c=h?.KeyframeResolver||tx;this.keyframeResolver=new c(a,(t,e,i)=>this.onKeyframesResolved(t,e,d,!i),o,l,h),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,n){this.keyframeResolver=void 0;let{name:s,type:r,velocity:a,delay:o,isHandoff:l,onUpdate:h}=i;this.resolvedAt=tQ.now(),!function(t,e,i,n){let s=t[0];if(null===s)return!1;if("display"===e||"visibility"===e)return!0;let r=t[t.length-1],a=ns(s,e),o=ns(r,e);return G(a===o,`You are trying to animate ${e} from "${s}" to "${r}". "${a?r:s}" is not an animatable value.`,"value-not-animatable"),!!a&&!!o&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||nt(i))&&n)}(t,s,r,a)&&((tr.instantAnimations||!o)&&h?.(iJ(t,i,e)),t[0]=t[t.length-1],ie(i),i.repeat=0);let u={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},d=!l&&function(t){let{motionValue:e,name:i,repeatDelay:n,repeatType:s,damping:r,type:a}=t;if(!(e?.owner?.current instanceof HTMLElement))return!1;let{onUpdate:o,transformTemplate:l}=e.owner.getProps();return na()&&i&&nr.has(i)&&("transform"!==i||!l)&&!o&&!n&&"mirror"!==s&&0!==r&&"inertia"!==a}(u)?new nn({...u,element:u.motionValue.owner.current}):new i5(u);d.finished.then(()=>this.notifyFinished()).catch(ts),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),tg=!0,tv(),ty(),tg=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let nl=t=>null!==t,nh={type:"spring",stiffness:500,damping:25,restSpeed:10},nu={type:"keyframes",duration:.8},nd={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},nc=(t,e,i,n={},s,r)=>o=>{let l=e6(n,t)||{},h=l.delay||n.delay||0,{elapsed:u=0}=n;u-=ir(h);let d={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...l,delay:-u,onUpdate:t=>{e.set(t),l.onUpdate&&l.onUpdate(t)},onComplete:()=>{o(),l.onComplete&&l.onComplete()},name:t,motionValue:e,element:r?void 0:s};!function({when:t,delay:e,delayChildren:i,staggerChildren:n,staggerDirection:s,repeat:r,repeatType:a,repeatDelay:o,from:l,elapsed:h,...u}){return!!Object.keys(u).length}(l)&&Object.assign(d,((t,{keyframes:e})=>e.length>2?nu:a.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:nh:nd)(t,d)),d.duration&&(d.duration=ir(d.duration)),d.repeatDelay&&(d.repeatDelay=ir(d.repeatDelay)),void 0!==d.from&&(d.keyframes[0]=d.from);let c=!1;if(!1!==d.type&&(0!==d.duration||d.repeatDelay)||(ie(d),0===d.delay&&(c=!0)),(tr.instantAnimations||tr.skipAnimations)&&(c=!0,ie(d),d.delay=0),d.allowFlatten=!l.type&&!l.ease,c&&!r&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},n){let s=t.filter(nl),r=e&&"loop"!==i&&e%2==1?0:s.length-1;return s[r]}(d.keyframes,l);if(void 0!==t)return void th.update(()=>{d.onUpdate(t),d.onComplete()})}return l.isSync?new i5(d):new no(d)};function np(t,e,{delay:i=0,transitionOverride:n,type:s}={}){let{transition:r=t.getDefaultTransition(),transitionEnd:a,...o}=e;n&&(r=n);let l=[],h=s&&t.animationState&&t.animationState.getState()[s];for(let e in o){let n=t.getValue(e,t.latestValues[e]??null),s=o[e];if(void 0===s||h&&function({protectedKeys:t,needsAnimating:e},i){let n=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,n}(h,e))continue;let a={delay:i,...e6(r||{},e)},u=n.get();if(void 0!==u&&!n.isAnimating&&!Array.isArray(s)&&s===u&&!a.velocity)continue;let d=!1;if(window.MotionHandoffAnimation){let i=t.props[e5];if(i){let t=window.MotionHandoffAnimation(i,e,th);null!==t&&(a.startTime=t,d=!0)}}it(t,e),n.start(nc(e,n,s,t.shouldReduceMotion&&F.has(e)?{type:!1}:a,t,d));let c=n.animation;c&&l.push(c)}return a&&Promise.all(l).then(()=>{th.update(()=>{a&&function(t,e){let{transitionEnd:i={},transition:n={},...s}=e7(t,e)||{};for(let e in s={...s,...i}){var r;let i=e8(r=s[e])?r[r.length-1]||0:r;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,t4(i))}}(t,a)})}),l}function nm(t,e,i,n=0,s=1){let r=Array.from(t).sort((t,e)=>t.sortNodePosition(e)).indexOf(e),a=t.size,o=(a-1)*n;return"function"==typeof i?i(r,a):1===s?r*n:o-r*n}function nf(t,e,i={}){let n=e7(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:s=t.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(s=i.transitionOverride);let r=n?()=>Promise.all(np(t,n,i)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(n=0)=>{let{delayChildren:r=0,staggerChildren:a,staggerDirection:o}=s;return function(t,e,i=0,n=0,s=0,r=1,a){let o=[];for(let l of t.variantChildren)l.notify("AnimationStart",e),o.push(nf(l,e,{...a,delay:i+("function"==typeof n?0:n)+nm(t.variantChildren,l,n,s,r)}).then(()=>l.notify("AnimationComplete",e)));return Promise.all(o)}(t,e,n,r,a,o,i)}:()=>Promise.resolve(),{when:o}=s;if(!o)return Promise.all([r(),a(i.delay)]);{let[t,e]="beforeChildren"===o?[r,a]:[a,r];return t().then(()=>e())}}function ng(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let n=0;n<i;n++)if(e[n]!==t[n])return!1;return!0}let ny=ed.length,nv=[...eu].reverse(),nx=eu.length;function nw(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function nT(){return{animate:nw(!0),whileInView:nw(),whileHover:nw(),whileTap:nw(),whileDrag:nw(),whileFocus:nw(),exit:nw()}}class nP{constructor(t){this.isMounted=!1,this.node=t}update(){}}class nb extends nP{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let n;if(t.notify("AnimationStart",e),Array.isArray(e))n=Promise.all(e.map(e=>nf(t,e,i)));else if("string"==typeof e)n=nf(t,e,i);else{let s="function"==typeof e?e7(t,e,i.custom):e;n=Promise.all(np(t,s,i))}return n.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=nT(),n=!0,s=e=>(i,n)=>{let s=e7(t,n,"exit"===e?t.presenceContext?.custom:void 0);if(s){let{transition:t,transitionEnd:e,...n}=s;i={...i,...n,...e}}return i};function r(r){let{props:a}=t,o=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<ny;t++){let n=ed[t],s=e.props[n];(eh(s)||!1===s)&&(i[n]=s)}return i}(t.parent)||{},l=[],h=new Set,u={},d=1/0;for(let e=0;e<nx;e++){var c,p;let m=nv[e],f=i[m],g=void 0!==a[m]?a[m]:o[m],y=eh(g),v=m===r?f.isActive:null;!1===v&&(d=e);let x=g===o[m]&&g!==a[m]&&y;if(x&&n&&t.manuallyAnimateOnMount&&(x=!1),f.protectedKeys={...u},!f.isActive&&null===v||!g&&!f.prevProp||el(g)||"boolean"==typeof g)continue;let w=(c=f.prevProp,"string"==typeof(p=g)?p!==c:!!Array.isArray(p)&&!ng(p,c)),T=w||m===r&&f.isActive&&!x&&y||e>d&&y,P=!1,b=Array.isArray(g)?g:[g],S=b.reduce(s(m),{});!1===v&&(S={});let{prevResolvedValues:A={}}=f,M={...A,...S},E=e=>{T=!0,h.has(e)&&(P=!0,h.delete(e)),f.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in M){let e=S[t],i=A[t];if(!u.hasOwnProperty(t))(e8(e)&&e8(i)?ng(e,i):e===i)?void 0!==e&&h.has(t)?E(t):f.protectedKeys[t]=!0:null!=e?E(t):h.add(t)}f.prevProp=g,f.prevResolvedValues=S,f.isActive&&(u={...u,...S}),n&&t.blockInitialAnimation&&(T=!1);let V=x&&w,C=!V||P;T&&C&&l.push(...b.map(e=>{let i={type:m};if("string"==typeof e&&n&&!V&&t.manuallyAnimateOnMount&&t.parent){let{parent:n}=t,s=e7(n,e);if(n.enteringChildren&&s){let{delayChildren:e}=s.transition||{};i.delay=nm(n.enteringChildren,t,e)}}return{animation:e,options:i}}))}if(h.size){let e={};if("boolean"!=typeof a.initial){let i=e7(t,Array.isArray(a.initial)?a.initial[0]:a.initial);i&&i.transition&&(e.transition=i.transition)}h.forEach(i=>{let n=t.getBaseTarget(i),s=t.getValue(i);s&&(s.liveStyle=!0),e[i]=n??null}),l.push({animation:e})}let m=!!l.length;return n&&(!1===a.initial||a.initial===a.animate)&&!t.manuallyAnimateOnMount&&(m=!1),n=!1,m?e(l):Promise.resolve()}return{animateChanges:r,setActive:function(e,n){if(i[e].isActive===n)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,n)),i[e].isActive=n;let s=r(e);for(let t in i)i[t].protectedKeys={};return s},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=nT(),n=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();el(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let nS=0;class nA extends nP{constructor(){super(...arguments),this.id=nS++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let n=this.node.animationState.setActive("exit",!t);e&&!t&&n.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let nM={x:!1,y:!1};function nE(t,e,i,n={passive:!0}){return t.addEventListener(e,i,n),()=>t.removeEventListener(e,i)}let nV=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function nC(t){return{point:{x:t.pageX,y:t.pageY}}}function nD(t,e,i,n){return nE(t,e,t=>nV(t)&&i(t,nC(t)),n)}function nk(t){return t.max-t.min}function nR(t,e,i,n=.5){t.origin=n,t.originPoint=b(e.min,e.max,t.origin),t.scale=nk(i)/nk(e),t.translate=b(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function nj(t,e,i,n){nR(t.x,e.x,i.x,n?n.originX:void 0),nR(t.y,e.y,i.y,n?n.originY:void 0)}function nL(t,e,i){t.min=i.min+e.min,t.max=t.min+nk(e)}function nF(t,e,i){t.min=e.min-i.min,t.max=t.min+nk(e)}function nB(t,e,i){nF(t.x,e.x,i.x),nF(t.y,e.y,i.y)}function nO(t){return[t("x"),t("y")]}let nI=({current:t})=>t?t.ownerDocument.defaultView:null,nU=(t,e)=>Math.abs(t-e);class nN{constructor(t,e,{transformPagePoint:i,contextWindow:n=window,dragSnapToOrigin:s=!1,distanceThreshold:r=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=nY(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(nU(t.x,e.x)**2+nU(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=this.distanceThreshold;if(!e&&!i)return;let{point:n}=t,{timestamp:s}=td;this.history.push({...n,timestamp:s});let{onStart:r,onMove:a}=this.handlers;e||(r&&r(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=n$(e,this.transformPagePoint),th.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:n,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let r=nY("pointercancel"===t.type?this.lastMoveEventInfo:n$(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,r),n&&n(t,r)},!nV(t))return;this.dragSnapToOrigin=s,this.handlers=e,this.transformPagePoint=i,this.distanceThreshold=r,this.contextWindow=n||window;let a=n$(nC(t),this.transformPagePoint),{point:o}=a,{timestamp:l}=td;this.history=[{...o,timestamp:l}];let{onSessionStart:h}=e;h&&h(t,nY(a,this.history)),this.removeListeners=is(nD(this.contextWindow,"pointermove",this.handlePointerMove),nD(this.contextWindow,"pointerup",this.handlePointerUp),nD(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),tu(this.updatePoint)}}function n$(t,e){return e?{point:e(t.point)}:t}function nW(t,e){return{x:t.x-e.x,y:t.y-e.y}}function nY({point:t},e){return{point:t,delta:nW(t,nz(e)),offset:nW(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,n=null,s=nz(t);for(;i>=0&&(n=t[i],!(s.timestamp-n.timestamp>ir(.1)));)i--;if(!n)return{x:0,y:0};let r=(s.timestamp-n.timestamp)/1e3;if(0===r)return{x:0,y:0};let a={x:(s.x-n.x)/r,y:(s.y-n.y)/r};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(e,.1)}}function nz(t){return t[t.length-1]}function nH(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function nX(t,e){let i=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,n]=[n,i]),{min:i,max:n}}function nK(t,e,i){return{min:nq(t,e),max:nq(t,i)}}function nq(t,e){return"number"==typeof t?t:t[e]||0}let n_=new WeakMap;class nG{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=en(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=t}start(t,{snapToCursor:e=!1,distanceThreshold:i}={}){let{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;let s=t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(nC(t).point)},r=(t,e)=>{let{drag:i,dragPropagation:n,onDragStart:s}=this.getProps();if(i&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(nM[t])return null;else return nM[t]=!0,()=>{nM[t]=!1};return nM.x||nM.y?null:(nM.x=nM.y=!0,()=>{nM.x=nM.y=!1})}(i),!this.openDragLock))return;this.latestPointerEvent=t,this.latestPanInfo=e,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),nO(t=>{let e=this.getAxisMotionValue(t).get()||0;if(W.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let n=i.layout.layoutBox[t];n&&(e=nk(n)*(parseFloat(e)/100))}}this.originPoint[t]=e}),s&&th.postRender(()=>s(t,e)),it(this.visualElement,"transform");let{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},a=(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e;let{dragPropagation:i,dragDirectionLock:n,onDirectionLock:s,onDrag:r}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:a}=e;if(n&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(a),null!==this.currentDirection&&s&&s(this.currentDirection);return}this.updateAxis("x",e.point,a),this.updateAxis("y",e.point,a),this.visualElement.render(),r&&r(t,e)},o=(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e,this.stop(t,e),this.latestPointerEvent=null,this.latestPanInfo=null},l=()=>nO(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play()),{dragSnapToOrigin:h}=this.getProps();this.panSession=new nN(t,{onSessionStart:s,onStart:r,onMove:a,onSessionEnd:o,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:h,distanceThreshold:i,contextWindow:nI(this.visualElement)})}stop(t,e){let i=t||this.latestPointerEvent,n=e||this.latestPanInfo,s=this.isDragging;if(this.cancel(),!s||!n||!i)return;let{velocity:r}=n;this.startAnimation(r);let{onDragEnd:a}=this.getProps();a&&th.postRender(()=>a(i,n))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:n}=this.getProps();if(!i||!nZ(t,n,this.currentDirection))return;let s=this.getAxisMotionValue(t),r=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(r=function(t,{min:e,max:i},n){return void 0!==e&&t<e?t=n?b(e,t,n.min):Math.max(t,e):void 0!==i&&t>i&&(t=n?b(i,t,n.max):Math.min(t,i)),t}(r,this.constraints[t],this.elastic[t])),s.set(r)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,n=this.constraints;t&&e2(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:n,right:s}){return{x:nH(t.x,i,s),y:nH(t.y,e,n)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:nK(t,"left","right"),y:nK(t,"top","bottom")}}(e),n!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&nO(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!e2(e))return!1;let n=e.current;Z(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.","drag-constraints-ref");let{projection:s}=this.visualElement;if(!s||!s.layout)return!1;let r=function(t,e,i){let n=L(t,i),{scroll:s}=e;return s&&(k(n.x,s.offset.x),k(n.y,s.offset.y)),n}(n,s.root,this.visualElement.getTransformPagePoint()),a=(t=s.layout.layoutBox,{x:nX(t.x,r.x),y:nX(t.y,r.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(a));this.hasMutatedConstraints=!!t,t&&(a=P(t))}return a}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:n,dragTransition:s,dragSnapToOrigin:r,onDragTransitionEnd:a}=this.getProps(),o=this.constraints||{};return Promise.all(nO(a=>{if(!nZ(a,e,this.currentDirection))return;let l=o&&o[a]||{};r&&(l={min:0,max:0});let h={type:"inertia",velocity:i?t[a]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(a,h)})).then(a)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return it(this.visualElement,t),i.start(nc(t,i,0,e,this.visualElement,!1))}stopAnimation(){nO(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){nO(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){nO(e=>{let{drag:i}=this.getProps();if(!nZ(e,i,this.currentDirection))return;let{projection:n}=this.visualElement,s=this.getAxisMotionValue(e);if(n&&n.layout){let{min:i,max:r}=n.layout.layoutBox[e];s.set(t[e]-b(i,r,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!e2(e)||!i||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};nO(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();n[t]=function(t,e){let i=.5,n=nk(t),s=nk(e);return s>n?i=i_(e.min,e.max-n,t.min):n>s&&(i=i_(t.min,t.max-s,e.min)),B(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),nO(e=>{if(!nZ(e,t,null))return;let i=this.getAxisMotionValue(e),{min:s,max:r}=this.constraints[e];i.set(b(s,r,n[e]))})}addListeners(){if(!this.visualElement.current)return;n_.set(this.visualElement,this);let t=nD(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();e2(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,n=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),th.read(e);let s=nE(window,"resize",()=>this.scalePositionWithinConstraints()),r=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(nO(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{s(),t(),n(),r&&r()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:n=!1,dragConstraints:s=!1,dragElastic:r=.35,dragMomentum:a=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:n,dragConstraints:s,dragElastic:r,dragMomentum:a}}}function nZ(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class nJ extends nP{constructor(t){super(t),this.removeGroupControls=ts,this.removeListeners=ts,this.controls=new nG(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||ts}unmount(){this.removeGroupControls(),this.removeListeners()}}let nQ=t=>(e,i)=>{t&&th.postRender(()=>t(e,i))};class n0 extends nP{constructor(){super(...arguments),this.removePointerDownListener=ts}onPointerDown(t){this.session=new nN(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:nI(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:n}=this.node.getProps();return{onSessionStart:nQ(t),onStart:nQ(e),onMove:i,onEnd:(t,e)=>{delete this.session,n&&th.postRender(()=>n(t,e))}}}mount(){this.removePointerDownListener=nD(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let n1={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function n2(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let n5={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!Y.test(t))return t;else t=parseFloat(t);let i=n2(t,e.target.x),n=n2(t,e.target.y);return`${i}% ${n}%`}},n3=!1;class n4 extends s.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:n}=this.props,{projection:s}=t;for(let t in n7)eS[t]=n7[t],v(t)&&(eS[t].isCSSVariable=!0);s&&(e.group&&e.group.add(s),i&&i.register&&n&&i.register(s),n3&&s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),n1.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:n,isPresent:s}=this.props,{projection:r}=i;return r&&(r.isPresent=s,n3=!0,n||t.layoutDependency!==e||void 0===e||t.isPresent!==s?r.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?r.promote():r.relegate()||th.postRender(()=>{let t=r.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),t7.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:n}=t;n3=!0,n&&(n.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(n),i&&i.deregister&&i.deregister(n))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function n9(t){let[e,i]=function(t=!0){let e=(0,s.useContext)(eG);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:n,register:r}=e,a=(0,s.useId)();(0,s.useEffect)(()=>{if(t)return r(a)},[t]);let o=(0,s.useCallback)(()=>t&&n&&n(a),[a,n,t]);return!i&&n?[!1,o]:[!0]}(),n=(0,s.useContext)(eU);return(0,eI.jsx)(n4,{...t,layoutGroup:n,switchLayoutGroup:(0,s.useContext)(e3),isPresent:e,safeToRemove:i})}let n7={borderRadius:{...n5,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:n5,borderTopRightRadius:n5,borderBottomLeftRadius:n5,borderBottomRightRadius:n5,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let n=tU.parse(t);if(n.length>5)return t;let s=tU.createTransformer(t),r=+("number"!=typeof n[0]),a=i.x.scale*e.x,o=i.y.scale*e.y;n[0+r]/=a,n[1+r]/=o;let l=b(a,o,.5);return"number"==typeof n[2+r]&&(n[2+r]/=l),"number"==typeof n[3+r]&&(n[3+r]/=l),s(n)}}};function n6(t){return"object"==typeof t&&null!==t}function n8(t){return n6(t)&&"ownerSVGElement"in t}let st=(t,e)=>t.depth-e.depth;class se{constructor(){this.children=[],this.isDirty=!1}add(t){t0(this.children,t),this.isDirty=!0}remove(t){t1(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(st),this.isDirty=!1,this.children.forEach(t)}}let si=["TopLeft","TopRight","BottomLeft","BottomRight"],sn=si.length,ss=t=>"string"==typeof t?parseFloat(t):t,sr=t=>"number"==typeof t||Y.test(t);function sa(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let so=sh(0,.5,iz),sl=sh(.5,.95,ts);function sh(t,e,i){return n=>n<t?0:n>e?1:i(i_(t,e,n))}function su(t,e){t.min=e.min,t.max=e.max}function sd(t,e){su(t.x,e.x),su(t.y,e.y)}function sc(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function sp(t,e,i,n,s){return t-=e,t=n+1/i*(t-n),void 0!==s&&(t=n+1/s*(t-n)),t}function sm(t,e,[i,n,s],r,a){!function(t,e=0,i=1,n=.5,s,r=t,a=t){if(W.test(e)&&(e=parseFloat(e),e=b(a.min,a.max,e/100)-a.min),"number"!=typeof e)return;let o=b(r.min,r.max,n);t===r&&(o-=e),t.min=sp(t.min,e,i,o,s),t.max=sp(t.max,e,i,o,s)}(t,e[i],e[n],e[s],e.scale,r,a)}let sf=["x","scaleX","originX"],sg=["y","scaleY","originY"];function sy(t,e,i,n){sm(t.x,e,sf,i?i.x:void 0,n?n.x:void 0),sm(t.y,e,sg,i?i.y:void 0,n?n.y:void 0)}function sv(t){return 0===t.translate&&1===t.scale}function sx(t){return sv(t.x)&&sv(t.y)}function sw(t,e){return t.min===e.min&&t.max===e.max}function sT(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function sP(t,e){return sT(t.x,e.x)&&sT(t.y,e.y)}function sb(t){return nk(t.x)/nk(t.y)}function sS(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class sA{constructor(){this.members=[]}add(t){t0(this.members,t),t.scheduleRender()}remove(t){if(t1(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:n}=t.options;!1===n&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let sM={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},sE=["","X","Y","Z"],sV=0;function sC(t,e,i,n){let{latestValues:s}=e;s[t]&&(i[t]=s[t],e.setStaticValue(t,0),n&&(n[t]=0))}function sD({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:n,resetTransform:s}){return class{constructor(t={},i=e?.()){this.id=sV++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,to.value&&(sM.nodes=sM.calculatedTargetDeltas=sM.calculatedProjections=0),this.nodes.forEach(sj),this.nodes.forEach(sN),this.nodes.forEach(s$),this.nodes.forEach(sL),to.addProjectionMetrics&&to.addProjectionMetrics(sM)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new se)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new t2),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=n8(e)&&!(n8(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:i,layout:n,visualElement:s}=this.options;if(s&&!s.current&&s.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(n||i)&&(this.isLayoutDirty=!0),t){let i,n=0,s=()=>this.root.updateBlockedByResize=!1;th.read(()=>{n=window.innerWidth}),t(e,()=>{let t=window.innerWidth;t!==n&&(n=t,this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=tQ.now(),n=({timestamp:e})=>{let s=e-i;s>=250&&(tu(n),t(s-250))};return th.setup(n,!0),()=>tu(n)}(s,250),n1.hasAnimatedSinceResize&&(n1.hasAnimatedSinceResize=!1,this.nodes.forEach(sU)))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&s&&(i||n)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let r=this.options.transition||s.getDefaultTransition()||sK,{onLayoutAnimationStart:a,onLayoutAnimationComplete:o}=s.getProps(),l=!this.targetLayout||!sP(this.targetLayout,n),h=!e&&i;if(this.options.layoutRoot||this.resumeFrom||h||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...e6(r,"layout"),onPlay:a,onComplete:o};(s.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,h)}else e||sU(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),tu(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(sW),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let n=i.props[e5];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",th,!(t||i))}let{parent:s}=e;s&&!s.hasCheckedOptimisedAppear&&t(s)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(sB);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(sO);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(sI),this.nodes.forEach(sk),this.nodes.forEach(sR)):this.nodes.forEach(sO),this.clearAllSnapshots();let t=tQ.now();td.delta=B(0,1e3/60,t-td.timestamp),td.timestamp=t,td.isProcessing=!0,tc.update.process(td),tc.preRender.process(td),tc.render.process(td),td.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,t7.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(sF),this.sharedNodes.forEach(sY)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,th.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){th.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||nk(this.snapshot.measuredBox.x)||nk(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=en(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=n(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!s)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!sx(this.projectionDelta),i=this.getTransformTemplate(),n=i?i(this.latestValues,""):void 0,r=n!==this.prevTransformTemplateValue;t&&this.instance&&(e||M(this.latestValues)||r)&&(s(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),n=this.removeElementScroll(i);return t&&(n=this.removeTransform(n)),sG((e=n).x),sG(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return en();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(sJ))){let{scroll:t}=this.root;t&&(k(e.x,t.offset.x),k(e.y,t.offset.y))}return e}removeElementScroll(t){let e=en();if(sd(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let n=this.path[i],{scroll:s,options:r}=n;n!==this.root&&s&&r.layoutScroll&&(s.wasRoot&&sd(e,t),k(e.x,s.offset.x),k(e.y,s.offset.y))}return e}applyTransform(t,e=!1){let i=en();sd(i,t);for(let t=0;t<this.path.length;t++){let n=this.path[t];!e&&n.options.layoutScroll&&n.scroll&&n!==n.root&&j(i,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),M(n.latestValues)&&j(i,n.latestValues)}return M(this.latestValues)&&j(i,this.latestValues),i}removeTransform(t){let e=en();sd(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!M(i.latestValues))continue;A(i.latestValues)&&i.updateSnapshot();let n=en();sd(n,i.measurePageBox()),sy(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,n)}return M(this.latestValues)&&sy(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==td.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:n,layoutId:s}=this.options;if(this.layout&&(n||s)){if(this.resolvedRelativeTargetAt=td.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=en(),this.relativeTargetOrigin=en(),nB(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),sd(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=en(),this.targetWithTransforms=en()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var r,a,o;this.forceRelativeParentToResolveTarget(),r=this.target,a=this.relativeTarget,o=this.relativeParent.target,nL(r.x,a.x,o.x),nL(r.y,a.y,o.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):sd(this.target,this.layout.layoutBox),D(this.target,this.targetDelta)):sd(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=en(),this.relativeTargetOrigin=en(),nB(this.relativeTargetOrigin,this.target,t.target),sd(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}to.value&&sM.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||A(this.parent.latestValues)||E(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===td.timestamp&&(i=!1),i)return;let{layout:n,layoutId:s}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||s))return;sd(this.layoutCorrected,this.layout.layoutBox);let r=this.treeScale.x,a=this.treeScale.y;!function(t,e,i,n=!1){let s,r,a=i.length;if(a){e.x=e.y=1;for(let o=0;o<a;o++){r=(s=i[o]).projectionDelta;let{visualElement:a}=s.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(n&&s.options.layoutScroll&&s.scroll&&s!==s.root&&j(t,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,D(t,r)),n&&M(s.latestValues)&&j(t,s.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=en());let{target:o}=t;if(!o){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(sc(this.prevProjectionDelta.x,this.projectionDelta.x),sc(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),nj(this.projectionDelta,this.layoutCorrected,o,this.latestValues),this.treeScale.x===r&&this.treeScale.y===a&&sS(this.projectionDelta.x,this.prevProjectionDelta.x)&&sS(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",o)),to.value&&sM.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=ee(),this.projectionDelta=ee(),this.projectionDeltaWithTransform=ee()}setAnimationOrigin(t,e=!1){let i,n=this.snapshot,s=n?n.latestValues:{},r={...this.latestValues},a=ee();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let o=en(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),h=this.getStack(),u=!h||h.members.length<=1,d=!!(l&&!u&&!0===this.options.crossfade&&!this.path.some(sX));this.animationProgress=0,this.mixTargetDelta=e=>{let n=e/1e3;if(sz(a.x,t.x,n),sz(a.y,t.y,n),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var h,c,p,m,f,g;nB(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=o,g=n,sH(p.x,m.x,f.x,g),sH(p.y,m.y,f.y,g),i&&(h=this.relativeTarget,c=i,sw(h.x,c.x)&&sw(h.y,c.y))&&(this.isProjectionDirty=!1),i||(i=en()),sd(i,this.relativeTarget)}l&&(this.animationValues=r,function(t,e,i,n,s,r){s?(t.opacity=b(0,i.opacity??1,so(n)),t.opacityExit=b(e.opacity??1,0,sl(n))):r&&(t.opacity=b(e.opacity??1,i.opacity??1,n));for(let s=0;s<sn;s++){let r=`border${si[s]}Radius`,a=sa(e,r),o=sa(i,r);(void 0!==a||void 0!==o)&&(a||(a=0),o||(o=0),0===a||0===o||sr(a)===sr(o)?(t[r]=Math.max(b(ss(a),ss(o),n),0),(W.test(o)||W.test(a))&&(t[r]+="%")):t[r]=o)}(e.rotate||i.rotate)&&(t.rotate=b(e.rotate||0,i.rotate||0,n))}(r,s,this.latestValues,n,d,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(tu(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=th.update(()=>{n1.hasAnimatedSinceResize=!0,ia.layout++,this.motionValue||(this.motionValue=t4(0)),this.currentAnimation=function(t,e,i){let n=tZ(t)?t:t4(t);return n.start(nc("",n,e,i)),n.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{ia.layout--},onComplete:()=>{ia.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:n,latestValues:s}=t;if(e&&i&&n){if(this!==t&&this.layout&&n&&sZ(this.options.animationType,this.layout.layoutBox,n.layoutBox)){i=this.target||en();let e=nk(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let n=nk(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+n}sd(e,i),j(e,s),nj(this.projectionDeltaWithTransform,this.layoutCorrected,e,s)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new sA),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let n=this.getStack();n&&n.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let n={};i.z&&sC("z",t,n,this.animationValues);for(let e=0;e<sE.length;e++)sC(`rotate${sE[e]}`,t,n,this.animationValues),sC(`skew${sE[e]}`,t,n,this.animationValues);for(let e in t.render(),n)t.setStaticValue(e,n[e]),this.animationValues&&(this.animationValues[e]=n[e]);t.scheduleRender()}applyProjectionStyles(t,e){if(!this.instance||this.isSVG)return;if(!this.isVisible){t.visibility="hidden";return}let i=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,t.visibility="",t.opacity="",t.pointerEvents=eZ(e?.pointerEvents)||"",t.transform=i?i(this.latestValues,""):"none";return}let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=eZ(e?.pointerEvents)||""),this.hasProjected&&!M(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1);return}t.visibility="";let s=n.animationValues||n.latestValues;this.applyTransformsToTarget();let r=function(t,e,i){let n="",s=t.x.translate/e.x,r=t.y.translate/e.y,a=i?.z||0;if((s||r||a)&&(n=`translate3d(${s}px, ${r}px, ${a}px) `),(1!==e.x||1!==e.y)&&(n+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:s,rotateY:r,skewX:a,skewY:o}=i;t&&(n=`perspective(${t}px) ${n}`),e&&(n+=`rotate(${e}deg) `),s&&(n+=`rotateX(${s}deg) `),r&&(n+=`rotateY(${r}deg) `),a&&(n+=`skewX(${a}deg) `),o&&(n+=`skewY(${o}deg) `)}let o=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==o||1!==l)&&(n+=`scale(${o}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,s);i&&(r=i(s,r)),t.transform=r;let{x:a,y:o}=this.projectionDelta;for(let e in t.transformOrigin=`${100*a.origin}% ${100*o.origin}% 0`,n.animationValues?t.opacity=n===this?s.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:s.opacityExit:t.opacity=n===this?void 0!==s.opacity?s.opacity:"":void 0!==s.opacityExit?s.opacityExit:0,eS){if(void 0===s[e])continue;let{correct:i,applyTo:a,isCSSVariable:o}=eS[e],l="none"===r?s[e]:i(s[e],n);if(a){let e=a.length;for(let i=0;i<e;i++)t[a[i]]=l}else o?this.options.visualElement.renderState.vars[e]=l:t[e]=l}this.options.layoutId&&(t.pointerEvents=n===this?eZ(e?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(sB),this.root.sharedNodes.clear()}}}function sk(t){t.updateLayout()}function sR(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:n}=t.layout,{animationType:s}=t.options,r=e.source!==t.layout.source;"size"===s?nO(t=>{let n=r?e.measuredBox[t]:e.layoutBox[t],s=nk(n);n.min=i[t].min,n.max=n.min+s}):sZ(s,e.layoutBox,i)&&nO(n=>{let s=r?e.measuredBox[n]:e.layoutBox[n],a=nk(i[n]);s.max=s.min+a,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[n].max=t.relativeTarget[n].min+a)});let a=ee();nj(a,i,e.layoutBox);let o=ee();r?nj(o,t.applyTransform(n,!0),e.measuredBox):nj(o,i,e.layoutBox);let l=!sx(a),h=!1;if(!t.resumeFrom){let n=t.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:s,layout:r}=n;if(s&&r){let a=en();nB(a,e.layoutBox,s.layoutBox);let o=en();nB(o,i,r.layoutBox),sP(a,o)||(h=!0),n.options.layoutRoot&&(t.relativeTarget=o,t.relativeTargetOrigin=a,t.relativeParent=n)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:o,layoutDelta:a,hasLayoutChanged:l,hasRelativeLayoutChanged:h})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function sj(t){to.value&&sM.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function sL(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function sF(t){t.clearSnapshot()}function sB(t){t.clearMeasurements()}function sO(t){t.isLayoutDirty=!1}function sI(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function sU(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function sN(t){t.resolveTargetDelta()}function s$(t){t.calcProjection()}function sW(t){t.resetSkewAndRotation()}function sY(t){t.removeLeadSnapshot()}function sz(t,e,i){t.translate=b(e.translate,0,i),t.scale=b(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function sH(t,e,i,n){t.min=b(e.min,i.min,n),t.max=b(e.max,i.max,n)}function sX(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let sK={duration:.45,ease:[.4,0,.1,1]},sq=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),s_=sq("applewebkit/")&&!sq("chrome/")?Math.round:ts;function sG(t){t.min=s_(t.min),t.max=s_(t.max)}function sZ(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(sb(e)-sb(i)))}function sJ(t){return t!==t.root&&t.scroll?.wasRoot}let sQ=sD({attachResizeListener:(t,e)=>nE(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),s0={current:void 0},s1=sD({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!s0.current){let t=new sQ({});t.mount(window),t.setOptions({layoutScroll:!0}),s0.current=t}return s0.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function s2(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),n=new AbortController;return[i,{passive:!0,...e,signal:n.signal},()=>n.abort()]}function s5(t){return!("touch"===t.pointerType||nM.x||nM.y)}function s3(t,e,i){let{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover","Start"===i);let s=n["onHover"+i];s&&th.postRender(()=>s(e,nC(e)))}class s4 extends nP{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,s,r]=s2(t,i),a=t=>{if(!s5(t))return;let{target:i}=t,n=e(i,t);if("function"!=typeof n||!i)return;let r=t=>{s5(t)&&(n(t),i.removeEventListener("pointerleave",r))};i.addEventListener("pointerleave",r,s)};return n.forEach(t=>{t.addEventListener("pointerenter",a,s)}),r}(t,(t,e)=>(s3(this.node,e,"Start"),t=>s3(this.node,t,"End"))))}unmount(){}}class s9 extends nP{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=is(nE(this.node.current,"focus",()=>this.onFocus()),nE(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let s7=(t,e)=>!!e&&(t===e||s7(t,e.parentElement)),s6=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),s8=new WeakSet;function rt(t){return e=>{"Enter"===e.key&&t(e)}}function re(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function ri(t){return nV(t)&&!(nM.x||nM.y)}function rn(t,e,i){let{props:n}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&n.whileTap&&t.animationState.setActive("whileTap","Start"===i);let s=n["onTap"+("End"===i?"":i)];s&&th.postRender(()=>s(e,nC(e)))}class rs extends nP{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,s,r]=s2(t,i),a=t=>{let n=t.currentTarget;if(!ri(t))return;s8.add(n);let r=e(n,t),a=(t,e)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),s8.has(n)&&s8.delete(n),ri(t)&&"function"==typeof r&&r(t,{success:e})},o=t=>{a(t,n===window||n===document||i.useGlobalTarget||s7(n,t.target))},l=t=>{a(t,!1)};window.addEventListener("pointerup",o,s),window.addEventListener("pointercancel",l,s)};return n.forEach(t=>{(i.useGlobalTarget?window:t).addEventListener("pointerdown",a,s),n6(t)&&"offsetHeight"in t&&(t.addEventListener("focus",t=>((t,e)=>{let i=t.currentTarget;if(!i)return;let n=rt(()=>{if(s8.has(i))return;re(i,"down");let t=rt(()=>{re(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>re(i,"cancel"),e)});i.addEventListener("keydown",n,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",n),e)})(t,s)),s6.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),r}(t,(t,e)=>(rn(this.node,e,"Start"),(t,{success:e})=>rn(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let rr=new WeakMap,ra=new WeakMap,ro=t=>{let e=rr.get(t.target);e&&e(t)},rl=t=>{t.forEach(ro)},rh={some:0,all:1};class ru extends nP{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:n="some",once:s}=t,r={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof n?n:rh[n]},a=t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,s&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:n}=this.node.getProps(),r=e?i:n;r&&r(t)};var o=this.node.current;let l=function({root:t,...e}){let i=t||document;ra.has(i)||ra.set(i,{});let n=ra.get(i),s=JSON.stringify(e);return n[s]||(n[s]=new IntersectionObserver(rl,{root:t,...e})),n[s]}(r);return rr.set(o,a),l.observe(o),()=>{rr.delete(o),l.unobserve(o)}}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let rd=function(t,e){if("undefined"==typeof Proxy)return e9;let i=new Map,n=(i,n)=>e9(i,n,t,e);return new Proxy((t,e)=>n(t,e),{get:(s,r)=>"create"===r?n:(i.has(r)||i.set(r,e9(r,void 0,t,e)),i.get(r))})}({animation:{Feature:nb},exit:{Feature:nA},inView:{Feature:ru},tap:{Feature:rs},focus:{Feature:s9},hover:{Feature:s4},pan:{Feature:n0},drag:{Feature:nJ,ProjectionNode:s1,MeasureLayout:n9},layout:{ProjectionNode:s1,MeasureLayout:n9}},(t,e)=>eO(t)?new eF(e):new eE(e,{allowProjection:t!==s.Fragment}))},8984:(t,e,i)=>{i.d(e,{A:()=>s});var n=i(2115);let s=n.forwardRef(function(t,e){let{title:i,titleId:s,...r}=t;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":s},r),i?n.createElement("title",{id:s},i):null,n.createElement("path",{d:"m11.645 20.91-.007-.003-.022-.012a15.247 15.247 0 0 1-.383-.218 25.18 25.18 0 0 1-4.244-3.17C4.688 15.36 2.25 12.174 2.25 8.25 2.25 5.322 4.714 3 7.688 3A5.5 5.5 0 0 1 12 5.052 5.5 5.5 0 0 1 16.313 3c2.973 0 5.437 2.322 5.437 5.25 0 3.925-2.438 7.111-4.739 9.256a25.175 25.175 0 0 1-4.244 3.17 15.247 15.247 0 0 1-.383.219l-.022.012-.007.004-.003.001a.752.752 0 0 1-.704 0l-.003-.001Z"}))})},9949:(t,e,i)=>{i.d(e,{A:()=>s});var n=i(2115);let s=n.forwardRef(function(t,e){let{title:i,titleId:s,...r}=t;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":s},r),i?n.createElement("title",{id:s},i):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12Z"}))})}}]);