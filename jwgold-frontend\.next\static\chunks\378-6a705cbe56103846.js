"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[378],{6515:(e,t,a)=>{a.d(t,{CartProvider:()=>d,_:()=>u});var r=a(5155),i=a(2115),o=a(7120),c=a(7383);let l={items:[],checkoutId:null,checkoutUrl:null,isLoading:!1,totalPrice:{amount:"0",currencyCode:"USD"},totalQuantity:0};function n(e,t){switch(t.type){case"SET_LOADING":return{...e,isLoading:t.payload};case"SET_CHECKOUT":return{...e,checkoutId:t.payload.checkoutId,checkoutUrl:t.payload.checkoutUrl};case"ADD_ITEM":if(e.items.find(e=>e.variantId===t.payload.variantId))return{...e,items:e.items.map(e=>e.variantId===t.payload.variantId?{...e,quantity:e.quantity+t.payload.quantity}:e)};return{...e,items:[...e.items,t.payload]};case"UPDATE_ITEM":return{...e,items:e.items.map(e=>e.id===t.payload.id?{...e,quantity:t.payload.quantity}:e).filter(e=>e.quantity>0)};case"REMOVE_ITEM":return{...e,items:e.items.filter(e=>e.id!==t.payload)};case"CLEAR_CART":return{...e,items:[]};case"SET_CART":return{...e,items:t.payload};case"UPDATE_TOTALS":return{...e,totalPrice:t.payload.totalPrice,totalQuantity:t.payload.totalQuantity};default:return e}}let s=(0,i.createContext)(void 0);function d(e){let{children:t}=e,[a,d]=(0,i.useReducer)(n,l);(0,i.useEffect)(()=>{u()},[]),(0,i.useEffect)(()=>{var e;let t=a.items.reduce((e,t)=>e+t.quantity,0);d({type:"UPDATE_TOTALS",payload:{totalPrice:{amount:a.items.reduce((e,t)=>e+parseFloat(t.price.amount)*t.quantity,0).toString(),currencyCode:(null==(e=a.items[0])?void 0:e.price.currencyCode)||"USD"},totalQuantity:t}})},[a.items]),(0,i.useEffect)(()=>{a.items.length>0?c.A.set("cart",JSON.stringify(a.items),{expires:7}):c.A.remove("cart")},[a.items]),(0,i.useEffect)(()=>{let e=c.A.get("cart");if(e)try{let t=JSON.parse(e);d({type:"SET_CART",payload:t})}catch(e){console.error("Error loading cart from cookies:",e)}},[]);let u=async()=>{try{d({type:"SET_LOADING",payload:!0});let e=await o.h.createCheckout();e&&(d({type:"SET_CHECKOUT",payload:{checkoutId:e.id,checkoutUrl:e.webUrl}}),c.A.set("checkoutId",e.id,{expires:7}))}catch(e){console.error("Error initializing checkout:",e)}finally{d({type:"SET_LOADING",payload:!1})}},h=async e=>{if(d({type:"ADD_ITEM",payload:{...e,id:"".concat(e.variantId,"-").concat(Date.now())}}),a.checkoutId)try{let t=[{variantId:e.variantId,quantity:e.quantity}];await o.h.addToCheckout(a.checkoutId,t)}catch(e){console.error("Error adding to Shopify checkout:",e)}},m=async(e,t)=>{d({type:"UPDATE_ITEM",payload:{id:e,quantity:t}})},y=async e=>{d({type:"REMOVE_ITEM",payload:e})};return(0,r.jsx)(s.Provider,{value:{...a,addToCart:h,updateCartItem:m,removeFromCart:y,clearCart:()=>{d({type:"CLEAR_CART"}),c.A.remove("cart")},initializeCheckout:u},children:t})}function u(){let e=(0,i.useContext)(s);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e}},7120:(e,t,a)=>{a.d(t,{h:()=>o});var r=a(8325);let i=a.n(r)().buildClient({domain:"your-store.myshopify.com",storefrontAccessToken:"your-storefront-access-token"}),o={async fetchProducts(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:20;try{return await i.product.fetchAll(e)}catch(e){return console.error("Error fetching products:",e),[]}},async fetchProductByHandle(e){try{return(await i.product.fetchAll()).find(t=>t.handle===e)}catch(e){return console.error("Error fetching product:",e),null}},async fetchCollections(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10;try{return await i.collection.fetchAll(e)}catch(e){return console.error("Error fetching collections:",e),[]}},async createCheckout(){try{return await i.checkout.create()}catch(e){return console.error("Error creating checkout:",e),null}},async addToCheckout(e,t){try{return await i.checkout.addLineItems(e,t)}catch(e){return console.error("Error adding to checkout:",e),null}},async updateCheckout(e,t){try{return await i.checkout.updateLineItems(e,t)}catch(e){return console.error("Error updating checkout:",e),null}},async removeFromCheckout(e,t){try{return await i.checkout.removeLineItems(e,t)}catch(e){return console.error("Error removing from checkout:",e),null}},formatPrice:e=>new Intl.NumberFormat("en-US",{style:"currency",currency:e.currencyCode}).format(parseFloat(e.amount))}},7378:(e,t,a)=>{a.d(t,{A:()=>y});var r=a(5155),i=a(2115),o=a(6874),c=a.n(o),l=a(6766),n=a(2900),s=a(6515),d=a(7120),u=a(9949),h=a(527),m=a(8984);function y(e){var t,a,o,y,p,v,x,f,g,w,j,E;let{product:b}=e,[k,N]=(0,i.useState)(!1),[A,C]=(0,i.useState)(!1),{addToCart:T}=(0,s._)(),I=async e=>{if(e.preventDefault(),e.stopPropagation(),!b.variants||0===b.variants.length)return;C(!0);let t=b.variants[0];try{var a;await T({variantId:t.id,quantity:1,title:b.title,price:t.price,image:(null==(a=b.images)?void 0:a[0])?{src:b.images[0].src,altText:b.images[0].altText||b.title}:void 0,handle:b.handle})}catch(e){console.error("Error adding to cart:",e)}finally{C(!1)}},P=null==(t=b.images)?void 0:t[0],_=null==(a=b.images)?void 0:a[1],S=null==(o=b.priceRange)?void 0:o.minVariantPrice,L=null==(y=b.priceRange)?void 0:y.maxVariantPrice,D=S&&L&&S.amount!==L.amount;return(0,r.jsx)(n.P.div,{whileHover:{y:-5},transition:{duration:.3},className:"group relative bg-white rounded-lg shadow-sm hover:shadow-lg transition-shadow duration-300",children:(0,r.jsxs)(c(),{href:"/products/".concat(b.handle),children:[(0,r.jsxs)("div",{className:"relative aspect-square w-full overflow-hidden rounded-t-lg bg-gray-200",children:[P?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.default,{src:P.src,alt:P.altText||b.title,fill:!0,className:"object-cover object-center group-hover:scale-105 transition-transform duration-300",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"}),_&&(0,r.jsx)(l.default,{src:_.src,alt:_.altText||b.title,fill:!0,className:"object-cover object-center opacity-0 group-hover:opacity-100 transition-opacity duration-300",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"})]}):(0,r.jsx)("div",{className:"w-full h-full bg-gray-200 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 mx-auto mb-2 bg-gray-300 rounded-full flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-8 h-8 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"})})}),(0,r.jsx)("span",{className:"text-gray-400 text-sm",children:"No Image"})]})}),(0,r.jsxs)("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300",children:[(0,r.jsx)("div",{className:"absolute top-4 right-4 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:(0,r.jsx)("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),N(!k)},className:"p-2 bg-white rounded-full shadow-md hover:bg-gray-50 transition-colors duration-200",children:k?(0,r.jsx)(m.A,{className:"h-5 w-5 text-red-500"}):(0,r.jsx)(u.A,{className:"h-5 w-5 text-gray-600"})})}),(0,r.jsx)("div",{className:"absolute bottom-4 left-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:(0,r.jsx)("button",{onClick:I,disabled:A||!(null==(v=b.variants)||null==(p=v[0])?void 0:p.available),className:"w-full bg-white text-gray-900 py-2 px-4 rounded-md font-medium hover:bg-gray-50 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2",children:A?(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900"}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(h.A,{className:"h-4 w-4"}),(null==(f=b.variants)||null==(x=f[0])?void 0:x.available)?"Quick Add":"Sold Out"]})})})]}),(null==(w=b.variants)||null==(g=w[0])?void 0:g.compareAtPrice)&&(0,r.jsx)("div",{className:"absolute top-4 left-4",children:(0,r.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800",children:"Sale"})})]}),(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsxs)("div",{className:"mb-2",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-gray-900 line-clamp-2 group-hover:text-gold transition-colors duration-200",children:b.title}),b.vendor&&(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:b.vendor})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("div",{className:"flex flex-col",children:D?(0,r.jsxs)("span",{className:"text-sm font-medium text-gray-900",children:[d.h.formatPrice(S)," - ",d.h.formatPrice(L)]}):S?(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:d.h.formatPrice(S)}),(null==(E=b.variants)||null==(j=E[0])?void 0:j.compareAtPrice)&&(0,r.jsx)("span",{className:"text-xs text-gray-500 line-through",children:d.h.formatPrice(b.variants[0].compareAtPrice)})]}):(0,r.jsx)("span",{className:"text-sm text-gray-500",children:"Price unavailable"})}),(0,r.jsx)("div",{className:"flex items-center",children:[void 0,void 0,void 0,void 0,void 0].map((e,t)=>(0,r.jsx)("svg",{className:"h-3 w-3 text-gold",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})},t))})]})]})]})})}}}]);