{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/lib/shopify.ts"], "sourcesContent": ["import Client from 'shopify-buy';\n\n// Initialize the Shopify client\nconst client = Client.buildClient({\n  domain: process.env.NEXT_PUBLIC_SHOPIFY_STORE_DOMAIN!,\n  storefrontAccessToken: process.env.NEXT_PUBLIC_SHOPIFY_STOREFRONT_ACCESS_TOKEN!,\n});\n\nexport default client;\n\n// Types for better TypeScript support\nexport interface ShopifyProduct {\n  id: string;\n  title: string;\n  handle: string;\n  description: string;\n  images: Array<{\n    id: string;\n    src: string;\n    altText?: string;\n  }>;\n  variants: Array<{\n    id: string;\n    title: string;\n    price: {\n      amount: string;\n      currencyCode: string;\n    };\n    compareAtPrice?: {\n      amount: string;\n      currencyCode: string;\n    };\n    available: boolean;\n    selectedOptions: Array<{\n      name: string;\n      value: string;\n    }>;\n  }>;\n  options: Array<{\n    id: string;\n    name: string;\n    values: string[];\n  }>;\n  priceRange: {\n    minVariantPrice: {\n      amount: string;\n      currencyCode: string;\n    };\n    maxVariantPrice: {\n      amount: string;\n      currencyCode: string;\n    };\n  };\n  tags: string[];\n  productType: string;\n  vendor: string;\n}\n\nexport interface ShopifyCollection {\n  id: string;\n  title: string;\n  handle: string;\n  description: string;\n  image?: {\n    id: string;\n    src: string;\n    altText?: string;\n  };\n}\n\nexport interface CartItem {\n  id: string;\n  quantity: number;\n  variant: {\n    id: string;\n    title: string;\n    price: {\n      amount: string;\n      currencyCode: string;\n    };\n    product: {\n      id: string;\n      title: string;\n      handle: string;\n      images: Array<{\n        id: string;\n        src: string;\n        altText?: string;\n      }>;\n    };\n  };\n}\n\n// Helper functions for Shopify operations\nexport const shopifyHelpers = {\n  // Fetch all products\n  async fetchProducts(limit = 20) {\n    try {\n      const products = await client.product.fetchAll(limit);\n      return products;\n    } catch (error) {\n      console.error('Error fetching products:', error);\n      return [];\n    }\n  },\n\n  // Fetch a single product by handle\n  async fetchProductByHandle(handle: string) {\n    try {\n      const products = await client.product.fetchAll();\n      return products.find((product: any) => product.handle === handle);\n    } catch (error) {\n      console.error('Error fetching product:', error);\n      return null;\n    }\n  },\n\n  // Fetch collections\n  async fetchCollections(limit = 10) {\n    try {\n      const collections = await client.collection.fetchAll(limit);\n      return collections;\n    } catch (error) {\n      console.error('Error fetching collections:', error);\n      return [];\n    }\n  },\n\n  // Create checkout\n  async createCheckout() {\n    try {\n      const checkout = await client.checkout.create();\n      return checkout;\n    } catch (error) {\n      console.error('Error creating checkout:', error);\n      return null;\n    }\n  },\n\n  // Add items to checkout\n  async addToCheckout(checkoutId: string, lineItemsToAdd: any[]) {\n    try {\n      const checkout = await client.checkout.addLineItems(checkoutId, lineItemsToAdd);\n      return checkout;\n    } catch (error) {\n      console.error('Error adding to checkout:', error);\n      return null;\n    }\n  },\n\n  // Update checkout\n  async updateCheckout(checkoutId: string, lineItemsToUpdate: any[]) {\n    try {\n      const checkout = await client.checkout.updateLineItems(checkoutId, lineItemsToUpdate);\n      return checkout;\n    } catch (error) {\n      console.error('Error updating checkout:', error);\n      return null;\n    }\n  },\n\n  // Remove from checkout\n  async removeFromCheckout(checkoutId: string, lineItemIds: string[]) {\n    try {\n      const checkout = await client.checkout.removeLineItems(checkoutId, lineItemIds);\n      return checkout;\n    } catch (error) {\n      console.error('Error removing from checkout:', error);\n      return null;\n    }\n  },\n\n  // Format price\n  formatPrice(price: { amount: string; currencyCode: string }) {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: price.currencyCode,\n    }).format(parseFloat(price.amount));\n  },\n};\n"], "names": [], "mappings": ";;;;AAIU;AAJV;;AAEA,gCAAgC;AAChC,MAAM,SAAS,0IAAA,CAAA,UAAM,CAAC,WAAW,CAAC;IAChC,MAAM;IACN,qBAAqB;AACvB;uCAEe;AAsFR,MAAM,iBAAiB;IAC5B,qBAAqB;IACrB,MAAM;YAAc,QAAA,iEAAQ;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,OAAO,OAAO,CAAC,QAAQ,CAAC;YAC/C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO,EAAE;QACX;IACF;IAEA,mCAAmC;IACnC,MAAM,sBAAqB,MAAc;QACvC,IAAI;YACF,MAAM,WAAW,MAAM,OAAO,OAAO,CAAC,QAAQ;YAC9C,OAAO,SAAS,IAAI,CAAC,CAAC,UAAiB,QAAQ,MAAM,KAAK;QAC5D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO;QACT;IACF;IAEA,oBAAoB;IACpB,MAAM;YAAiB,QAAA,iEAAQ;QAC7B,IAAI;YACF,MAAM,cAAc,MAAM,OAAO,UAAU,CAAC,QAAQ,CAAC;YACrD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO,EAAE;QACX;IACF;IAEA,kBAAkB;IAClB,MAAM;QACJ,IAAI;YACF,MAAM,WAAW,MAAM,OAAO,QAAQ,CAAC,MAAM;YAC7C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;QACT;IACF;IAEA,wBAAwB;IACxB,MAAM,eAAc,UAAkB,EAAE,cAAqB;QAC3D,IAAI;YACF,MAAM,WAAW,MAAM,OAAO,QAAQ,CAAC,YAAY,CAAC,YAAY;YAChE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO;QACT;IACF;IAEA,kBAAkB;IAClB,MAAM,gBAAe,UAAkB,EAAE,iBAAwB;QAC/D,IAAI;YACF,MAAM,WAAW,MAAM,OAAO,QAAQ,CAAC,eAAe,CAAC,YAAY;YACnE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;QACT;IACF;IAEA,uBAAuB;IACvB,MAAM,oBAAmB,UAAkB,EAAE,WAAqB;QAChE,IAAI;YACF,MAAM,WAAW,MAAM,OAAO,QAAQ,CAAC,eAAe,CAAC,YAAY;YACnE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO;QACT;IACF;IAEA,eAAe;IACf,aAAY,KAA+C;QACzD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU,MAAM,YAAY;QAC9B,GAAG,MAAM,CAAC,WAAW,MAAM,MAAM;IACnC;AACF", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/context/CartContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';\nimport { shopifyHelpers } from '@/lib/shopify';\nimport Cookies from 'js-cookie';\n\ninterface CartItem {\n  id: string;\n  variantId: string;\n  quantity: number;\n  title: string;\n  price: {\n    amount: string;\n    currencyCode: string;\n  };\n  image?: {\n    src: string;\n    altText?: string;\n  };\n  handle: string;\n}\n\ninterface CartState {\n  items: CartItem[];\n  checkoutId: string | null;\n  checkoutUrl: string | null;\n  isLoading: boolean;\n  totalPrice: {\n    amount: string;\n    currencyCode: string;\n  };\n  totalQuantity: number;\n}\n\ntype CartAction =\n  | { type: 'SET_LOADING'; payload: boolean }\n  | { type: 'SET_CHECKOUT'; payload: { checkoutId: string; checkoutUrl: string } }\n  | { type: 'ADD_ITEM'; payload: CartItem }\n  | { type: 'UPDATE_ITEM'; payload: { id: string; quantity: number } }\n  | { type: 'REMOVE_ITEM'; payload: string }\n  | { type: 'CLEAR_CART' }\n  | { type: 'SET_CART'; payload: CartItem[] }\n  | { type: 'UPDATE_TOTALS'; payload: { totalPrice: { amount: string; currencyCode: string }; totalQuantity: number } };\n\nconst initialState: CartState = {\n  items: [],\n  checkoutId: null,\n  checkoutUrl: null,\n  isLoading: false,\n  totalPrice: { amount: '0', currencyCode: 'USD' },\n  totalQuantity: 0,\n};\n\nfunction cartReducer(state: CartState, action: CartAction): CartState {\n  switch (action.type) {\n    case 'SET_LOADING':\n      return { ...state, isLoading: action.payload };\n    \n    case 'SET_CHECKOUT':\n      return {\n        ...state,\n        checkoutId: action.payload.checkoutId,\n        checkoutUrl: action.payload.checkoutUrl,\n      };\n    \n    case 'ADD_ITEM': {\n      const existingItem = state.items.find(item => item.variantId === action.payload.variantId);\n      if (existingItem) {\n        return {\n          ...state,\n          items: state.items.map(item =>\n            item.variantId === action.payload.variantId\n              ? { ...item, quantity: item.quantity + action.payload.quantity }\n              : item\n          ),\n        };\n      }\n      return { ...state, items: [...state.items, action.payload] };\n    }\n    \n    case 'UPDATE_ITEM':\n      return {\n        ...state,\n        items: state.items.map(item =>\n          item.id === action.payload.id\n            ? { ...item, quantity: action.payload.quantity }\n            : item\n        ).filter(item => item.quantity > 0),\n      };\n    \n    case 'REMOVE_ITEM':\n      return {\n        ...state,\n        items: state.items.filter(item => item.id !== action.payload),\n      };\n    \n    case 'CLEAR_CART':\n      return { ...state, items: [] };\n    \n    case 'SET_CART':\n      return { ...state, items: action.payload };\n    \n    case 'UPDATE_TOTALS':\n      return {\n        ...state,\n        totalPrice: action.payload.totalPrice,\n        totalQuantity: action.payload.totalQuantity,\n      };\n    \n    default:\n      return state;\n  }\n}\n\ninterface CartContextType extends CartState {\n  addToCart: (item: Omit<CartItem, 'id'>) => Promise<void>;\n  updateCartItem: (id: string, quantity: number) => Promise<void>;\n  removeFromCart: (id: string) => Promise<void>;\n  clearCart: () => void;\n  initializeCheckout: () => Promise<void>;\n}\n\nconst CartContext = createContext<CartContextType | undefined>(undefined);\n\nexport function CartProvider({ children }: { children: ReactNode }) {\n  const [state, dispatch] = useReducer(cartReducer, initialState);\n\n  // Initialize checkout on mount\n  useEffect(() => {\n    initializeCheckout();\n  }, []);\n\n  // Update totals when items change\n  useEffect(() => {\n    const totalQuantity = state.items.reduce((sum, item) => sum + item.quantity, 0);\n    const totalAmount = state.items.reduce((sum, item) => {\n      return sum + (parseFloat(item.price.amount) * item.quantity);\n    }, 0);\n    \n    dispatch({\n      type: 'UPDATE_TOTALS',\n      payload: {\n        totalPrice: {\n          amount: totalAmount.toString(),\n          currencyCode: state.items[0]?.price.currencyCode || 'USD',\n        },\n        totalQuantity,\n      },\n    });\n  }, [state.items]);\n\n  // Save cart to cookies when items change\n  useEffect(() => {\n    if (state.items.length > 0) {\n      Cookies.set('cart', JSON.stringify(state.items), { expires: 7 });\n    } else {\n      Cookies.remove('cart');\n    }\n  }, [state.items]);\n\n  // Load cart from cookies on mount\n  useEffect(() => {\n    const savedCart = Cookies.get('cart');\n    if (savedCart) {\n      try {\n        const cartItems = JSON.parse(savedCart);\n        dispatch({ type: 'SET_CART', payload: cartItems });\n      } catch (error) {\n        console.error('Error loading cart from cookies:', error);\n      }\n    }\n  }, []);\n\n  const initializeCheckout = async () => {\n    try {\n      dispatch({ type: 'SET_LOADING', payload: true });\n      const checkout = await shopifyHelpers.createCheckout();\n      if (checkout) {\n        dispatch({\n          type: 'SET_CHECKOUT',\n          payload: {\n            checkoutId: checkout.id,\n            checkoutUrl: checkout.webUrl,\n          },\n        });\n        Cookies.set('checkoutId', checkout.id, { expires: 7 });\n      }\n    } catch (error) {\n      console.error('Error initializing checkout:', error);\n    } finally {\n      dispatch({ type: 'SET_LOADING', payload: false });\n    }\n  };\n\n  const addToCart = async (item: Omit<CartItem, 'id'>) => {\n    const cartItem: CartItem = {\n      ...item,\n      id: `${item.variantId}-${Date.now()}`,\n    };\n    \n    dispatch({ type: 'ADD_ITEM', payload: cartItem });\n    \n    // Update Shopify checkout\n    if (state.checkoutId) {\n      try {\n        const lineItemsToAdd = [{\n          variantId: item.variantId,\n          quantity: item.quantity,\n        }];\n        await shopifyHelpers.addToCheckout(state.checkoutId, lineItemsToAdd);\n      } catch (error) {\n        console.error('Error adding to Shopify checkout:', error);\n      }\n    }\n  };\n\n  const updateCartItem = async (id: string, quantity: number) => {\n    dispatch({ type: 'UPDATE_ITEM', payload: { id, quantity } });\n  };\n\n  const removeFromCart = async (id: string) => {\n    dispatch({ type: 'REMOVE_ITEM', payload: id });\n  };\n\n  const clearCart = () => {\n    dispatch({ type: 'CLEAR_CART' });\n    Cookies.remove('cart');\n  };\n\n  return (\n    <CartContext.Provider\n      value={{\n        ...state,\n        addToCart,\n        updateCartItem,\n        removeFromCart,\n        clearCart,\n        initializeCheckout,\n      }}\n    >\n      {children}\n    </CartContext.Provider>\n  );\n}\n\nexport function useCart() {\n  const context = useContext(CartContext);\n  if (context === undefined) {\n    throw new Error('useCart must be used within a CartProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;AA4CA,MAAM,eAA0B;IAC9B,OAAO,EAAE;IACT,YAAY;IACZ,aAAa;IACb,WAAW;IACX,YAAY;QAAE,QAAQ;QAAK,cAAc;IAAM;IAC/C,eAAe;AACjB;AAEA,SAAS,YAAY,KAAgB,EAAE,MAAkB;IACvD,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,WAAW,OAAO,OAAO;YAAC;QAE/C,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,YAAY,OAAO,OAAO,CAAC,UAAU;gBACrC,aAAa,OAAO,OAAO,CAAC,WAAW;YACzC;QAEF,KAAK;YAAY;gBACf,MAAM,eAAe,MAAM,KAAK,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,SAAS,KAAK,OAAO,OAAO,CAAC,SAAS;gBACzF,IAAI,cAAc;oBAChB,OAAO;wBACL,GAAG,KAAK;wBACR,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,CAAA,OACrB,KAAK,SAAS,KAAK,OAAO,OAAO,CAAC,SAAS,GACvC;gCAAE,GAAG,IAAI;gCAAE,UAAU,KAAK,QAAQ,GAAG,OAAO,OAAO,CAAC,QAAQ;4BAAC,IAC7D;oBAER;gBACF;gBACA,OAAO;oBAAE,GAAG,KAAK;oBAAE,OAAO;2BAAI,MAAM,KAAK;wBAAE,OAAO,OAAO;qBAAC;gBAAC;YAC7D;QAEA,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,CAAA,OACrB,KAAK,EAAE,KAAK,OAAO,OAAO,CAAC,EAAE,GACzB;wBAAE,GAAG,IAAI;wBAAE,UAAU,OAAO,OAAO,CAAC,QAAQ;oBAAC,IAC7C,MACJ,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,GAAG;YACnC;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,OAAO,MAAM,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,OAAO,OAAO;YAC9D;QAEF,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,OAAO,EAAE;YAAC;QAE/B,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,OAAO,OAAO,OAAO;YAAC;QAE3C,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,YAAY,OAAO,OAAO,CAAC,UAAU;gBACrC,eAAe,OAAO,OAAO,CAAC,aAAa;YAC7C;QAEF;YACE,OAAO;IACX;AACF;AAUA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,KAAqC;QAArC,EAAE,QAAQ,EAA2B,GAArC;;IAC3B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,aAAa;IAElD,+BAA+B;IAC/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG,EAAE;IAEL,kCAAkC;IAClC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;gBAWY;YAVpB,MAAM,gBAAgB,MAAM,KAAK,CAAC,MAAM;wDAAC,CAAC,KAAK,OAAS,MAAM,KAAK,QAAQ;uDAAE;YAC7E,MAAM,cAAc,MAAM,KAAK,CAAC,MAAM;sDAAC,CAAC,KAAK;oBAC3C,OAAO,MAAO,WAAW,KAAK,KAAK,CAAC,MAAM,IAAI,KAAK,QAAQ;gBAC7D;qDAAG;YAEH,SAAS;gBACP,MAAM;gBACN,SAAS;oBACP,YAAY;wBACV,QAAQ,YAAY,QAAQ;wBAC5B,cAAc,EAAA,gBAAA,MAAM,KAAK,CAAC,EAAE,cAAd,oCAAA,cAAgB,KAAK,CAAC,YAAY,KAAI;oBACtD;oBACA;gBACF;YACF;QACF;iCAAG;QAAC,MAAM,KAAK;KAAC;IAEhB,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,MAAM,KAAK,CAAC,MAAM,GAAG,GAAG;gBAC1B,wJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,SAAS,CAAC,MAAM,KAAK,GAAG;oBAAE,SAAS;gBAAE;YAChE,OAAO;gBACL,wJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;YACjB;QACF;iCAAG;QAAC,MAAM,KAAK;KAAC;IAEhB,kCAAkC;IAClC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,YAAY,wJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;YAC9B,IAAI,WAAW;gBACb,IAAI;oBACF,MAAM,YAAY,KAAK,KAAK,CAAC;oBAC7B,SAAS;wBAAE,MAAM;wBAAY,SAAS;oBAAU;gBAClD,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,oCAAoC;gBACpD;YACF;QACF;iCAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,IAAI;YACF,SAAS;gBAAE,MAAM;gBAAe,SAAS;YAAK;YAC9C,MAAM,WAAW,MAAM,iHAAA,CAAA,iBAAc,CAAC,cAAc;YACpD,IAAI,UAAU;gBACZ,SAAS;oBACP,MAAM;oBACN,SAAS;wBACP,YAAY,SAAS,EAAE;wBACvB,aAAa,SAAS,MAAM;oBAC9B;gBACF;gBACA,wJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,cAAc,SAAS,EAAE,EAAE;oBAAE,SAAS;gBAAE;YACtD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,SAAS;gBAAE,MAAM;gBAAe,SAAS;YAAM;QACjD;IACF;IAEA,MAAM,YAAY,OAAO;QACvB,MAAM,WAAqB;YACzB,GAAG,IAAI;YACP,IAAI,AAAC,GAAoB,OAAlB,KAAK,SAAS,EAAC,KAAc,OAAX,KAAK,GAAG;QACnC;QAEA,SAAS;YAAE,MAAM;YAAY,SAAS;QAAS;QAE/C,0BAA0B;QAC1B,IAAI,MAAM,UAAU,EAAE;YACpB,IAAI;gBACF,MAAM,iBAAiB;oBAAC;wBACtB,WAAW,KAAK,SAAS;wBACzB,UAAU,KAAK,QAAQ;oBACzB;iBAAE;gBACF,MAAM,iHAAA,CAAA,iBAAc,CAAC,aAAa,CAAC,MAAM,UAAU,EAAE;YACvD,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qCAAqC;YACrD;QACF;IACF;IAEA,MAAM,iBAAiB,OAAO,IAAY;QACxC,SAAS;YAAE,MAAM;YAAe,SAAS;gBAAE;gBAAI;YAAS;QAAE;IAC5D;IAEA,MAAM,iBAAiB,OAAO;QAC5B,SAAS;YAAE,MAAM;YAAe,SAAS;QAAG;IAC9C;IAEA,MAAM,YAAY;QAChB,SAAS;YAAE,MAAM;QAAa;QAC9B,wJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;IACjB;IAEA,qBACE,6LAAC,YAAY,QAAQ;QACnB,OAAO;YACL,GAAG,KAAK;YACR;YACA;YACA;YACA;YACA;QACF;kBAEC;;;;;;AAGP;GAvHgB;KAAA;AAyHT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 380, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/components/CartSidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { Fragment } from 'react';\nimport { Dialog, Transition } from '@headlessui/react';\nimport { XMarkIcon, MinusIcon, PlusIcon } from '@heroicons/react/24/outline';\nimport { useCart } from '@/context/CartContext';\nimport { shopifyHelpers } from '@/lib/shopify';\nimport Image from 'next/image';\nimport Link from 'next/link';\n\ninterface CartSidebarProps {\n  open: boolean;\n  setOpen: (open: boolean) => void;\n}\n\nexport default function CartSidebar({ open, setOpen }: CartSidebarProps) {\n  const { items, totalPrice, totalQuantity, updateCartItem, removeFromCart, checkoutUrl } = useCart();\n\n  const handleQuantityChange = (id: string, newQuantity: number) => {\n    if (newQuantity <= 0) {\n      removeFromCart(id);\n    } else {\n      updateCartItem(id, newQuantity);\n    }\n  };\n\n  return (\n    <Transition.Root show={open} as={Fragment}>\n      <Dialog as=\"div\" className=\"relative z-50\" onClose={setOpen}>\n        <Transition.Child\n          as={Fragment}\n          enter=\"ease-in-out duration-500\"\n          enterFrom=\"opacity-0\"\n          enterTo=\"opacity-100\"\n          leave=\"ease-in-out duration-500\"\n          leaveFrom=\"opacity-100\"\n          leaveTo=\"opacity-0\"\n        >\n          <div className=\"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\" />\n        </Transition.Child>\n\n        <div className=\"fixed inset-0 overflow-hidden\">\n          <div className=\"absolute inset-0 overflow-hidden\">\n            <div className=\"pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10\">\n              <Transition.Child\n                as={Fragment}\n                enter=\"transform transition ease-in-out duration-500 sm:duration-700\"\n                enterFrom=\"translate-x-full\"\n                enterTo=\"translate-x-0\"\n                leave=\"transform transition ease-in-out duration-500 sm:duration-700\"\n                leaveFrom=\"translate-x-0\"\n                leaveTo=\"translate-x-full\"\n              >\n                <Dialog.Panel className=\"pointer-events-auto w-screen max-w-md\">\n                  <div className=\"flex h-full flex-col overflow-y-scroll bg-white shadow-xl\">\n                    <div className=\"flex-1 overflow-y-auto px-4 py-6 sm:px-6\">\n                      <div className=\"flex items-start justify-between\">\n                        <Dialog.Title className=\"text-lg font-medium text-gray-900\">\n                          Shopping cart\n                        </Dialog.Title>\n                        <div className=\"ml-3 flex h-7 items-center\">\n                          <button\n                            type=\"button\"\n                            className=\"-m-2 p-2 text-gray-400 hover:text-gray-500\"\n                            onClick={() => setOpen(false)}\n                          >\n                            <span className=\"sr-only\">Close panel</span>\n                            <XMarkIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                          </button>\n                        </div>\n                      </div>\n\n                      <div className=\"mt-8\">\n                        <div className=\"flow-root\">\n                          {items.length === 0 ? (\n                            <div className=\"text-center py-12\">\n                              <p className=\"text-gray-500 mb-4\">Your cart is empty</p>\n                              <Link\n                                href=\"/collections\"\n                                className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700\"\n                                onClick={() => setOpen(false)}\n                              >\n                                Continue Shopping\n                              </Link>\n                            </div>\n                          ) : (\n                            <ul role=\"list\" className=\"-my-6 divide-y divide-gray-200\">\n                              {items.map((item) => (\n                                <li key={item.id} className=\"flex py-6\">\n                                  <div className=\"h-24 w-24 flex-shrink-0 overflow-hidden rounded-md border border-gray-200\">\n                                    {item.image ? (\n                                      <Image\n                                        src={item.image.src}\n                                        alt={item.image.altText || item.title}\n                                        width={96}\n                                        height={96}\n                                        className=\"h-full w-full object-cover object-center\"\n                                      />\n                                    ) : (\n                                      <div className=\"h-full w-full bg-gray-200 flex items-center justify-center\">\n                                        <span className=\"text-gray-400 text-xs\">No image</span>\n                                      </div>\n                                    )}\n                                  </div>\n\n                                  <div className=\"ml-4 flex flex-1 flex-col\">\n                                    <div>\n                                      <div className=\"flex justify-between text-base font-medium text-gray-900\">\n                                        <h3>\n                                          <Link href={`/products/${item.handle}`} onClick={() => setOpen(false)}>\n                                            {item.title}\n                                          </Link>\n                                        </h3>\n                                        <p className=\"ml-4\">\n                                          {shopifyHelpers.formatPrice(item.price)}\n                                        </p>\n                                      </div>\n                                    </div>\n                                    <div className=\"flex flex-1 items-end justify-between text-sm\">\n                                      <div className=\"flex items-center space-x-2\">\n                                        <button\n                                          type=\"button\"\n                                          className=\"p-1 text-gray-400 hover:text-gray-500\"\n                                          onClick={() => handleQuantityChange(item.id, item.quantity - 1)}\n                                        >\n                                          <MinusIcon className=\"h-4 w-4\" />\n                                        </button>\n                                        <span className=\"text-gray-500 min-w-[2rem] text-center\">\n                                          Qty {item.quantity}\n                                        </span>\n                                        <button\n                                          type=\"button\"\n                                          className=\"p-1 text-gray-400 hover:text-gray-500\"\n                                          onClick={() => handleQuantityChange(item.id, item.quantity + 1)}\n                                        >\n                                          <PlusIcon className=\"h-4 w-4\" />\n                                        </button>\n                                      </div>\n\n                                      <div className=\"flex\">\n                                        <button\n                                          type=\"button\"\n                                          className=\"font-medium text-indigo-600 hover:text-indigo-500\"\n                                          onClick={() => removeFromCart(item.id)}\n                                        >\n                                          Remove\n                                        </button>\n                                      </div>\n                                    </div>\n                                  </div>\n                                </li>\n                              ))}\n                            </ul>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n\n                    {items.length > 0 && (\n                      <div className=\"border-t border-gray-200 px-4 py-6 sm:px-6\">\n                        <div className=\"flex justify-between text-base font-medium text-gray-900\">\n                          <p>Subtotal</p>\n                          <p>{shopifyHelpers.formatPrice(totalPrice)}</p>\n                        </div>\n                        <p className=\"mt-0.5 text-sm text-gray-500\">\n                          Shipping and taxes calculated at checkout.\n                        </p>\n                        <div className=\"mt-6\">\n                          {checkoutUrl ? (\n                            <a\n                              href={checkoutUrl}\n                              className=\"flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-6 py-3 text-base font-medium text-white shadow-sm hover:bg-indigo-700 transition-colors duration-200\"\n                            >\n                              Checkout\n                            </a>\n                          ) : (\n                            <button\n                              disabled\n                              className=\"flex items-center justify-center rounded-md border border-transparent bg-gray-300 px-6 py-3 text-base font-medium text-gray-500 cursor-not-allowed\"\n                            >\n                              Loading...\n                            </button>\n                          )}\n                        </div>\n                        <div className=\"mt-6 flex justify-center text-center text-sm text-gray-500\">\n                          <p>\n                            or{' '}\n                            <button\n                              type=\"button\"\n                              className=\"font-medium text-indigo-600 hover:text-indigo-500\"\n                              onClick={() => setOpen(false)}\n                            >\n                              Continue Shopping\n                              <span aria-hidden=\"true\"> &rarr;</span>\n                            </button>\n                          </p>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                </Dialog.Panel>\n              </Transition.Child>\n            </div>\n          </div>\n        </div>\n      </Dialog>\n    </Transition.Root>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAee,SAAS,YAAY,KAAmC;QAAnC,EAAE,IAAI,EAAE,OAAO,EAAoB,GAAnC;;IAClC,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAEhG,MAAM,uBAAuB,CAAC,IAAY;QACxC,IAAI,eAAe,GAAG;YACpB,eAAe;QACjB,OAAO;YACL,eAAe,IAAI;QACrB;IACF;IAEA,qBACE,6LAAC,0LAAA,CAAA,aAAU,CAAC,IAAI;QAAC,MAAM;QAAM,IAAI,6JAAA,CAAA,WAAQ;kBACvC,cAAA,6LAAC,kLAAA,CAAA,SAAM;YAAC,IAAG;YAAM,WAAU;YAAgB,SAAS;;8BAClD,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;oBACf,IAAI,6JAAA,CAAA,WAAQ;oBACZ,OAAM;oBACN,WAAU;oBACV,SAAQ;oBACR,OAAM;oBACN,WAAU;oBACV,SAAQ;8BAER,cAAA,6LAAC;wBAAI,WAAU;;;;;;;;;;;8BAGjB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;gCACf,IAAI,6JAAA,CAAA,WAAQ;gCACZ,OAAM;gCACN,WAAU;gCACV,SAAQ;gCACR,OAAM;gCACN,WAAU;gCACV,SAAQ;0CAER,cAAA,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;oCAAC,WAAU;8CACtB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;gEAAC,WAAU;0EAAoC;;;;;;0EAG5D,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEACC,MAAK;oEACL,WAAU;oEACV,SAAS,IAAM,QAAQ;;sFAEvB,6LAAC;4EAAK,WAAU;sFAAU;;;;;;sFAC1B,6LAAC,oNAAA,CAAA,YAAS;4EAAC,WAAU;4EAAU,eAAY;;;;;;;;;;;;;;;;;;;;;;;kEAKjD,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;sEACZ,MAAM,MAAM,KAAK,kBAChB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAE,WAAU;kFAAqB;;;;;;kFAClC,6LAAC,+JAAA,CAAA,UAAI;wEACH,MAAK;wEACL,WAAU;wEACV,SAAS,IAAM,QAAQ;kFACxB;;;;;;;;;;;qFAKH,6LAAC;gEAAG,MAAK;gEAAO,WAAU;0EACvB,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;wEAAiB,WAAU;;0FAC1B,6LAAC;gFAAI,WAAU;0FACZ,KAAK,KAAK,iBACT,6LAAC,gIAAA,CAAA,UAAK;oFACJ,KAAK,KAAK,KAAK,CAAC,GAAG;oFACnB,KAAK,KAAK,KAAK,CAAC,OAAO,IAAI,KAAK,KAAK;oFACrC,OAAO;oFACP,QAAQ;oFACR,WAAU;;;;;yGAGZ,6LAAC;oFAAI,WAAU;8FACb,cAAA,6LAAC;wFAAK,WAAU;kGAAwB;;;;;;;;;;;;;;;;0FAK9C,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;kGACC,cAAA,6LAAC;4FAAI,WAAU;;8GACb,6LAAC;8GACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wGAAC,MAAM,AAAC,aAAwB,OAAZ,KAAK,MAAM;wGAAI,SAAS,IAAM,QAAQ;kHAC5D,KAAK,KAAK;;;;;;;;;;;8GAGf,6LAAC;oGAAE,WAAU;8GACV,iHAAA,CAAA,iBAAc,CAAC,WAAW,CAAC,KAAK,KAAK;;;;;;;;;;;;;;;;;kGAI5C,6LAAC;wFAAI,WAAU;;0GACb,6LAAC;gGAAI,WAAU;;kHACb,6LAAC;wGACC,MAAK;wGACL,WAAU;wGACV,SAAS,IAAM,qBAAqB,KAAK,EAAE,EAAE,KAAK,QAAQ,GAAG;kHAE7D,cAAA,6LAAC,oNAAA,CAAA,YAAS;4GAAC,WAAU;;;;;;;;;;;kHAEvB,6LAAC;wGAAK,WAAU;;4GAAyC;4GAClD,KAAK,QAAQ;;;;;;;kHAEpB,6LAAC;wGACC,MAAK;wGACL,WAAU;wGACV,SAAS,IAAM,qBAAqB,KAAK,EAAE,EAAE,KAAK,QAAQ,GAAG;kHAE7D,cAAA,6LAAC,kNAAA,CAAA,WAAQ;4GAAC,WAAU;;;;;;;;;;;;;;;;;0GAIxB,6LAAC;gGAAI,WAAU;0GACb,cAAA,6LAAC;oGACC,MAAK;oGACL,WAAU;oGACV,SAAS,IAAM,eAAe,KAAK,EAAE;8GACtC;;;;;;;;;;;;;;;;;;;;;;;;uEAxDA,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;4CAsE3B,MAAM,MAAM,GAAG,mBACd,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAE;;;;;;0EACH,6LAAC;0EAAG,iHAAA,CAAA,iBAAc,CAAC,WAAW,CAAC;;;;;;;;;;;;kEAEjC,6LAAC;wDAAE,WAAU;kEAA+B;;;;;;kEAG5C,6LAAC;wDAAI,WAAU;kEACZ,4BACC,6LAAC;4DACC,MAAM;4DACN,WAAU;sEACX;;;;;iFAID,6LAAC;4DACC,QAAQ;4DACR,WAAU;sEACX;;;;;;;;;;;kEAKL,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;;gEAAE;gEACE;8EACH,6LAAC;oEACC,MAAK;oEACL,WAAU;oEACV,SAAS,IAAM,QAAQ;;wEACxB;sFAEC,6LAAC;4EAAK,eAAY;sFAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAevD;GAjMwB;;QACoE,0HAAA,CAAA,UAAO;;;KAD3E", "debugId": null}}, {"offset": {"line": 897, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { useCart } from '@/context/CartContext';\nimport { ShoppingBagIcon, Bars3Icon, XMarkIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';\nimport { Dialog, Transition } from '@headlessui/react';\nimport { Fragment } from 'react';\nimport CartSidebar from './CartSidebar';\n\nconst navigation = [\n  { name: 'Home', href: '/' },\n  { name: 'Collections', href: '/collections' },\n  { name: 'Rings', href: '/collections/rings' },\n  { name: 'Necklaces', href: '/collections/necklaces' },\n  { name: 'Earrings', href: '/collections/earrings' },\n  { name: 'Bracelets', href: '/collections/bracelets' },\n  { name: 'About', href: '/about' },\n  { name: 'Contact', href: '/contact' },\n];\n\nexport default function Header() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const [cartOpen, setCartOpen] = useState(false);\n  const { totalQuantity } = useCart();\n\n  return (\n    <>\n      <header className=\"bg-white shadow-sm sticky top-0 z-50\">\n        <nav className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\" aria-label=\"Top\">\n          <div className=\"flex h-16 items-center justify-between\">\n            {/* Mobile menu button */}\n            <div className=\"flex lg:hidden\">\n              <button\n                type=\"button\"\n                className=\"-ml-2 rounded-md bg-white p-2 text-gray-400\"\n                onClick={() => setMobileMenuOpen(true)}\n              >\n                <span className=\"sr-only\">Open menu</span>\n                <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\n              </button>\n            </div>\n\n            {/* Logo */}\n            <div className=\"flex lg:flex-1\">\n              <Link href=\"/\" className=\"-m-1.5 p-1.5\">\n                <span className=\"sr-only\">JW GOLD</span>\n                <h1 className=\"text-2xl font-bold font-playfair text-gray-900 tracking-wide\">\n                  JW GOLD\n                </h1>\n              </Link>\n            </div>\n\n            {/* Desktop navigation */}\n            <div className=\"hidden lg:flex lg:gap-x-8\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors duration-200\"\n                >\n                  {item.name}\n                </Link>\n              ))}\n            </div>\n\n            {/* Right side icons */}\n            <div className=\"flex items-center gap-x-4 lg:flex-1 lg:justify-end\">\n              {/* Search */}\n              <button\n                type=\"button\"\n                className=\"rounded-md bg-white p-2 text-gray-400 hover:text-gray-500\"\n              >\n                <span className=\"sr-only\">Search</span>\n                <MagnifyingGlassIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n              </button>\n\n              {/* Cart */}\n              <button\n                type=\"button\"\n                className=\"relative rounded-md bg-white p-2 text-gray-400 hover:text-gray-500\"\n                onClick={() => setCartOpen(true)}\n              >\n                <span className=\"sr-only\">Shopping cart</span>\n                <ShoppingBagIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                {totalQuantity > 0 && (\n                  <span className=\"absolute -top-1 -right-1 h-5 w-5 rounded-full bg-indigo-600 flex items-center justify-center text-xs font-medium text-white\">\n                    {totalQuantity}\n                  </span>\n                )}\n              </button>\n            </div>\n          </div>\n        </nav>\n\n        {/* Mobile menu */}\n        <Transition.Root show={mobileMenuOpen} as={Fragment}>\n          <Dialog as=\"div\" className=\"relative z-50 lg:hidden\" onClose={setMobileMenuOpen}>\n            <Transition.Child\n              as={Fragment}\n              enter=\"transition-opacity ease-linear duration-300\"\n              enterFrom=\"opacity-0\"\n              enterTo=\"opacity-100\"\n              leave=\"transition-opacity ease-linear duration-300\"\n              leaveFrom=\"opacity-100\"\n              leaveTo=\"opacity-0\"\n            >\n              <div className=\"fixed inset-0 bg-black bg-opacity-25\" />\n            </Transition.Child>\n\n            <div className=\"fixed inset-0 z-50 flex\">\n              <Transition.Child\n                as={Fragment}\n                enter=\"transition ease-in-out duration-300 transform\"\n                enterFrom=\"-translate-x-full\"\n                enterTo=\"translate-x-0\"\n                leave=\"transition ease-in-out duration-300 transform\"\n                leaveFrom=\"translate-x-0\"\n                leaveTo=\"-translate-x-full\"\n              >\n                <Dialog.Panel className=\"relative flex w-full max-w-xs flex-col overflow-y-auto bg-white pb-12 shadow-xl\">\n                  <div className=\"flex px-4 pb-2 pt-5\">\n                    <button\n                      type=\"button\"\n                      className=\"-m-2 inline-flex items-center justify-center rounded-md p-2 text-gray-400\"\n                      onClick={() => setMobileMenuOpen(false)}\n                    >\n                      <span className=\"sr-only\">Close menu</span>\n                      <XMarkIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                    </button>\n                  </div>\n\n                  <div className=\"space-y-6 border-t border-gray-200 px-4 py-6\">\n                    {navigation.map((item) => (\n                      <div key={item.name} className=\"flow-root\">\n                        <Link\n                          href={item.href}\n                          className=\"-m-2 block p-2 font-medium text-gray-900\"\n                          onClick={() => setMobileMenuOpen(false)}\n                        >\n                          {item.name}\n                        </Link>\n                      </div>\n                    ))}\n                  </div>\n                </Dialog.Panel>\n              </Transition.Child>\n            </div>\n          </Dialog>\n        </Transition.Root>\n      </header>\n\n      {/* Cart Sidebar */}\n      <CartSidebar open={cartOpen} setOpen={setCartOpen} />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAEA;;;AARA;;;;;;;;AAUA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAe,MAAM;IAAe;IAC5C;QAAE,MAAM;QAAS,MAAM;IAAqB;IAC5C;QAAE,MAAM;QAAa,MAAM;IAAyB;IACpD;QAAE,MAAM;QAAY,MAAM;IAAwB;IAClD;QAAE,MAAM;QAAa,MAAM;IAAyB;IACpD;QAAE,MAAM;QAAS,MAAM;IAAS;IAChC;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAEc,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAEhC,qBACE;;0BACE,6LAAC;gBAAO,WAAU;;kCAChB,6LAAC;wBAAI,WAAU;wBAAyC,cAAW;kCACjE,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;;0DAEjC,6LAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;gDAAU,eAAY;;;;;;;;;;;;;;;;;8CAK/C,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;;0DACvB,6LAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,6LAAC;gDAAG,WAAU;0DAA+D;;;;;;;;;;;;;;;;;8CAOjF,6LAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAU;sDAET,KAAK,IAAI;2CAJL,KAAK,IAAI;;;;;;;;;;8CAUpB,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CACC,MAAK;4CACL,WAAU;;8DAEV,6LAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,6LAAC,wOAAA,CAAA,sBAAmB;oDAAC,WAAU;oDAAU,eAAY;;;;;;;;;;;;sDAIvD,6LAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,YAAY;;8DAE3B,6LAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,6LAAC,gOAAA,CAAA,kBAAe;oDAAC,WAAU;oDAAU,eAAY;;;;;;gDAChD,gBAAgB,mBACf,6LAAC;oDAAK,WAAU;8DACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASb,6LAAC,0LAAA,CAAA,aAAU,CAAC,IAAI;wBAAC,MAAM;wBAAgB,IAAI,6JAAA,CAAA,WAAQ;kCACjD,cAAA,6LAAC,kLAAA,CAAA,SAAM;4BAAC,IAAG;4BAAM,WAAU;4BAA0B,SAAS;;8CAC5D,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;oCACf,IAAI,6JAAA,CAAA,WAAQ;oCACZ,OAAM;oCACN,WAAU;oCACV,SAAQ;oCACR,OAAM;oCACN,WAAU;oCACV,SAAQ;8CAER,cAAA,6LAAC;wCAAI,WAAU;;;;;;;;;;;8CAGjB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;wCACf,IAAI,6JAAA,CAAA,WAAQ;wCACZ,OAAM;wCACN,WAAU;wCACV,SAAQ;wCACR,OAAM;wCACN,WAAU;wCACV,SAAQ;kDAER,cAAA,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;4CAAC,WAAU;;8DACtB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,MAAK;wDACL,WAAU;wDACV,SAAS,IAAM,kBAAkB;;0EAEjC,6LAAC;gEAAK,WAAU;0EAAU;;;;;;0EAC1B,6LAAC,oNAAA,CAAA,YAAS;gEAAC,WAAU;gEAAU,eAAY;;;;;;;;;;;;;;;;;8DAI/C,6LAAC;oDAAI,WAAU;8DACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;4DAAoB,WAAU;sEAC7B,cAAA,6LAAC,+JAAA,CAAA,UAAI;gEACH,MAAM,KAAK,IAAI;gEACf,WAAU;gEACV,SAAS,IAAM,kBAAkB;0EAEhC,KAAK,IAAI;;;;;;2DANJ,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAmBnC,6LAAC,6HAAA,CAAA,UAAW;gBAAC,MAAM;gBAAU,SAAS;;;;;;;;AAG5C;GAvIwB;;QAGI,0HAAA,CAAA,UAAO;;;KAHX", "debugId": null}}]}