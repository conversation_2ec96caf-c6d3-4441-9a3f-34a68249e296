'use client';

import { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { shopifyHelpers } from '@/lib/shopify';
import { demoProducts } from '@/lib/demoProducts';
import ProductCard from '@/components/ProductCard';

export default function RingsPage() {
  const [products, setProducts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchRings = async () => {
      try {
        setLoading(true);
        // Filter demo products for rings
        const ringProducts = demoProducts.filter(product => 
          product.tags.includes('rings') || product.productType === 'Ring'
        );
        setProducts(ringProducts);
      } catch (error) {
        console.error('Error fetching rings:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchRings();
  }, []);

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <div className="relative bg-gray-900 py-24 sm:py-32">
        <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-black"></div>
        <div className="relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl font-bold font-playfair text-white sm:text-5xl lg:text-6xl">
              Rings Collection
            </h1>
            <p className="mt-6 text-xl text-gray-300 max-w-3xl mx-auto">
              Discover our exquisite collection of rings, from elegant engagement rings to statement pieces that celebrate life's special moments.
            </p>
          </motion.div>
        </div>
      </div>

      {/* Products Section */}
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16 sm:py-24">
        <div className="text-center mb-12">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl font-bold font-playfair text-gray-900 sm:text-4xl"
          >
            Handcrafted Rings
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="mt-4 text-lg text-gray-600 max-w-2xl mx-auto"
          >
            Each ring is meticulously crafted with the finest materials and attention to detail.
          </motion.p>
        </div>

        {loading ? (
          <div className="grid grid-cols-1 gap-y-10 gap-x-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 xl:gap-x-8">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="aspect-square w-full bg-gray-200 rounded-lg"></div>
                <div className="mt-4 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        ) : products.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">No rings available at the moment.</p>
            <p className="text-gray-400 mt-2">Please check back soon for new arrivals.</p>
          </div>
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6 }}
            className="grid grid-cols-1 gap-y-10 gap-x-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 xl:gap-x-8"
          >
            {products.map((product, index) => (
              <motion.div
                key={product.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <ProductCard product={product} />
              </motion.div>
            ))}
          </motion.div>
        )}

        {/* Ring Care Information */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mt-20 bg-gray-50 rounded-lg p-8"
        >
          <h3 className="text-2xl font-bold font-playfair text-gray-900 mb-6 text-center">
            Ring Care & Sizing Guide
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h4 className="text-lg font-semibold text-gray-900 mb-4">Care Instructions</h4>
              <ul className="space-y-2 text-gray-600">
                <li>• Clean regularly with mild soap and warm water</li>
                <li>• Store in a soft cloth or jewelry box</li>
                <li>• Remove before swimming or exercising</li>
                <li>• Have professionally cleaned annually</li>
                <li>• Check prongs and settings regularly</li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold text-gray-900 mb-4">Sizing Information</h4>
              <ul className="space-y-2 text-gray-600">
                <li>• Free sizing within 30 days of purchase</li>
                <li>• Professional sizing available in-store</li>
                <li>• Ring sizer available upon request</li>
                <li>• Most rings can be sized up or down 2 sizes</li>
                <li>• Eternity bands cannot be resized</li>
              </ul>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
