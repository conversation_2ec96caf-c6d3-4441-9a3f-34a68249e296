{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/shopify-buy/index.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/shopify-buy/node_modules/graphql-js-client/index.es.js", "file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/shopify-buy/src/config.js", "file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/shopify-buy/src/input-map-resource.js", "file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/shopify-buy/src/resource.js", "file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/shopify-buy/src/default-resolver.js", "file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/shopify-buy/src/fetch-resources-for-products.js", "file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/shopify-buy/src/paginators.js", "file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/shopify-buy/src/product-helpers.js", "file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/shopify-buy/src/product-resource.js", "file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/shopify-buy/src/collection-resource.js", "file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/shopify-buy/src/shop-resource.js", "file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/shopify-buy/src/utilities/cart-discount-mapping.js", "file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/shopify-buy/src/utilities/cart-mapping-utils.js", "file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/shopify-buy/src/cart-payload-mapper.js", "file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/shopify-buy/src/checkout-map-user-error-codes.js", "file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/shopify-buy/src/handle-cart-mutation.js", "file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/shopify-buy/src/checkout-resource.js", "file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/shopify-buy/src/image-helpers.js", "file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/shopify-buy/src/image-resource.js", "file:///C:/Users/<USER>/OneDrive/Desktop/jwgold/jwgold-frontend/node_modules/shopify-buy/src/client.js"], "sourcesContent": ["/*\nThe MIT License (MIT)\nCopyright (c) 2016 Shopify Inc.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\nDAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\nOTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE\nOR OTHER DEALINGS IN THE SOFTWARE.\n\n\n*/\nfunction join() {\n  for (var _len = arguments.length, fields = Array(_len), _key = 0; _key < _len; _key++) {\n    fields[_key] = arguments[_key];\n  }\n\n  return fields.join(' ');\n}\n\nfunction isObject(value) {\n  return Boolean(value) && Object.prototype.toString.call(value.valueOf()) === '[object Object]';\n}\n\nfunction deepFreezeCopyExcept(predicate, structure) {\n  if (predicate(structure)) {\n    return structure;\n  } else if (isObject(structure)) {\n    return Object.freeze(Object.keys(structure).reduce(function (copy, key) {\n      copy[key] = deepFreezeCopyExcept(predicate, structure[key]);\n\n      return copy;\n    }, {}));\n  } else if (Array.isArray(structure)) {\n    return Object.freeze(structure.map(function (item) {\n      return deepFreezeCopyExcept(predicate, item);\n    }));\n  } else {\n    return structure;\n  }\n}\n\nfunction schemaForType(typeBundle, typeName) {\n  var typeSchema = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n\n  var type = typeBundle.types[typeName];\n\n  if (type) {\n    return type;\n  } else if (typeSchema && typeSchema.kind === 'INTERFACE') {\n    return typeSchema;\n  }\n\n  throw new Error('No type of ' + typeName + ' found in schema');\n}\n\nvar classCallCheck = function (instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n};\n\nvar createClass = function () {\n  function defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, descriptor.key, descriptor);\n    }\n  }\n\n  return function (Constructor, protoProps, staticProps) {\n    if (protoProps) defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) defineProperties(Constructor, staticProps);\n    return Constructor;\n  };\n}();\n\n\n\n\n\n\n\nvar _extends = Object.assign || function (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n\n    for (var key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n\n  return target;\n};\n\n\n\nvar inherits = function (subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n  }\n\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      enumerable: false,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n};\n\n\n\n\n\n\n\n\n\n\n\nvar possibleConstructorReturn = function (self, call) {\n  if (!self) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n};\n\n\n\n\n\nvar slicedToArray = function () {\n  function sliceIterator(arr, i) {\n    var _arr = [];\n    var _n = true;\n    var _d = false;\n    var _e = undefined;\n\n    try {\n      for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {\n        _arr.push(_s.value);\n\n        if (i && _arr.length === i) break;\n      }\n    } catch (err) {\n      _d = true;\n      _e = err;\n    } finally {\n      try {\n        if (!_n && _i[\"return\"]) _i[\"return\"]();\n      } finally {\n        if (_d) throw _e;\n      }\n    }\n\n    return _arr;\n  }\n\n  return function (arr, i) {\n    if (Array.isArray(arr)) {\n      return arr;\n    } else if (Symbol.iterator in Object(arr)) {\n      return sliceIterator(arr, i);\n    } else {\n      throw new TypeError(\"Invalid attempt to destructure non-iterable instance\");\n    }\n  };\n}();\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar toConsumableArray = function (arr) {\n  if (Array.isArray(arr)) {\n    for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) arr2[i] = arr[i];\n\n    return arr2;\n  } else {\n    return Array.from(arr);\n  }\n};\n\nvar VariableDefinition = function () {\n\n  /**\n   * This constructor should not be invoked directly.\n   * Use the factory function {@link Client#variable} to create a VariableDefinition.\n   *\n   * @param {String} name The name of the variable.\n   * @param {String} type The GraphQL type of the variable.\n   * @param {*} [defaultValue] The default value of the variable.\n   */\n  function VariableDefinition(name, type, defaultValue) {\n    classCallCheck(this, VariableDefinition);\n\n    this.name = name;\n    this.type = type;\n    this.defaultValue = defaultValue;\n    Object.freeze(this);\n  }\n\n  /**\n   * Returns the GraphQL query string for the variable as an input value (e.g. `$variableName`).\n   *\n   * @return {String} The GraphQL query string for the variable as an input value.\n   */\n\n\n  createClass(VariableDefinition, [{\n    key: 'toInputValueString',\n    value: function toInputValueString() {\n      return '$' + this.name;\n    }\n\n    /**\n     * Returns the GraphQL query string for the variable (e.g. `$variableName:VariableType = defaultValue`).\n     *\n     * @return {String} The GraphQL query string for the variable.\n     */\n\n  }, {\n    key: 'toString',\n    value: function toString() {\n      var defaultValueString = this.defaultValue ? ' = ' + formatInputValue(this.defaultValue) : '';\n\n      return '$' + this.name + ':' + this.type + defaultValueString;\n    }\n  }]);\n  return VariableDefinition;\n}();\n\nfunction isVariable(value) {\n  return VariableDefinition.prototype.isPrototypeOf(value);\n}\n\nfunction variable(name, type, defaultValue) {\n  return new VariableDefinition(name, type, defaultValue);\n}\n\nvar Enum = function () {\n\n  /**\n   * This constructor should not be invoked directly.\n   * Use the factory function {@link Client#enum} to create an Enum.\n   *\n   * @param {String} key The key of the enum.\n   */\n  function Enum(key) {\n    classCallCheck(this, Enum);\n\n    this.key = key;\n  }\n\n  /**\n   * Returns the GraphQL query string for the enum (e.g. `enumKey`).\n   *\n   * @return {String} The GraphQL query string for the enum.\n   */\n\n\n  createClass(Enum, [{\n    key: \"toString\",\n    value: function toString() {\n      return this.key;\n    }\n  }, {\n    key: \"valueOf\",\n    value: function valueOf() {\n      return this.key.valueOf();\n    }\n  }]);\n  return Enum;\n}();\n\nvar enumFunction = (function (key) {\n  return new Enum(key);\n});\n\nvar Scalar = function () {\n  function Scalar(value) {\n    classCallCheck(this, Scalar);\n\n    this.value = value;\n  }\n\n  createClass(Scalar, [{\n    key: \"toString\",\n    value: function toString() {\n      return this.value.toString();\n    }\n  }, {\n    key: \"valueOf\",\n    value: function valueOf() {\n      return this.value.valueOf();\n    }\n  }, {\n    key: \"unwrapped\",\n    get: function get$$1() {\n      return this.value;\n    }\n  }]);\n  return Scalar;\n}();\n\nfunction formatInputValue(value) {\n  if (VariableDefinition.prototype.isPrototypeOf(value)) {\n    return value.toInputValueString();\n  } else if (Enum.prototype.isPrototypeOf(value)) {\n    return String(value);\n  } else if (Scalar.prototype.isPrototypeOf(value)) {\n    return JSON.stringify(value.valueOf());\n  } else if (Array.isArray(value)) {\n    return '[' + join.apply(undefined, toConsumableArray(value.map(formatInputValue))) + ']';\n  } else if (isObject(value)) {\n    return formatObject(value, '{', '}');\n  } else {\n    return JSON.stringify(value);\n  }\n}\n\nfunction formatObject(value) {\n  var openChar = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n  var closeChar = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : '';\n\n  var argPairs = Object.keys(value).map(function (key) {\n    return key + ': ' + formatInputValue(value[key]);\n  });\n\n  return '' + openChar + join.apply(undefined, toConsumableArray(argPairs)) + closeChar;\n}\n\nfunction formatArgs(args) {\n  if (!Object.keys(args).length) {\n    return '';\n  }\n\n  return ' (' + formatObject(args) + ')';\n}\n\nfunction formatDirectives(directives) {\n  if (!Object.keys(directives).length) {\n    return '';\n  }\n\n  var directiveStrings = Object.keys(directives).map(function (key) {\n    var directiveArgs = directives[key];\n    var arg = directiveArgs && Object.keys(directiveArgs).length ? '(' + formatObject(directiveArgs) + ')' : '';\n\n    return '@' + key + arg;\n  });\n\n  return ' ' + join.apply(undefined, toConsumableArray(directiveStrings));\n}\n\n// eslint-disable-next-line no-empty-function\nvar noop = (function () {});\n\nvar Profiler = {\n  trackTypeDependency: noop,\n  trackFieldDependency: noop\n};\n\nvar trackTypeDependency = Profiler.trackTypeDependency;\nvar trackFieldDependency = Profiler.trackFieldDependency;\n\n\nfunction parseFieldCreationArgs(creationArgs) {\n  var callback = noop;\n  var options = {};\n  var selectionSet = null;\n\n  if (creationArgs.length === 2) {\n    if (typeof creationArgs[1] === 'function') {\n      var _creationArgs = slicedToArray(creationArgs, 2);\n\n      options = _creationArgs[0];\n      callback = _creationArgs[1];\n    } else {\n      var _creationArgs2 = slicedToArray(creationArgs, 2);\n\n      options = _creationArgs2[0];\n      selectionSet = _creationArgs2[1];\n    }\n  } else if (creationArgs.length === 1) {\n    // SelectionSet is defined before this function is called since it's\n    // called by SelectionSet\n    // eslint-disable-next-line no-use-before-define\n    if (SelectionSet.prototype.isPrototypeOf(creationArgs[0])) {\n      selectionSet = creationArgs[0];\n    } else if (typeof creationArgs[0] === 'function') {\n      callback = creationArgs[0];\n    } else {\n      options = creationArgs[0];\n    }\n  }\n\n  return { options: options, selectionSet: selectionSet, callback: callback };\n}\n\nvar emptyArgs = Object.freeze({});\nvar emptyDirectives = Object.freeze({});\n\nvar Field = function () {\n\n  /**\n   * This constructor should not be invoked directly.\n   * Fields are added to a selection by {@link SelectionSetBuilder#add}, {@link SelectionSetBuilder#addConnection}\n   * and {@link SelectionSetBuilder#addInlineFragmentOn}.\n   *\n   * @param {String} name The name of the field.\n   * @param {Object} [options] An options object containing:\n   *   @param {Object} [options.args] Arguments for the field.\n   *   @param {String} [options.alias] An alias for the field.\n   *   @param {Object} [options.directives] Directives for the field.\n   * @param {SelectionSet} selectionSet The selection set on the field.\n   */\n  function Field(name, options, selectionSet) {\n    classCallCheck(this, Field);\n\n    this.name = name;\n    this.alias = options.alias || null;\n    this.responseKey = this.alias || this.name;\n    this.args = options.args ? deepFreezeCopyExcept(isVariable, options.args) : emptyArgs;\n    this.directives = options.directives ? deepFreezeCopyExcept(isVariable, options.directives) : emptyDirectives;\n    this.selectionSet = selectionSet;\n    Object.freeze(this);\n  }\n\n  /**\n   * Returns the GraphQL query string for the Field (e.g. `catAlias: cat(size: 'small') { name }` or `name`).\n   *\n   * @return {String} The GraphQL query string for the Field.\n   */\n\n\n  createClass(Field, [{\n    key: 'toString',\n    value: function toString() {\n      var aliasPrefix = this.alias ? this.alias + ': ' : '';\n\n      return '' + aliasPrefix + this.name + formatArgs(this.args) + formatDirectives(this.directives) + this.selectionSet;\n    }\n  }]);\n  return Field;\n}();\n\n// This is an interface that defines a usage, and simplifies type checking\nvar Spread = function Spread() {\n  classCallCheck(this, Spread);\n};\n\nvar InlineFragment = function (_Spread) {\n  inherits(InlineFragment, _Spread);\n\n  /**\n   * This constructor should not be invoked directly.\n   * Use the factory function {@link SelectionSetBuilder#addInlineFragmentOn} to create an InlineFragment.\n   *\n   * @param {String} typeName The type of the fragment.\n   * @param {SelectionSet} selectionSet The selection set on the fragment.\n   */\n  function InlineFragment(typeName, selectionSet) {\n    classCallCheck(this, InlineFragment);\n\n    var _this = possibleConstructorReturn(this, (InlineFragment.__proto__ || Object.getPrototypeOf(InlineFragment)).call(this));\n\n    _this.typeName = typeName;\n    _this.selectionSet = selectionSet;\n    Object.freeze(_this);\n    return _this;\n  }\n\n  /**\n   * Returns the GraphQL query string for the InlineFragment (e.g. `... on Cat { name }`).\n   *\n   * @return {String} The GraphQL query string for the InlineFragment.\n   */\n\n\n  createClass(InlineFragment, [{\n    key: 'toString',\n    value: function toString() {\n      return '... on ' + this.typeName + this.selectionSet;\n    }\n  }]);\n  return InlineFragment;\n}(Spread);\n\nvar FragmentSpread = function (_Spread2) {\n  inherits(FragmentSpread, _Spread2);\n\n  /**\n   * This constructor should not be invoked directly.\n   * Use the factory function {@link Document#defineFragment} to create a FragmentSpread.\n   *\n   * @param {FragmentDefinition} fragmentDefinition The corresponding fragment definition.\n   */\n  function FragmentSpread(fragmentDefinition) {\n    classCallCheck(this, FragmentSpread);\n\n    var _this2 = possibleConstructorReturn(this, (FragmentSpread.__proto__ || Object.getPrototypeOf(FragmentSpread)).call(this));\n\n    _this2.name = fragmentDefinition.name;\n    _this2.selectionSet = fragmentDefinition.selectionSet;\n    Object.freeze(_this2);\n    return _this2;\n  }\n\n  /**\n   * Returns the GraphQL query string for the FragmentSpread (e.g. `...catName`).\n   *\n   * @return {String} The GraphQL query string for the FragmentSpread.\n   */\n\n\n  createClass(FragmentSpread, [{\n    key: 'toString',\n    value: function toString() {\n      return '...' + this.name;\n    }\n  }, {\n    key: 'toDefinition',\n    value: function toDefinition() {\n      // eslint-disable-next-line no-use-before-define\n      return new FragmentDefinition(this.name, this.selectionSet.typeSchema.name, this.selectionSet);\n    }\n  }]);\n  return FragmentSpread;\n}(Spread);\n\nvar FragmentDefinition = function () {\n\n  /**\n   * This constructor should not be invoked directly.\n   * Use the factory function {@link Document#defineFragment} to create a FragmentDefinition on a {@link Document}.\n   *\n   * @param {String} name The name of the fragment definition.\n   * @param {String} typeName The type of the fragment.\n   */\n  function FragmentDefinition(name, typeName, selectionSet) {\n    classCallCheck(this, FragmentDefinition);\n\n    this.name = name;\n    this.typeName = typeName;\n    this.selectionSet = selectionSet;\n    this.spread = new FragmentSpread(this);\n    Object.freeze(this);\n  }\n\n  /**\n   * Returns the GraphQL query string for the FragmentDefinition (e.g. `fragment catName on Cat { name }`).\n   *\n   * @return {String} The GraphQL query string for the FragmentDefinition.\n   */\n\n\n  createClass(FragmentDefinition, [{\n    key: 'toString',\n    value: function toString() {\n      return 'fragment ' + this.name + ' on ' + this.typeName + ' ' + this.selectionSet;\n    }\n  }]);\n  return FragmentDefinition;\n}();\n\nfunction selectionsHaveIdField(selections) {\n  return selections.some(function (fieldOrFragment) {\n    if (Field.prototype.isPrototypeOf(fieldOrFragment)) {\n      return fieldOrFragment.name === 'id';\n    } else if (Spread.prototype.isPrototypeOf(fieldOrFragment) && fieldOrFragment.selectionSet.typeSchema.implementsNode) {\n      return selectionsHaveIdField(fieldOrFragment.selectionSet.selections);\n    }\n\n    return false;\n  });\n}\n\nfunction selectionsHaveTypenameField(selections) {\n  return selections.some(function (fieldOrFragment) {\n    if (Field.prototype.isPrototypeOf(fieldOrFragment)) {\n      return fieldOrFragment.name === '__typename';\n    } else if (Spread.prototype.isPrototypeOf(fieldOrFragment) && fieldOrFragment.selectionSet.typeSchema.implementsNode) {\n      return selectionsHaveTypenameField(fieldOrFragment.selectionSet.selections);\n    }\n\n    return false;\n  });\n}\n\nfunction indexSelectionsByResponseKey(selections) {\n  function assignOrPush(obj, key, value) {\n    if (Array.isArray(obj[key])) {\n      obj[key].push(value);\n    } else {\n      obj[key] = [value];\n    }\n  }\n  var unfrozenObject = selections.reduce(function (acc, selection) {\n    if (selection.responseKey) {\n      assignOrPush(acc, selection.responseKey, selection);\n    } else {\n      var responseKeys = Object.keys(selection.selectionSet.selectionsByResponseKey);\n\n      responseKeys.forEach(function (responseKey) {\n        assignOrPush(acc, responseKey, selection);\n      });\n    }\n\n    return acc;\n  }, {});\n\n  Object.keys(unfrozenObject).forEach(function (key) {\n    Object.freeze(unfrozenObject[key]);\n  });\n\n  return Object.freeze(unfrozenObject);\n}\n\n/**\n * Class that specifies the full selection of data to query.\n */\n\nvar SelectionSet = function () {\n\n  /**\n   * This constructor should not be invoked directly. SelectionSets are created when building queries/mutations.\n   *\n   * @param {Object} typeBundle A set of ES6 modules generated by {@link https://github.com/Shopify/graphql-js-schema|graphql-js-schema}.\n   * @param {(Object|String)} type The type of the current selection.\n   * @param {Function} builderFunction Callback function used to build the SelectionSet.\n   *   The callback takes a {@link SelectionSetBuilder} as its argument.\n   */\n  function SelectionSet(typeBundle, type, builderFunction) {\n    classCallCheck(this, SelectionSet);\n\n\n    if (typeof type === 'string') {\n      this.typeSchema = schemaForType(typeBundle, type);\n    } else {\n      this.typeSchema = type;\n    }\n\n    trackTypeDependency(this.typeSchema.name);\n\n    this.typeBundle = typeBundle;\n    this.selections = [];\n    if (builderFunction) {\n      // eslint-disable-next-line no-use-before-define\n      builderFunction(new SelectionSetBuilder(this.typeBundle, this.typeSchema, this.selections));\n    }\n\n    if (this.typeSchema.implementsNode || this.typeSchema.name === 'Node') {\n      if (!selectionsHaveIdField(this.selections)) {\n        this.selections.unshift(new Field('id', {}, new SelectionSet(typeBundle, 'ID')));\n      }\n    }\n\n    if (this.typeSchema.kind === 'INTERFACE') {\n      if (!selectionsHaveTypenameField(this.selections)) {\n        this.selections.unshift(new Field('__typename', {}, new SelectionSet(typeBundle, 'String')));\n      }\n    }\n\n    this.selectionsByResponseKey = indexSelectionsByResponseKey(this.selections);\n    Object.freeze(this.selections);\n    Object.freeze(this);\n  }\n\n  /**\n   * Returns the GraphQL query string for the SelectionSet (e.g. `{ cat { name } }`).\n   *\n   * @return {String} The GraphQL query string for the SelectionSet.\n   */\n\n\n  createClass(SelectionSet, [{\n    key: 'toString',\n    value: function toString() {\n      if (this.typeSchema.kind === 'SCALAR' || this.typeSchema.kind === 'ENUM') {\n        return '';\n      } else {\n        return ' { ' + join(this.selections) + ' }';\n      }\n    }\n  }]);\n  return SelectionSet;\n}();\n\nvar SelectionSetBuilder = function () {\n\n  /**\n   * This constructor should not be invoked directly. SelectionSetBuilders are created when building queries/mutations.\n   *\n   * @param {Object} typeBundle A set of ES6 modules generated by {@link https://github.com/Shopify/graphql-js-schema|graphql-js-schema}.\n   * @param {Object} typeSchema The schema object for the type of the current selection.\n   * @param {Field[]} selections The fields on the current selection.\n   */\n  function SelectionSetBuilder(typeBundle, typeSchema, selections) {\n    classCallCheck(this, SelectionSetBuilder);\n\n    this.typeBundle = typeBundle;\n    this.typeSchema = typeSchema;\n    this.selections = selections;\n  }\n\n  createClass(SelectionSetBuilder, [{\n    key: 'hasSelectionWithResponseKey',\n    value: function hasSelectionWithResponseKey(responseKey) {\n      return this.selections.some(function (field) {\n        return field.responseKey === responseKey;\n      });\n    }\n\n    /**\n     * Adds a field to be queried on the current selection.\n     *\n     * @example\n     * client.query((root) => {\n     *   root.add('cat', {args: {id: '123456'}, alias: 'meow'}, (cat) => {\n     *     cat.add('name');\n     *   });\n     * });\n     *\n     * @param {SelectionSet|String} selectionOrFieldName The selection or name of the field to add.\n     * @param {Object} [options] Options on the query including:\n     *   @param {Object} [options.args] Arguments on the query (e.g. `{id: '123456'}`).\n     *   @param {String} [options.alias] Alias for the field being added.\n     * @param {Function|SelectionSet} [callbackOrSelectionSet] Either a callback which will be used to create a new {@link SelectionSet}, or an existing {@link SelectionSet}.\n     */\n\n  }, {\n    key: 'add',\n    value: function add(selectionOrFieldName) {\n      var selection = void 0;\n\n      if (Object.prototype.toString.call(selectionOrFieldName) === '[object String]') {\n        trackFieldDependency(this.typeSchema.name, selectionOrFieldName);\n\n        for (var _len = arguments.length, rest = Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n          rest[_key - 1] = arguments[_key];\n        }\n\n        selection = this.field.apply(this, [selectionOrFieldName].concat(rest));\n      } else {\n        if (Field.prototype.isPrototypeOf(selectionOrFieldName)) {\n          trackFieldDependency(this.typeSchema.name, selectionOrFieldName.name);\n        }\n\n        selection = selectionOrFieldName;\n      }\n\n      if (selection.responseKey && this.hasSelectionWithResponseKey(selection.responseKey)) {\n        throw new Error('The field name or alias \\'' + selection.responseKey + '\\' has already been added.');\n      }\n      this.selections.push(selection);\n    }\n  }, {\n    key: 'field',\n    value: function field(name) {\n      for (var _len2 = arguments.length, creationArgs = Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        creationArgs[_key2 - 1] = arguments[_key2];\n      }\n\n      var parsedArgs = parseFieldCreationArgs(creationArgs);\n      var options = parsedArgs.options,\n          callback = parsedArgs.callback;\n      var selectionSet = parsedArgs.selectionSet;\n\n\n      if (!selectionSet) {\n        if (!this.typeSchema.fieldBaseTypes[name]) {\n          throw new Error('No field of name \"' + name + '\" found on type \"' + this.typeSchema.name + '\" in schema');\n        }\n\n        var fieldBaseType = schemaForType(this.typeBundle, this.typeSchema.fieldBaseTypes[name]);\n\n        selectionSet = new SelectionSet(this.typeBundle, fieldBaseType, callback);\n      }\n\n      return new Field(name, options, selectionSet);\n    }\n\n    /**\n     * Creates an inline fragment.\n     *\n     * @access private\n     * @param {String} typeName The type  the inline fragment.\n     * @param {Function|SelectionSet}  [callbackOrSelectionSet] Either a callback which will be used to create a new {@link SelectionSet}, or an existing {@link SelectionSet}.\n     * @return {InlineFragment} An inline fragment.\n     */\n\n  }, {\n    key: 'inlineFragmentOn',\n    value: function inlineFragmentOn(typeName) {\n      var builderFunctionOrSelectionSet = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : noop;\n\n      var selectionSet = void 0;\n\n      if (SelectionSet.prototype.isPrototypeOf(builderFunctionOrSelectionSet)) {\n        selectionSet = builderFunctionOrSelectionSet;\n      } else {\n        selectionSet = new SelectionSet(this.typeBundle, schemaForType(this.typeBundle, typeName), builderFunctionOrSelectionSet);\n      }\n\n      return new InlineFragment(typeName, selectionSet);\n    }\n\n    /**\n     * Adds a field to be queried on the current selection.\n     *\n     * @access private\n     * @param {String}    name The name of the field to add to the query.\n     * @param {Object} [options] Options on the query including:\n     *   @param {Object} [options.args] Arguments on the query (e.g. `{id: '123456'}`).\n     *   @param {String} [options.alias] Alias for the field being added.\n     * @param {Function}  [callback] Callback which will be used to create a new {@link SelectionSet} for the field added.\n     */\n\n  }, {\n    key: 'addField',\n    value: function addField(name) {\n      for (var _len3 = arguments.length, creationArgs = Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {\n        creationArgs[_key3 - 1] = arguments[_key3];\n      }\n\n      this.add.apply(this, [name].concat(creationArgs));\n    }\n\n    /**\n     * Adds a connection to be queried on the current selection.\n     * This adds all the fields necessary for pagination.\n     *\n     * @example\n     * client.query((root) => {\n     *   root.add('cat', (cat) => {\n     *     cat.addConnection('friends', {args: {first: 10}, alias: 'coolCats'}, (friends) => {\n     *       friends.add('name');\n     *     });\n     *   });\n     * });\n     *\n     * @param {String}    name The name of the connection to add to the query.\n     * @param {Object} [options] Options on the query including:\n     *   @param {Object} [options.args] Arguments on the query (e.g. `{first: 10}`).\n     *   @param {String} [options.alias] Alias for the field being added.\n     * @param {Function|SelectionSet}  [callbackOrSelectionSet] Either a callback which will be used to create a new {@link SelectionSet}, or an existing {@link SelectionSet}.\n     */\n\n  }, {\n    key: 'addConnection',\n    value: function addConnection(name) {\n      for (var _len4 = arguments.length, creationArgs = Array(_len4 > 1 ? _len4 - 1 : 0), _key4 = 1; _key4 < _len4; _key4++) {\n        creationArgs[_key4 - 1] = arguments[_key4];\n      }\n\n      var _parseFieldCreationAr = parseFieldCreationArgs(creationArgs),\n          options = _parseFieldCreationAr.options,\n          callback = _parseFieldCreationAr.callback,\n          selectionSet = _parseFieldCreationAr.selectionSet;\n\n      this.add(name, options, function (connection) {\n        connection.add('pageInfo', {}, function (pageInfo) {\n          pageInfo.add('hasNextPage');\n          pageInfo.add('hasPreviousPage');\n        });\n        connection.add('edges', {}, function (edges) {\n          edges.add('cursor');\n          edges.addField('node', {}, selectionSet || callback); // This is bad. Don't do this\n        });\n      });\n    }\n\n    /**\n     * Adds an inline fragment on the current selection.\n     *\n     * @example\n     * client.query((root) => {\n     *   root.add('animal', (animal) => {\n     *     animal.addInlineFragmentOn('cat', (cat) => {\n     *       cat.add('name');\n     *     });\n     *   });\n     * });\n     *\n     * @param {String} typeName The name of the type of the inline fragment.\n     * @param {Function|SelectionSet}  [callbackOrSelectionSet] Either a callback which will be used to create a new {@link SelectionSet}, or an existing {@link SelectionSet}.\n     */\n\n  }, {\n    key: 'addInlineFragmentOn',\n    value: function addInlineFragmentOn(typeName) {\n      var fieldTypeCb = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : noop;\n\n      this.add(this.inlineFragmentOn(typeName, fieldTypeCb));\n    }\n\n    /**\n     * Adds a fragment spread on the current selection.\n     *\n     * @example\n     * client.query((root) => {\n     *   root.addFragment(catFragmentSpread);\n     * });\n     *\n     * @param {FragmentSpread} fragmentSpread The fragment spread to add.\n     */\n\n  }, {\n    key: 'addFragment',\n    value: function addFragment(fragmentSpread) {\n      this.add(fragmentSpread);\n    }\n  }]);\n  return SelectionSetBuilder;\n}();\n\nfunction parseArgs(args) {\n  var name = void 0;\n  var variables = void 0;\n  var selectionSetCallback = void 0;\n\n  if (args.length === 3) {\n    var _args = slicedToArray(args, 3);\n\n    name = _args[0];\n    variables = _args[1];\n    selectionSetCallback = _args[2];\n  } else if (args.length === 2) {\n    if (Object.prototype.toString.call(args[0]) === '[object String]') {\n      name = args[0];\n      variables = null;\n    } else if (Array.isArray(args[0])) {\n      variables = args[0];\n      name = null;\n    }\n\n    selectionSetCallback = args[1];\n  } else {\n    selectionSetCallback = args[0];\n    name = null;\n  }\n\n  return { name: name, variables: variables, selectionSetCallback: selectionSetCallback };\n}\n\nvar VariableDefinitions = function () {\n  function VariableDefinitions(variableDefinitions) {\n    classCallCheck(this, VariableDefinitions);\n\n    this.variableDefinitions = variableDefinitions ? [].concat(toConsumableArray(variableDefinitions)) : [];\n    Object.freeze(this.variableDefinitions);\n    Object.freeze(this);\n  }\n\n  createClass(VariableDefinitions, [{\n    key: 'toString',\n    value: function toString() {\n      if (this.variableDefinitions.length === 0) {\n        return '';\n      }\n\n      return ' (' + join(this.variableDefinitions) + ') ';\n    }\n  }]);\n  return VariableDefinitions;\n}();\n\n/**\n * Base class for {@link Query} and {@link Mutation}.\n * @abstract\n */\n\n\nvar Operation = function () {\n\n  /**\n   * This constructor should not be invoked. The subclasses {@link Query} and {@link Mutation} should be used instead.\n   */\n  function Operation(typeBundle, operationType) {\n    classCallCheck(this, Operation);\n\n    for (var _len = arguments.length, args = Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {\n      args[_key - 2] = arguments[_key];\n    }\n\n    var _parseArgs = parseArgs(args),\n        name = _parseArgs.name,\n        variables = _parseArgs.variables,\n        selectionSetCallback = _parseArgs.selectionSetCallback;\n\n    this.typeBundle = typeBundle;\n    this.name = name;\n    this.variableDefinitions = new VariableDefinitions(variables);\n    this.operationType = operationType;\n    if (operationType === 'query') {\n      this.selectionSet = new SelectionSet(typeBundle, typeBundle.queryType, selectionSetCallback);\n      this.typeSchema = schemaForType(typeBundle, typeBundle.queryType);\n    } else {\n      this.selectionSet = new SelectionSet(typeBundle, typeBundle.mutationType, selectionSetCallback);\n      this.typeSchema = schemaForType(typeBundle, typeBundle.mutationType);\n    }\n    Object.freeze(this);\n  }\n\n  /**\n   * Whether the operation is anonymous (i.e. has no name).\n   */\n\n\n  createClass(Operation, [{\n    key: 'toString',\n\n\n    /**\n     * Returns the GraphQL query or mutation string (e.g. `query myQuery { cat { name } }`).\n     *\n     * @return {String} The GraphQL query or mutation string.\n     */\n    value: function toString() {\n      var nameString = this.name ? ' ' + this.name : '';\n\n      return '' + this.operationType + nameString + this.variableDefinitions + this.selectionSet;\n    }\n  }, {\n    key: 'isAnonymous',\n    get: function get$$1() {\n      return !this.name;\n    }\n  }]);\n  return Operation;\n}();\n\n/**\n * GraphQL Query class.\n * @extends Operation\n */\n\nvar Query = function (_Operation) {\n  inherits(Query, _Operation);\n\n  /**\n   * This constructor should not be invoked directly.\n   * Use the factory functions {@link Client#query} or {@link Document#addQuery} to create a Query.\n   *\n   * @param {Object} typeBundle A set of ES6 modules generated by {@link https://github.com/Shopify/graphql-js-schema|graphql-js-schema}.\n   * @param {String} [name] A name for the query.\n   * @param {Object[]} [variables] A list of variables in the query. See {@link Client#variable}.\n   * @param {Function} selectionSetCallback The query builder callback.\n   *   A {@link SelectionSet} is created using this callback.\n   */\n  function Query(typeBundle) {\n    var _ref;\n\n    classCallCheck(this, Query);\n\n    for (var _len = arguments.length, args = Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n\n    return possibleConstructorReturn(this, (_ref = Query.__proto__ || Object.getPrototypeOf(Query)).call.apply(_ref, [this, typeBundle, 'query'].concat(args)));\n  }\n\n  return Query;\n}(Operation);\n\n/**\n * GraphQL Mutation class.\n * @extends Operation\n */\n\nvar Mutation = function (_Operation) {\n  inherits(Mutation, _Operation);\n\n  /**\n   * This constructor should not be invoked directly.\n   * Use the factory functions {@link Client#mutation} or {@link Document#addMutation} to create a Mutation.\n   *\n   * @param {Object} typeBundle A set of ES6 modules generated by {@link https://github.com/Shopify/graphql-js-schema|graphql-js-schema}.\n   * @param {String} [name] A name for the mutation.\n   * @param {Object[]} [variables] A list of variables in the mutation. See {@link Client#variable}.\n   * @param {Function} selectionSetCallback The mutation builder callback.\n   *   A {@link SelectionSet} is created using this callback.\n   */\n  function Mutation(typeBundle) {\n    var _ref;\n\n    classCallCheck(this, Mutation);\n\n    for (var _len = arguments.length, args = Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n\n    return possibleConstructorReturn(this, (_ref = Mutation.__proto__ || Object.getPrototypeOf(Mutation)).call.apply(_ref, [this, typeBundle, 'mutation'].concat(args)));\n  }\n\n  return Mutation;\n}(Operation);\n\nfunction isAnonymous(operation) {\n  return operation.isAnonymous;\n}\n\nfunction hasAnonymousOperations(operations) {\n  return operations.some(isAnonymous);\n}\n\nfunction hasDuplicateOperationNames(operations) {\n  var names = operations.map(function (operation) {\n    return operation.name;\n  });\n\n  return names.reduce(function (hasDuplicates, name, index) {\n    return hasDuplicates || names.indexOf(name) !== index;\n  }, false);\n}\n\nfunction extractOperation(typeBundle, operationType) {\n  for (var _len = arguments.length, args = Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {\n    args[_key - 2] = arguments[_key];\n  }\n\n  if (Operation.prototype.isPrototypeOf(args[0])) {\n    return args[0];\n  }\n\n  if (operationType === 'query') {\n    return new (Function.prototype.bind.apply(Query, [null].concat([typeBundle], args)))();\n  } else {\n    return new (Function.prototype.bind.apply(Mutation, [null].concat([typeBundle], args)))();\n  }\n}\n\nfunction isInvalidOperationCombination(operations) {\n  if (operations.length === 1) {\n    return false;\n  }\n\n  return hasAnonymousOperations(operations) || hasDuplicateOperationNames(operations);\n}\n\nfunction fragmentNameIsNotUnique(existingDefinitions, name) {\n  return existingDefinitions.some(function (definition) {\n    return definition.name === name;\n  });\n}\n\nvar Document = function () {\n\n  /**\n   * This constructor should not be invoked directly.\n   * Use the factory function {@link Client#document} to create a Document.\n   * @param {Object} typeBundle A set of ES6 modules generated by {@link https://github.com/Shopify/graphql-js-schema|graphql-js-schema}.\n   */\n  function Document(typeBundle) {\n    classCallCheck(this, Document);\n\n    this.typeBundle = typeBundle;\n    this.definitions = [];\n  }\n\n  /**\n   * Returns the GraphQL query string for the Document (e.g. `query queryOne { ... } query queryTwo { ... }`).\n   *\n   * @return {String} The GraphQL query string for the Document.\n   */\n\n\n  createClass(Document, [{\n    key: 'toString',\n    value: function toString() {\n      return join(this.definitions);\n    }\n\n    /**\n     * Adds an operation to the Document.\n     *\n     * @private\n     * @param {String} operationType The type of the operation. Either 'query' or 'mutation'.\n     * @param {(Operation|String)} [query|queryName] Either an instance of an operation\n     *   object, or the name of an operation. Both are optional.\n     * @param {Object[]} [variables] A list of variables in the operation. See {@link Client#variable}.\n     * @param {Function} [callback] The query builder callback. If an operation\n     *   instance is passed, this callback will be ignored.\n     *   A {@link SelectionSet} is created using this callback.\n      */\n\n  }, {\n    key: 'addOperation',\n    value: function addOperation(operationType) {\n      for (var _len2 = arguments.length, args = Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      var operation = extractOperation.apply(undefined, [this.typeBundle, operationType].concat(args));\n\n      if (isInvalidOperationCombination(this.operations.concat(operation))) {\n        throw new Error('All operations must be uniquely named on a multi-operation document');\n      }\n\n      this.definitions.push(operation);\n    }\n\n    /**\n     * Adds a query to the Document.\n     *\n     * @example\n     * document.addQuery('myQuery', (root) => {\n     *   root.add('cat', (cat) => {\n     *    cat.add('name');\n     *   });\n     * });\n     *\n     * @param {(Query|String)} [query|queryName] Either an instance of a query\n     *   object, or the name of a query. Both are optional.\n     * @param {Object[]} [variables] A list of variables in the query. See {@link Client#variable}.\n     * @param {Function} [callback] The query builder callback. If a query\n     *   instance is passed, this callback will be ignored.\n     *   A {@link SelectionSet} is created using this callback.\n     */\n\n  }, {\n    key: 'addQuery',\n    value: function addQuery() {\n      for (var _len3 = arguments.length, args = Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        args[_key3] = arguments[_key3];\n      }\n\n      this.addOperation.apply(this, ['query'].concat(args));\n    }\n\n    /**\n     * Adds a mutation to the Document.\n     *\n     * @example\n     * const input = client.variable('input', 'CatCreateInput!');\n     *\n     * document.addMutation('myMutation', [input], (root) => {\n     *   root.add('catCreate', {args: {input}}, (catCreate) => {\n     *     catCreate.add('cat', (cat) => {\n     *       cat.add('name');\n     *     });\n     *   });\n     * });\n     *\n     * @param {(Mutation|String)} [mutation|mutationName] Either an instance of a mutation\n     *   object, or the name of a mutation. Both are optional.\n     * @param {Object[]} [variables] A list of variables in the mutation. See {@link Client#variable}.\n     * @param {Function} [callback] The mutation builder callback. If a mutation\n     *   instance is passed, this callback will be ignored.\n     *   A {@link SelectionSet} is created using this callback.\n     */\n\n  }, {\n    key: 'addMutation',\n    value: function addMutation() {\n      for (var _len4 = arguments.length, args = Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n        args[_key4] = arguments[_key4];\n      }\n\n      this.addOperation.apply(this, ['mutation'].concat(args));\n    }\n\n    /**\n     * Defines a fragment on the Document.\n     *\n     * @param {String} name The name of the fragment.\n     * @param {String} onType The type the fragment is on.\n     * @param {Function} [builderFunction] The query builder callback.\n     *   A {@link SelectionSet} is created using this callback.\n     * @return {FragmentSpread} A {@link FragmentSpread} to be used with {@link SelectionSetBuilder#addFragment}.\n     */\n\n  }, {\n    key: 'defineFragment',\n    value: function defineFragment(name, onType, builderFunction) {\n      if (fragmentNameIsNotUnique(this.fragmentDefinitions, name)) {\n        throw new Error('All fragments must be uniquely named on a multi-fragment document');\n      }\n\n      var selectionSet = new SelectionSet(this.typeBundle, onType, builderFunction);\n      var fragment = new FragmentDefinition(name, onType, selectionSet);\n\n      this.definitions.push(fragment);\n\n      return fragment.spread;\n    }\n\n    /**\n     * All operations ({@link Query} and {@link Mutation}) on the Document.\n     */\n\n  }, {\n    key: 'operations',\n    get: function get$$1() {\n      return this.definitions.filter(function (definition) {\n        return Operation.prototype.isPrototypeOf(definition);\n      });\n    }\n\n    /**\n     * All {@link FragmentDefinition}s on the Document.\n     */\n\n  }, {\n    key: 'fragmentDefinitions',\n    get: function get$$1() {\n      return this.definitions.filter(function (definition) {\n        return FragmentDefinition.prototype.isPrototypeOf(definition);\n      });\n    }\n  }]);\n  return Document;\n}();\n\n/**\n * The base class used when deserializing response data.\n * Provides rich features, like functions to generate queries to refetch a node or fetch the next page.\n *\n * @class\n */\nvar GraphModel =\n\n/**\n * @param {Object} attrs Attributes on the GraphModel.\n */\nfunction GraphModel(attrs) {\n  var _this = this;\n\n  classCallCheck(this, GraphModel);\n\n  Object.defineProperty(this, 'attrs', { value: attrs, enumerable: false });\n\n  Object.keys(this.attrs).filter(function (key) {\n    return !(key in _this);\n  }).forEach(function (key) {\n    var descriptor = void 0;\n\n    if (attrs[key] === null) {\n      descriptor = {\n        enumerable: true,\n        get: function get$$1() {\n          return null;\n        }\n      };\n    } else {\n      descriptor = {\n        enumerable: true,\n        get: function get$$1() {\n          return this.attrs[key].valueOf();\n        }\n      };\n    }\n    Object.defineProperty(_this, key, descriptor);\n  });\n};\n\n/**\n * A registry of classes used to deserialize the response data. Uses {@link GraphModel} by default.\n */\n\nvar ClassRegistry = function () {\n  function ClassRegistry() {\n    classCallCheck(this, ClassRegistry);\n\n    this.classStore = {};\n  }\n\n  /**\n   * Registers a class for a GraphQL type in the registry.\n   *\n   * @param {Class} constructor The constructor of the class.\n   * @param {String} type The GraphQL type of the object to deserialize into the class.\n   */\n\n\n  createClass(ClassRegistry, [{\n    key: 'registerClassForType',\n    value: function registerClassForType(constructor, type) {\n      this.classStore[type] = constructor;\n    }\n\n    /**\n     * Unregisters a class for a GraphQL type in the registry.\n     *\n     * @param {String} type The GraphQL type to unregister.\n     */\n\n  }, {\n    key: 'unregisterClassForType',\n    value: function unregisterClassForType(type) {\n      delete this.classStore[type];\n    }\n\n    /**\n     * Returns the class for the given GraphQL type.\n     *\n     * @param {String} type The GraphQL type to look up.\n     * @return {Class|GraphModel} The class for the given GraphQL type. Defaults to {@link GraphModel} if no class is registered for the GraphQL type.\n     */\n\n  }, {\n    key: 'classForType',\n    value: function classForType(type) {\n      return this.classStore[type] || GraphModel;\n    }\n  }]);\n  return ClassRegistry;\n}();\n\nfunction isValue(arg) {\n  return Object.prototype.toString.call(arg) !== '[object Null]' && Object.prototype.toString.call(arg) !== '[object Undefined]';\n}\n\nfunction isNodeContext(context) {\n  return context.selection.selectionSet.typeSchema.implementsNode;\n}\n\nfunction isConnection(context) {\n  return context.selection.selectionSet.typeSchema.name.endsWith('Connection');\n}\n\nfunction nearestNode(context) {\n  if (context == null) {\n    return null;\n  } else if (isNodeContext(context)) {\n    return context;\n  } else {\n    return nearestNode(context.parent);\n  }\n}\n\nfunction contextsFromRoot(context) {\n  if (context.parent) {\n    return contextsFromRoot(context.parent).concat(context);\n  } else {\n    return [context];\n  }\n}\n\nfunction contextsFromNearestNode(context) {\n  if (context.selection.selectionSet.typeSchema.implementsNode) {\n    return [context];\n  } else {\n    return contextsFromNearestNode(context.parent).concat(context);\n  }\n}\n\nfunction initializeDocumentAndVars(currentContext, contextChain) {\n  var lastInChain = contextChain[contextChain.length - 1];\n  var first = lastInChain.selection.args.first;\n  var variableDefinitions = Object.keys(lastInChain.selection.args).filter(function (key) {\n    return isVariable(lastInChain.selection.args[key]);\n  }).map(function (key) {\n    return lastInChain.selection.args[key];\n  });\n\n  var firstVar = variableDefinitions.find(function (definition) {\n    return definition.name === 'first';\n  });\n\n  if (!firstVar) {\n    if (isVariable(first)) {\n      firstVar = first;\n    } else {\n      firstVar = variable('first', 'Int', first);\n      variableDefinitions.push(firstVar);\n    }\n  }\n\n  var document = new Document(currentContext.selection.selectionSet.typeBundle);\n\n  return [document, variableDefinitions, firstVar];\n}\n\nfunction addNextFieldTo(currentSelection, contextChain, path, cursor) {\n  // There are always at least two. When we start, it's the root context, and the first set\n  var nextContext = contextChain.shift();\n\n  path.push(nextContext.selection.responseKey);\n\n  if (contextChain.length) {\n    currentSelection.add(nextContext.selection.name, { alias: nextContext.selection.alias, args: nextContext.selection.args }, function (newSelection) {\n      addNextFieldTo(newSelection, contextChain, path, cursor);\n    });\n  } else {\n    var edgesField = nextContext.selection.selectionSet.selections.find(function (field) {\n      return field.name === 'edges';\n    });\n    var nodeField = edgesField.selectionSet.selections.find(function (field) {\n      return field.name === 'node';\n    });\n    var first = void 0;\n\n    if (isVariable(nextContext.selection.args.first)) {\n      first = nextContext.selection.args.first;\n    } else {\n      first = variable('first', 'Int', nextContext.selection.args.first);\n    }\n\n    var options = {\n      alias: nextContext.selection.alias,\n      args: Object.assign({}, nextContext.selection.args, { after: cursor, first: first })\n    };\n\n    currentSelection.addConnection(nextContext.selection.name, options, nodeField.selectionSet);\n  }\n}\n\nfunction collectFragments(selections) {\n  return selections.reduce(function (fragmentDefinitions, field) {\n    if (FragmentSpread.prototype.isPrototypeOf(field)) {\n      fragmentDefinitions.push(field.toDefinition());\n    }\n\n    fragmentDefinitions.push.apply(fragmentDefinitions, toConsumableArray(collectFragments(field.selectionSet.selections)));\n\n    return fragmentDefinitions;\n  }, []);\n}\n\nfunction nextPageQueryAndPath(context, cursor) {\n  var nearestNodeContext = nearestNode(context);\n\n  if (nearestNodeContext) {\n    return function () {\n      var _document$definitions;\n\n      var path = [];\n      var nodeType = nearestNodeContext.selection.selectionSet.typeSchema;\n      var nodeId = nearestNodeContext.responseData.id;\n      var contextChain = contextsFromNearestNode(context);\n\n      var _initializeDocumentAn = initializeDocumentAndVars(context, contextChain),\n          _initializeDocumentAn2 = slicedToArray(_initializeDocumentAn, 2),\n          document = _initializeDocumentAn2[0],\n          variableDefinitions = _initializeDocumentAn2[1];\n\n      document.addQuery(variableDefinitions, function (root) {\n        path.push('node');\n        root.add('node', { args: { id: nodeId } }, function (node) {\n          node.addInlineFragmentOn(nodeType.name, function (fragment) {\n            addNextFieldTo(fragment, contextChain.slice(1), path, cursor);\n          });\n        });\n      });\n\n      var fragments = collectFragments(document.operations[0].selectionSet.selections);\n\n      (_document$definitions = document.definitions).unshift.apply(_document$definitions, toConsumableArray(fragments));\n\n      return [document, path];\n    };\n  } else {\n    return function () {\n      var _document$definitions2;\n\n      var path = [];\n      var contextChain = contextsFromRoot(context);\n\n      var _initializeDocumentAn3 = initializeDocumentAndVars(context, contextChain),\n          _initializeDocumentAn4 = slicedToArray(_initializeDocumentAn3, 2),\n          document = _initializeDocumentAn4[0],\n          variableDefinitions = _initializeDocumentAn4[1];\n\n      document.addQuery(variableDefinitions, function (root) {\n        addNextFieldTo(root, contextChain.slice(1), path, cursor);\n      });\n\n      var fragments = collectFragments(document.operations[0].selectionSet.selections);\n\n      (_document$definitions2 = document.definitions).unshift.apply(_document$definitions2, toConsumableArray(fragments));\n\n      return [document, path];\n    };\n  }\n}\n\nfunction hasNextPage$1(connection, edge) {\n  if (edge !== connection.edges[connection.edges.length - 1]) {\n    return new Scalar(true);\n  }\n\n  return connection.pageInfo.hasNextPage;\n}\n\nfunction hasPreviousPage(connection, edge) {\n  if (edge !== connection.edges[0]) {\n    return new Scalar(true);\n  }\n\n  return connection.pageInfo.hasPreviousPage;\n}\n\nfunction transformConnections(variableValues) {\n  return function (context, value) {\n    if (isConnection(context)) {\n      if (!(value.pageInfo && value.pageInfo.hasOwnProperty('hasNextPage') && value.pageInfo.hasOwnProperty('hasPreviousPage'))) {\n        throw new Error('Connections must include the selections \"pageInfo { hasNextPage, hasPreviousPage }\".');\n      }\n\n      return value.edges.map(function (edge) {\n        return Object.assign(edge.node, {\n          nextPageQueryAndPath: nextPageQueryAndPath(context, edge.cursor),\n          hasNextPage: hasNextPage$1(value, edge),\n          hasPreviousPage: hasPreviousPage(value, edge),\n          variableValues: variableValues\n        });\n      });\n    } else {\n      return value;\n    }\n  };\n}\n\n/* eslint-disable no-warning-comments */\nvar DecodingContext = function () {\n  function DecodingContext(selection, responseData) {\n    var parent = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n    classCallCheck(this, DecodingContext);\n\n    this.selection = selection;\n    this.responseData = responseData;\n    this.parent = parent;\n    Object.freeze(this);\n  }\n\n  createClass(DecodingContext, [{\n    key: 'contextForObjectProperty',\n    value: function contextForObjectProperty(responseKey) {\n      var nestedSelections = this.selection.selectionSet.selectionsByResponseKey[responseKey];\n      var nextSelection = nestedSelections && nestedSelections[0];\n      var nextContext = void 0;\n\n      // fragment spreads operate inside the current context, so we recurse to get the proper\n      // selection set, but retain the current response context\n      if (Spread.prototype.isPrototypeOf(nextSelection)) {\n        nextContext = new DecodingContext(nextSelection, this.responseData, this.parent);\n      } else {\n        nextContext = new DecodingContext(nextSelection, this.responseData[responseKey], this);\n      }\n\n      if (!nextSelection) {\n        throw new Error('Unexpected response key \"' + responseKey + '\", not found in selection set: ' + this.selection.selectionSet);\n      }\n\n      if (Field.prototype.isPrototypeOf(nextSelection)) {\n        return nextContext;\n      } else {\n        return nextContext.contextForObjectProperty(responseKey);\n      }\n    }\n  }, {\n    key: 'contextForArrayItem',\n    value: function contextForArrayItem(item) {\n      return new DecodingContext(this.selection, item, this.parent);\n    }\n  }]);\n  return DecodingContext;\n}();\n\nfunction decodeArrayItems(context, transformers) {\n  return context.responseData.map(function (item) {\n    return decodeContext(context.contextForArrayItem(item), transformers);\n  });\n}\n\nfunction decodeObjectValues(context, transformers) {\n  return Object.keys(context.responseData).reduce(function (acc, responseKey) {\n    acc[responseKey] = decodeContext(context.contextForObjectProperty(responseKey), transformers);\n\n    return acc;\n  }, {});\n}\n\nfunction runTransformers(transformers, context, value) {\n  return transformers.reduce(function (acc, transformer) {\n    return transformer(context, acc);\n  }, value);\n}\n\nfunction decodeContext(context, transformers) {\n  var value = context.responseData;\n\n  if (Array.isArray(value)) {\n    value = decodeArrayItems(context, transformers);\n  } else if (isObject(value)) {\n    value = decodeObjectValues(context, transformers);\n  }\n\n  return runTransformers(transformers, context, value);\n}\n\nfunction generateRefetchQueries(context, value) {\n  if (isValue(value) && isNodeContext(context)) {\n    value.refetchQuery = function () {\n      return new Query(context.selection.selectionSet.typeBundle, function (root) {\n        root.add('node', { args: { id: context.responseData.id } }, function (node) {\n          node.addInlineFragmentOn(context.selection.selectionSet.typeSchema.name, context.selection.selectionSet);\n        });\n      });\n    };\n  }\n\n  return value;\n}\n\nfunction transformPojosToClassesWithRegistry(classRegistry) {\n  return function transformPojosToClasses(context, value) {\n    if (isObject(value)) {\n      var Klass = classRegistry.classForType(context.selection.selectionSet.typeSchema.name);\n\n      return new Klass(value);\n    } else {\n      return value;\n    }\n  };\n}\n\nfunction transformScalars(context, value) {\n  if (isValue(value)) {\n    if (context.selection.selectionSet.typeSchema.kind === 'SCALAR') {\n      return new Scalar(value);\n    } else if (context.selection.selectionSet.typeSchema.kind === 'ENUM') {\n      return new Enum(value);\n    }\n  }\n\n  return value;\n}\n\nfunction recordTypeInformation(context, value) {\n  var _context$selection$se = context.selection.selectionSet,\n      typeBundle = _context$selection$se.typeBundle,\n      typeSchema = _context$selection$se.typeSchema;\n\n\n  if (isValue(value)) {\n    if (value.__typename) {\n      value.type = schemaForType(typeBundle, value.__typename, typeSchema);\n    } else {\n      value.type = typeSchema;\n    }\n  }\n\n  return value;\n}\n\nfunction defaultTransformers(_ref) {\n  var _ref$classRegistry = _ref.classRegistry,\n      classRegistry = _ref$classRegistry === undefined ? new ClassRegistry() : _ref$classRegistry,\n      variableValues = _ref.variableValues;\n\n  return [transformScalars, generateRefetchQueries, transformConnections(variableValues), recordTypeInformation, transformPojosToClassesWithRegistry(classRegistry)];\n}\n\n/**\n * A function used to decode the response data.\n *\n * @function decode\n * @param {SelectionSet} selection The selection set used to query the response data.\n * @param {Object} responseData The response data returned.\n * @param {Object} [options] Options to use when decoding including:\n *   @param {ClassRegistry} [options.classRegistry] A class registry to use when deserializing the data into classes.\n * @return {GraphModel} The decoded response data.\n */\nfunction decode(selection, responseData) {\n  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n\n  var transformers = options.transformers || defaultTransformers(options);\n  var context = new DecodingContext(selection, responseData);\n\n  return decodeContext(context, transformers);\n}\n\nfunction httpFetcher(url) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n  return function fetcher(graphQLParams, headers) {\n    return fetch(url, _extends({\n      body: JSON.stringify(graphQLParams),\n      method: 'POST',\n      mode: 'cors'\n    }, options, {\n      headers: _extends({\n        'Content-Type': 'application/json',\n        Accept: 'application/json'\n      }, options.headers, headers)\n    })).then(function (response) {\n      var contentType = response.headers.get('content-type');\n\n      if (contentType.indexOf('application/json') > -1) {\n        return response.json();\n      }\n\n      return response.text().then(function (text) {\n        return { text: text };\n      });\n    });\n  };\n}\n\nfunction hasNextPage(paginatedModels) {\n  return paginatedModels && paginatedModels.length && paginatedModels[paginatedModels.length - 1].hasNextPage;\n}\n\n/**\n * The Client class used to create and send GraphQL documents, fragments, queries and mutations.\n */\n\nvar Client = function () {\n\n  /**\n   * @param {Object} typeBundle A set of ES6 modules generated by {@link https://github.com/Shopify/graphql-js-schema|graphql-js-schema}.\n   * @param {Object} options An options object. Must include either `url` and optional `fetcherOptions` OR a `fetcher` function.\n   *   @param {(String|Function)} options.url|fetcher Either the URL of the GraphQL API endpoint, or a custom fetcher function for further customization.\n   *   @param {Object} [options.fetcherOptions] Additional options to use with `fetch`, like headers. Do not specify this argument if `fetcher` is specified.\n   *   @param {ClassRegistry} [options.registry=new ClassRegistry()] A {@link ClassRegistry} used to decode the response data.\n   */\n  function Client(typeBundle, _ref) {\n    var url = _ref.url,\n        fetcherOptions = _ref.fetcherOptions,\n        fetcher = _ref.fetcher,\n        _ref$registry = _ref.registry,\n        registry = _ref$registry === undefined ? new ClassRegistry() : _ref$registry;\n    classCallCheck(this, Client);\n\n    this.typeBundle = typeBundle;\n    this.classRegistry = registry;\n\n    if (url && fetcher) {\n      throw new Error('Arguments not supported: supply either `url` and optional `fetcherOptions` OR use a `fetcher` function for further customization.');\n    }\n\n    if (url) {\n      this.fetcher = httpFetcher(url, fetcherOptions);\n    } else if (fetcher) {\n      if (fetcherOptions) {\n        throw new Error('Arguments not supported: when specifying your own `fetcher`, set options through it and not with `fetcherOptions`');\n      }\n\n      this.fetcher = fetcher;\n    } else {\n      throw new Error('Invalid arguments: one of `url` or `fetcher` is needed.');\n    }\n  }\n\n  /**\n   * Creates a GraphQL document.\n   *\n   * @example\n   * const document = client.document();\n   *\n   * @return {Document} A GraphQL document.\n   */\n\n\n  createClass(Client, [{\n    key: 'document',\n    value: function document() {\n      return new Document(this.typeBundle);\n    }\n\n    /**\n     * Creates a GraphQL query.\n     *\n     * @example\n     * const query = client.query('myQuery', (root) => {\n     *   root.add('cat', (cat) => {\n     *    cat.add('name');\n     *   });\n     * });\n     *\n     * @param {String} [name] A name for the query.\n     * @param {VariableDefinition[]} [variables] A list of variables in the query. See {@link Client#variable}.\n     * @param {Function} selectionSetCallback The query builder callback.\n     *   A {@link SelectionSet} is created using this callback.\n     * @return {Query} A GraphQL query.\n     */\n\n  }, {\n    key: 'query',\n    value: function query() {\n      for (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n\n      return new (Function.prototype.bind.apply(Query, [null].concat([this.typeBundle], args)))();\n    }\n\n    /**\n     * Creates a GraphQL mutation.\n     *\n     * @example\n     * const input = client.variable('input', 'CatCreateInput!');\n     *\n     * const mutation = client.mutation('myMutation', [input], (root) => {\n     *   root.add('catCreate', {args: {input}}, (catCreate) => {\n     *     catCreate.add('cat', (cat) => {\n     *       cat.add('name');\n     *     });\n     *   });\n     * });\n     *\n     * @param {String} [name] A name for the mutation.\n     * @param {VariableDefinition[]} [variables] A list of variables in the mutation. See {@link Client#variable}.\n     * @param {Function} selectionSetCallback The mutation builder callback.\n     *   A {@link SelectionSet} is created using this callback.\n     * @return {Mutation} A GraphQL mutation.\n     */\n\n  }, {\n    key: 'mutation',\n    value: function mutation() {\n      for (var _len2 = arguments.length, args = Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n\n      return new (Function.prototype.bind.apply(Mutation, [null].concat([this.typeBundle], args)))();\n    }\n\n    /**\n     * Sends a GraphQL operation (query or mutation) or a document.\n     *\n     * @example\n     * client.send(query, {id: '12345'}).then((result) => {\n     *   // Do something with the returned result\n     *   console.log(result);\n     * });\n     *\n     * @param {(Query|Mutation|Document|Function)} request The operation or document to send. If represented\n     * as a function, it must return `Query`, `Mutation`, or `Document` and recieve the client as the only param.\n     * @param {Object} [variableValues] The values for variables in the operation or document.\n     * @param {Object} [otherProperties] Other properties to send with the query. For example, a custom operation name.\n     * @param {Object} [headers] Additional headers to be applied on a request by request basis.\n     * @return {Promise.<Object>} A promise resolving to an object containing the response data.\n     */\n\n  }, {\n    key: 'send',\n    value: function send(request) {\n      var variableValues = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n\n      var _this = this;\n\n      var otherProperties = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n      var headers = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;\n\n      var operationOrDocument = void 0;\n\n      if (Function.prototype.isPrototypeOf(request)) {\n        operationOrDocument = request(this);\n      } else {\n        operationOrDocument = request;\n      }\n\n      var graphQLParams = { query: operationOrDocument.toString() };\n\n      if (variableValues) {\n        graphQLParams.variables = variableValues;\n      }\n\n      Object.assign(graphQLParams, otherProperties);\n\n      var operation = void 0;\n\n      if (Operation.prototype.isPrototypeOf(operationOrDocument)) {\n        operation = operationOrDocument;\n      } else {\n        var document = operationOrDocument;\n\n        if (document.operations.length === 1) {\n          operation = document.operations[0];\n        } else if (otherProperties.operationName) {\n          operation = document.operations.find(function (documentOperation) {\n            return documentOperation.name === otherProperties.operationName;\n          });\n        } else {\n          throw new Error('\\n          A document must contain exactly one operation, or an operationName\\n          must be specified. Example:\\n\\n            client.send(document, null, {operationName: \\'myFancyQuery\\'});\\n        ');\n        }\n      }\n\n      return this.fetcher(graphQLParams, headers).then(function (response) {\n        if (response.data) {\n          response.model = decode(operation, response.data, {\n            classRegistry: _this.classRegistry,\n            variableValues: variableValues\n          });\n        }\n\n        return response;\n      });\n    }\n\n    /**\n     * Fetches the next page of a paginated node or array of nodes.\n     *\n     * @example\n     * client.fetchNextPage(node, {first: 10}).then((result) => {\n     *   // Do something with the next page\n     *   console.log(result);\n     * });\n     *\n     * @param {(GraphModel|GraphModel[])} nodeOrNodes The node or list of nodes on which to fetch the next page.\n     * @param {Object} [options] Options object containing:\n     *   @param {Integer} [options.first] The number of nodes to query on the next page. Defaults to the page size of the previous query.\n     * @return {Promise.<GraphModel[]>} A promise resolving with the next page of {@link GraphModel}s.\n     */\n\n  }, {\n    key: 'fetchNextPage',\n    value: function fetchNextPage(nodeOrNodes, options) {\n      var node = void 0;\n\n      if (Array.isArray(nodeOrNodes)) {\n        node = nodeOrNodes[nodeOrNodes.length - 1];\n      } else {\n        node = nodeOrNodes;\n      }\n\n      var _node$nextPageQueryAn = node.nextPageQueryAndPath(),\n          _node$nextPageQueryAn2 = slicedToArray(_node$nextPageQueryAn, 2),\n          query = _node$nextPageQueryAn2[0],\n          path = _node$nextPageQueryAn2[1];\n\n      var variableValues = void 0;\n\n      if (node.variableValues || options) {\n        variableValues = Object.assign({}, node.variableValues, options);\n      }\n\n      return this.send(query, variableValues).then(function (response) {\n        response.model = path.reduce(function (object, key) {\n          return object[key];\n        }, response.model);\n\n        return response;\n      });\n    }\n\n    /**\n     * Fetches all subsequent pages of a paginated array of nodes.\n     *\n     * @example\n     * client.fetchAllPages(nodes, {pageSize: 20}).then((result) => {\n     *   // Do something with all the models\n     *   console.log(result);\n     * });\n     *\n     * @param {GraphModel[]} paginatedModels The list of nodes on which to fetch all pages.\n     * @param {Object} options Options object containing:\n     *   @param {Integer} options.pageSize The number of nodes to query on each page.\n     * @return {Promise.<GraphModel[]>} A promise resolving with all pages of {@link GraphModel}s, including the original list.\n     */\n\n  }, {\n    key: 'fetchAllPages',\n    value: function fetchAllPages(paginatedModels, _ref2) {\n      var _this2 = this;\n\n      var pageSize = _ref2.pageSize;\n\n      if (hasNextPage(paginatedModels)) {\n        return this.fetchNextPage(paginatedModels, { first: pageSize }).then(function (_ref3) {\n          var model = _ref3.model;\n\n          var pages = paginatedModels.concat(model);\n\n          return _this2.fetchAllPages(pages, { pageSize: pageSize });\n        });\n      }\n\n      return Promise.resolve(paginatedModels);\n    }\n\n    /**\n     * Refetches a {@link GraphModel} whose type implements `Node`.\n     *\n     * @example\n     * client.refetch(node).then((result) => {\n     *   // Do something with the refetched node\n     *   console.log(result);\n     * });\n     *\n     * @param {GraphModel} nodeType A {@link GraphModel} whose type implements `Node`.\n     * @return {Promise.<GraphModel>} The refetched {@link GraphModel}.\n     */\n\n  }, {\n    key: 'refetch',\n    value: function refetch(nodeType) {\n      if (!nodeType) {\n        throw new Error('\\'client#refetch\\' must be called with a non-null instance of a Node.');\n      } else if (!nodeType.type.implementsNode) {\n        throw new Error('\\'client#refetch\\' must be called with a type that implements Node. Received ' + nodeType.type.name + '.');\n      }\n\n      return this.send(nodeType.refetchQuery()).then(function (_ref4) {\n        var model = _ref4.model;\n        return model.node;\n      });\n    }\n\n    /**\n     * Creates a variable to be used in a {@link Query} or {@link Mutation}.\n     *\n     * @example\n     * const idVariable = client.variable('id', 'ID!', '12345');\n     *\n     * @param {String} name The name of the variable.\n     * @param {String} type The GraphQL type of the variable.\n     * @param {*} [defaultValue] The default value of the variable.\n     * @return {VariableDefinition} A variable object that can be used in a {@link Query} or {@link Mutation}.\n     */\n\n  }, {\n    key: 'variable',\n    value: function variable$$1(name, type, defaultValue) {\n      return variable(name, type, defaultValue);\n    }\n\n    /**\n     * Creates an enum to be used in a {@link Query} or {@link Mutation}.\n     *\n     * @example\n     * const titleEnum = client.enum('TITLE');\n     *\n     * @param {String} key The key of the enum.\n     * @return {Enum} An enum object that can be used in a {@link Query} or {@link Mutation}.\n     */\n\n  }, {\n    key: 'enum',\n    value: function _enum(key) {\n      return enumFunction(key);\n    }\n  }]);\n  return Client;\n}();\n\nexport { ClassRegistry, GraphModel, decode };export default Client;\n//# sourceMappingURL=index.es.js.map\n", "/**\n * The class used to configure the JS Buy SDK Client.\n * @class\n */\nclass Config {\n\n  /**\n   * Properties that must be set on initializations\n   * @attribute requiredProperties\n   * @default ['storefrontAccessToken', 'domain']\n   * @type Array\n   * @private\n   */\n  get requiredProperties() {\n    return [\n      'storefrontAccessToken',\n      'domain'\n    ];\n  }\n\n  /**\n   * Deprecated properties that map directly to required properties\n   * @attribute deprecatedProperties\n   * @default {'accessToken': 'storefrontAccessToken', 'apiKey': 'storefrontAccessToken'}\n   * @type Object\n   * @private\n   */\n  get deprecatedProperties() {\n    return {\n      accessToken: 'storefrontAccessToken',\n      apiKey: 'storefrontAccessToken'\n    };\n  }\n\n  /**\n   * @constructs Config\n   * @param {Object} attrs An object specifying the configuration. Requires the following properties:\n   *   @param {String} attrs.storefrontAccessToken The {@link https://help.shopify.com/api/reference/storefront_access_token|Storefront access token} for the shop.\n   *   @param {String} attrs.domain The `myshopify` domain for the shop (e.g. `graphql.myshopify.com`).\n   */\n  constructor(attrs) {\n    Object.keys(this.deprecatedProperties).forEach((key) => {\n      if (!attrs.hasOwnProperty(key)) { return; }\n      // eslint-disable-next-line no-console\n      console.warn(`[ShopifyBuy] Config property ${key} is deprecated as of v1.0, please use ${this.deprecatedProperties[key]} instead.`);\n      attrs[this.deprecatedProperties[key]] = attrs[key];\n    });\n\n    this.requiredProperties.forEach((key) => {\n      if (attrs.hasOwnProperty(key)) {\n        this[key] = attrs[key];\n      } else {\n        throw new Error(`new Config() requires the option '${key}'`);\n      }\n    });\n\n    if (attrs.hasOwnProperty('apiVersion')) {\n      this.apiVersion = attrs.apiVersion;\n    } else {\n      this.apiVersion = '2024-04';\n    }\n\n    if (attrs.hasOwnProperty('source')) {\n      this.source = attrs.source;\n    }\n\n    if (attrs.hasOwnProperty('language')) {\n      this.language = attrs.language;\n    }\n  }\n}\n\nexport default Config;\n", "export default class InputMapper {\n  create(input = {}) {\n    const cartInput = {};\n\n    if (input.presentmentCurrencyCode) {\n      // eslint-disable-next-line no-console\n      console.warn('presentmentCurrencyCode is not supported by the Cart API');\n    }\n\n    // SDK checkout input fields we can map:\n    if (input.lineItems && input.lineItems.length) {\n      cartInput.lines = input.lineItems.map((lineItem) => {\n        lineItem.merchandiseId = lineItem.variantId;\n        delete lineItem.variantId;\n\n        return lineItem;\n      });\n    }\n\n    if (input.note) {\n      cartInput.note = input.note;\n    }\n\n    if (input.email) {\n      cartInput.buyerIdentity = {email: input.email};\n    }\n\n    if (input.shippingAddress) {\n      if (!cartInput.buyerIdentity) {\n        cartInput.buyerIdentity = {};\n      }\n      cartInput.buyerIdentity.deliveryAddressPreferences = [\n        {deliveryAddress: input.shippingAddress}\n      ];\n    }\n\n    if (input.customAttributes) {\n      cartInput.attributes = input.customAttributes;\n    }\n\n    // Fields that aren't documented in SDK but could still be passed in:\n    if (input.buyerIdentity) {\n      if (!cartInput.buyerIdentity) {\n        cartInput.buyerIdentity = {};\n      }\n      cartInput.buyerIdentity.countryCode = input.buyerIdentity.countryCode;\n    }\n\n    if (input.allowPartialAddresses) {\n      // eslint-disable-next-line no-console\n      console.warn('allowPartialAddresses is not supported by the Cart API');\n    }\n\n    return cartInput;\n  }\n\n  updateAttributes(checkoutId, input) {\n    const cartAttributesUpdateInput = {\n      attributes: [],\n      cartId: ''\n    };\n\n    const cartNoteUpdateInput = {\n      cartId: '',\n      note: ''\n    };\n\n    if (checkoutId) {\n      cartAttributesUpdateInput.cartId = checkoutId;\n      cartNoteUpdateInput.cartId = checkoutId;\n    }\n\n    if (input.customAttributes) {\n      cartAttributesUpdateInput.attributes = input.customAttributes;\n    }\n\n    if (input.note) {\n      cartNoteUpdateInput.note = input.note;\n    }\n\n    if (input.allowPartialAddresses) {\n      // eslint-disable-next-line no-console\n      console.warn('allowPartialAddresses is not supported by the Cart API');\n    }\n\n    // With cart, we will need to execute two separate mutations (one for attributes and one for note)\n    return {cartAttributesUpdateInput, cartNoteUpdateInput};\n  }\n\n  updateEmail(checkoutId, email) {\n    const cartBuyerIdentityInput = {\n      buyerIdentity: {email},\n      cartId: checkoutId\n    };\n\n    return cartBuyerIdentityInput;\n  }\n\n  addLineItems(checkoutId, lineItems) {\n    const lines = Array.isArray(lineItems) ? lineItems : [lineItems];\n\n    return {\n      cartId: checkoutId,\n      lines: lines.map(mapLineItemToLine).filter(Boolean)\n    };\n  }\n\n  addDiscount(checkoutId, discountCodes) {\n    return {\n      cartId: checkoutId,\n      discountCodes: Array.isArray(discountCodes) ? discountCodes.flat() : []\n    };\n  }\n\n  removeDiscount(checkoutId) {\n    return {\n      cartId: checkoutId,\n      discountCodes: []\n    };\n  }\n\n  addGiftCards(checkoutId, giftCardCodes) {\n    return {\n      cartId: checkoutId,\n      giftCardCodes: giftCardCodes || []\n    };\n  }\n\n  removeGiftCard(checkoutId, appliedGiftCardId) {\n    return {\n      cartId: checkoutId,\n      appliedGiftCardIds: appliedGiftCardId ? [appliedGiftCardId] : []\n    };\n  }\n\n  removeLineItems(checkoutId, lineItemIds) {\n    const lineIds = Array.isArray(lineItemIds) ? lineItemIds : [lineItemIds];\n\n    return {\n      cartId: checkoutId,\n      lineIds\n    };\n  }\n\n  replaceLineItems(checkoutId, lineItems) {\n    const lines = Array.isArray(lineItems) ? lineItems : [lineItems];\n\n    return {\n      cartId: checkoutId,\n      lines: lines.map(mapLineItemToLine).filter(Boolean)\n    };\n  }\n\n  updateLineItems(checkoutId, lineItems) {\n    const lines = Array.isArray(lineItems) ? lineItems : [lineItems];\n\n    return {\n      cartId: checkoutId,\n      lines: lines.map(mapLineItemToLine).filter(Boolean)\n    };\n  }\n\n  updateShippingAddress(checkoutId, shippingAddress) {\n    const deliveryAddress = {};\n\n    if (shippingAddress.address1) {\n      deliveryAddress.address1 = shippingAddress.address1;\n    }\n\n    if (shippingAddress.address2) {\n      deliveryAddress.address2 = shippingAddress.address2;\n    }\n\n    if (shippingAddress.city) {\n      deliveryAddress.city = shippingAddress.city;\n    }\n\n    if (shippingAddress.company) {\n      deliveryAddress.company = shippingAddress.company;\n    }\n\n    if (shippingAddress.country) {\n      deliveryAddress.country = shippingAddress.country;\n    }\n\n    if (shippingAddress.firstName) {\n      deliveryAddress.firstName = shippingAddress.firstName;\n    }\n\n    if (shippingAddress.lastName) {\n      deliveryAddress.lastName = shippingAddress.lastName;\n    }\n\n    if (shippingAddress.phone) {\n      deliveryAddress.phone = shippingAddress.phone;\n    }\n\n    if (shippingAddress.zip) {\n      deliveryAddress.zip = shippingAddress.zip;\n    }\n\n    if (shippingAddress.province) {\n      deliveryAddress.province = shippingAddress.province;\n    }\n\n    const withDeliveryAddress =\n      deliveryAddress && (Object.keys(deliveryAddress).length > 0);\n\n    return {\n      cartId: checkoutId,\n      buyerIdentity: {\n        deliveryAddressPreferences: withDeliveryAddress\n          ? [{deliveryAddress}]\n          : []\n      }\n    };\n  }\n}\n\nfunction mapLineItemToLine(lineItem) {\n  const line = {};\n\n  if (typeof lineItem.id !== 'undefined') {\n    line.id = lineItem.id;\n  }\n\n  if (typeof lineItem.customAttributes !== 'undefined') {\n    line.attributes = lineItem.customAttributes;\n  }\n\n  if (typeof lineItem.quantity !== 'undefined') {\n    line.quantity = lineItem.quantity;\n  }\n\n  if (typeof lineItem.variantId !== 'undefined') {\n    line.merchandiseId = lineItem.variantId;\n  }\n\n  if (Object.keys(line).length === 0) {\n    return null;\n  }\n\n  return line;\n}\n", "import InputMapper from './input-map-resource';\n\nexport default class Resource {\n  constructor(client) {\n    this.graphQLClient = client;\n    this.inputMapper = new InputMapper();\n  }\n}\n", "export const defaultErrors = [{message: 'an unknown error has occurred.'}];\n\nexport default function defaultResolver(path) {\n  const keys = path.split('.');\n\n  return function({model, errors}) {\n    return new Promise((resolve, reject) => {\n      try {\n        const result = keys.reduce((ref, key) => {\n          return ref[key];\n        }, model);\n\n        resolve(result);\n      } catch (_) {\n        if (errors) {\n          reject(errors);\n        } else {\n          reject(defaultErrors);\n        }\n      }\n    });\n  };\n}\n", "export default function fetchResourcesForProducts(productOrProduct, client) {\n  const products = [].concat(productOrProduct);\n\n  return Promise.all(products.reduce((promiseAcc, product) => {\n\n    // If the graphql query doesn't find a match, skip fetching variants and images.\n    if (product === null) {\n      return promiseAcc;\n    }\n\n    // Fetch the rest of the images and variants for this product\n    promiseAcc.push(client.fetchAllPages(product.images, {pageSize: 250}).then((images) => {\n      product.attrs.images = images;\n    }));\n\n    promiseAcc.push(client.fetchAllPages(product.variants, {pageSize: 250}).then((variants) => {\n      product.attrs.variants = variants;\n    }));\n\n    return promiseAcc;\n  }, []));\n}\n", "import fetchResourcesForProducts from './fetch-resources-for-products';\n\nexport function paginateProductConnectionsAndResolve(client) {\n  return function(products) {\n    return fetchResourcesForProducts(products, client).then(() => {\n      return products;\n    });\n  };\n}\n\nexport function paginateCollectionsProductConnectionsAndResolve(client) {\n  return function(collectionOrCollections) {\n    const collections = [].concat(collectionOrCollections);\n\n    return Promise.all(collections.reduce((promiseAcc, collection) => {\n      return promiseAcc.concat(fetchResourcesForProducts(collection.products, client));\n    }, [])).then(() => {\n      return collectionOrCollections;\n    });\n  };\n}\n", "/**\n * @namespace ProductHelpers\n */\nexport default {\n\n  /**\n   * Returns the variant of a product corresponding to the options given.\n   *\n   * @example\n   * const selectedVariant = client.product.helpers.variantForOptions(product, {\n   *   size: \"Small\",\n   *   color: \"Red\"\n   * });\n   *\n   * @memberof ProductHelpers\n   * @method variantForOptions\n   * @param {GraphModel} product The product to find the variant on. Must include `variants`.\n   * @param {Object} options An object containing the options for the variant.\n   * @return {GraphModel} The variant corresponding to the options given.\n   */\n  variantForOptions(product, options) {\n    return product.variants.find((variant) => {\n      return variant.selectedOptions.every((selectedOption) => {\n        return options[selectedOption.name] === selectedOption.value.valueOf();\n      });\n    });\n  }\n};\n", "import Resource from './resource';\nimport defaultResolver from './default-resolver';\nimport {paginateProductConnectionsAndResolve} from './paginators';\nimport productHelpers from './product-helpers';\n\n// GraphQL\nimport productNodeQuery from './graphql/productNodeQuery.graphql';\nimport productNodesQuery from './graphql/productNodesQuery.graphql';\nimport productConnectionQuery from './graphql/productConnectionQuery.graphql';\nimport productByHandleQuery from './graphql/productByHandleQuery.graphql';\nimport productRecommendationsQuery from './graphql/productRecommendations.graphql';\n\n/**\n * The JS Buy SDK product resource\n * @class\n */\nclass ProductResource extends Resource {\n  get helpers() {\n    return productHelpers;\n  }\n\n  /**\n   * Fetches all products on the shop.\n   *\n   * @example\n   * client.product.fetchAll().then((products) => {\n   *   // Do something with the products\n   * });\n   *\n   * @param {Int} [pageSize] The number of products to fetch per page\n   * @return {Promise|GraphModel[]} A promise resolving with an array of `GraphModel`s of the products.\n   */\n  fetchAll(first = 20) {\n    return this.graphQLClient\n      .send(productConnectionQuery, {first})\n      .then(defaultResolver('products'))\n      .then(paginateProductConnectionsAndResolve(this.graphQLClient));\n  }\n\n  /**\n   * Fetches a single product by ID on the shop.\n   *\n   * @example\n   * client.product.fetch('Xk9lM2JkNzFmNzIQ4NTIY4ZDFi9DaGVja291dC9lM2JkN==').then((product) => {\n   *   // Do something with the product\n   * });\n   *\n   * @param {String} id The id of the product to fetch.\n   * @return {Promise|GraphModel} A promise resolving with a `GraphModel` of the product.\n   */\n  fetch(id) {\n    return this.graphQLClient\n      .send(productNodeQuery, {id})\n      .then(defaultResolver('node'))\n      .then(paginateProductConnectionsAndResolve(this.graphQLClient));\n  }\n\n  /**\n   * Fetches multiple products by ID on the shop.\n   *\n   * @example\n   * const ids = ['Xk9lM2JkNzFmNzIQ4NTIY4ZDFi9DaGVja291dC9lM2JkN==', 'Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0Lzc4NTc5ODkzODQ='];\n   * client.product.fetchMultiple(ids).then((products) => {\n   *   // Do something with the products\n   * });\n   *\n   * @param {String[]} ids The ids of the products to fetch\n   * @return {Promise|GraphModel[]} A promise resolving with a `GraphModel` of the product.\n   */\n  fetchMultiple(ids) {\n    return this.graphQLClient\n      .send(productNodesQuery, {ids})\n      .then(defaultResolver('nodes'))\n      .then(paginateProductConnectionsAndResolve(this.graphQLClient));\n  }\n\n  /**\n   * Fetches a single product by handle on the shop.\n   *\n   * @example\n   * client.product.fetchByHandle('my-product').then((product) => {\n   *   // Do something with the product\n   * });\n   *\n   * @param {String} handle The handle of the product to fetch.\n   * @return {Promise|GraphModel} A promise resolving with a `GraphModel` of the product.\n   */\n  fetchByHandle(handle) {\n    return this.graphQLClient\n      .send(productByHandleQuery, {handle})\n      .then(defaultResolver('productByHandle'))\n      .then(paginateProductConnectionsAndResolve(this.graphQLClient));\n  }\n\n  /**\n   * Fetches all products on the shop that match the query.\n   *\n   * @example\n   * client.product.fetchQuery({first: 20, sortKey: 'CREATED_AT', reverse: true}).then((products) => {\n   *   // Do something with the first 10 products sorted by title in ascending order\n   * });\n   *\n   * @param {Object} [args] An object specifying the query data containing zero or more of:\n   *   @param {Int} [args.first=20] The relay `first` param. This specifies page size.\n   *   @param {String} [args.sortKey=ID] The key to sort results by. Available values are\n   *   documented as {@link https://help.shopify.com/api/storefront-api/reference/enum/productsortkeys|Product Sort Keys}.\n   *   @param {String} [args.query] A query string. See full documentation {@link https://help.shopify.com/api/storefront-api/reference/object/shop#products|here}\n   *   @param {Boolean} [args.reverse] Whether or not to reverse the sort order of the results\n   * @return {Promise|GraphModel[]} A promise resolving with an array of `GraphModel`s of the products.\n   */\n  fetchQuery({first = 20, sortKey = 'ID', query, reverse} = {}) {\n    return this.graphQLClient\n      .send(productConnectionQuery, {\n        first,\n        sortKey,\n        query,\n        reverse\n      })\n      .then(defaultResolver('products'))\n      .then(paginateProductConnectionsAndResolve(this.graphQLClient));\n  }\n\n  /**\n   * Find recommended products related to a given productId.\n   * To learn more about how recommendations are generated, see https://shopify.dev/themes/product-merchandising/recommendations.\n   *\n   * @example\n   * const productId 'Xk9lM2JkNzFmNzIQ4NTIY4ZDFi9DaGVja291dC9lM2JkN==';\n   * client.product.fetchProductRecommendations(productId).then((products) => {\n   *   // Do something with the products\n   * });\n   *\n   * @param {String} productId The id of the product to fetch.\n   * @return {Promise|GraphModel[]} A promise resolving with an array of `GraphModel`s of the products.\n   */\n  fetchRecommendations(productId) {\n    return this.graphQLClient\n      .send(productRecommendationsQuery, {productId})\n      .then(defaultResolver('productRecommendations'))\n      .then(paginateProductConnectionsAndResolve(this.graphQLClient));\n  }\n}\n\nexport default ProductResource;\n", "import Resource from './resource';\nimport defaultResolver from './default-resolver';\nimport {paginateCollectionsProductConnectionsAndResolve} from './paginators';\n\n// GraphQL\nimport collectionNodeQuery from './graphql/collectionNodeQuery.graphql';\nimport collectionNodeWithProductsQuery from './graphql/collectionNodeWithProductsQuery.graphql';\nimport collectionConnectionQuery from './graphql/collectionConnectionQuery.graphql';\nimport collectionConnectionWithProductsQuery from './graphql/collectionConnectionWithProductsQuery.graphql';\nimport collectionByHandleQuery from './graphql/collectionByHandleQuery.graphql';\n\n/**\n * The JS Buy SDK collection resource\n * @class\n */\nclass CollectionResource extends Resource {\n\n  /**\n   * Fetches all collections on the shop, not including products.\n   * To fetch collections with products use [fetchAllsWithProducts]{@link Client#fetchAllsWithProducts}.\n   *\n   * @example\n   * client.collection.fetchAll().then((collections) => {\n   *   // Do something with the collections\n   * });\n   *\n   * @return {Promise|GraphModel[]} A promise resolving with an array of `GraphModel`s of the collections.\n   */\n  fetchAll(first = 20) {\n    return this.graphQLClient\n      .send(collectionConnectionQuery, {first})\n      .then(defaultResolver('collections'));\n  }\n\n  /**\n   * Fetches all collections on the shop, including products.\n   *\n   * @example\n   * client.collection.fetchAllWithProducts().then((collections) => {\n   *   // Do something with the collections\n   * });\n   *\n   * @return {Promise|GraphModel[]} A promise resolving with an array of `GraphModel`s of the collections.\n   */\n  fetchAllWithProducts({first = 20, productsFirst = 20} = {}) {\n    return this.graphQLClient\n      .send(collectionConnectionWithProductsQuery, {first, productsFirst})\n      .then(defaultResolver('collections'))\n      .then(paginateCollectionsProductConnectionsAndResolve(this.graphQLClient));\n  }\n\n  /**\n   * Fetches a single collection by ID on the shop, not including products.\n   * To fetch the collection with products use [fetchWithProducts]{@link Client#fetchWithProducts}.\n   *\n   * @example\n   * client.collection.fetch('Xk9lM2JkNzFmNzIQ4NTIY4ZDFiZTUyZTUwNTE2MDNhZjg==').then((collection) => {\n   *   // Do something with the collection\n   * });\n   *\n   * @param {String} id The id of the collection to fetch.\n   * @return {Promise|GraphModel} A promise resolving with a `GraphModel` of the collection.\n   */\n  fetch(id) {\n    return this.graphQLClient\n      .send(collectionNodeQuery, {id})\n      .then(defaultResolver('node'));\n  }\n\n  /**\n   * Fetches a single collection by ID on the shop, including products.\n   *\n   * @example\n   * client.collection.fetchWithProducts('Xk9lM2JkNzFmNzIQ4NTIY4ZDFiZTUyZTUwNTE2MDNhZjg==').then((collection) => {\n   *   // Do something with the collection\n   * });\n   *\n   * @param {String} id The id of the collection to fetch.\n   * @return {Promise|GraphModel} A promise resolving with a `GraphModel` of the collection.\n   */\n  fetchWithProducts(id, {productsFirst = 20} = {}) {\n    return this.graphQLClient\n      .send(collectionNodeWithProductsQuery, {id, productsFirst})\n      .then(defaultResolver('node'))\n      .then(paginateCollectionsProductConnectionsAndResolve(this.graphQLClient));\n  }\n\n  /**\n   * Fetches a collection by handle on the shop.\n   *\n   * @example\n   * client.collection.fetchByHandle('my-collection').then((collection) => {\n   *   // Do something with the collection\n   * });\n   *\n   * @param {String} handle The handle of the collection to fetch.\n   * @return {Promise|GraphModel} A promise resolving with a `GraphModel` of the collection.\n   */\n  fetchByHandle(handle) {\n    return this.graphQLClient\n      .send(collectionByHandleQuery, {handle})\n      .then(defaultResolver('collectionByHandle'));\n  }\n\n  /**\n   * Fetches all collections on the shop that match the query.\n   *\n   * @example\n   * client.collection.fetchQuery({first: 20, sortKey: 'CREATED_AT', reverse: true}).then((collections) => {\n   *   // Do something with the first 10 collections sorted by title in ascending order\n   * });\n   *\n   * @param {Object} [args] An object specifying the query data containing zero or more of:\n   *   @param {Int} [args.first=20] The relay `first` param. This specifies page size.\n   *   @param {String} [args.sortKey=ID] The key to sort results by. Available values are\n   *   documented as {@link https://help.shopify.com/api/storefront-api/reference/enum/collectionsortkeys|Collection Sort Keys}.\n   *   @param {String} [args.query] A query string. See full documentation {@link https://help.shopify.com/api/storefront-api/reference/object/shop#collections|here}\n   *   @param {Boolean} [args.reverse] Whether or not to reverse the sort order of the results\n   * @return {Promise|GraphModel[]} A promise resolving with an array of `GraphModel`s of the collections.\n   */\n  fetchQuery({first = 20, sortKey = 'ID', query, reverse} = {}) {\n    return this.graphQLClient.send(collectionConnectionQuery, {\n      first,\n      sortKey,\n      query,\n      reverse\n    }).then(defaultResolver('collections'));\n  }\n}\nexport default CollectionResource;\n", "import Resource from './resource';\nimport defaultResolver from './default-resolver';\n\n// GraphQL\nimport shopQuery from './graphql/shopQuery.graphql';\nimport shopPolicyQuery from './graphql/shopPolicyQuery.graphql';\n\n/**\n * The JS Buy SDK shop resource\n * @class\n */\nclass ShopResource extends Resource {\n\n  /**\n   * Fetches shop information (`currencyCode`, `description`, `moneyFormat`, `name`, and `primaryDomain`).\n   * See the {@link https://help.shopify.com/api/storefront-api/reference/object/shop|Storefront API reference} for more information.\n   *\n   * @example\n   * client.shop.fetchInfo().then((shop) => {\n   *   // Do something with the shop\n   * });\n   *\n   * @return {Promise|GraphModel} A promise resolving with a `GraphModel` of the shop.\n   */\n  fetchInfo() {\n    return this.graphQLClient\n      .send(shopQuery)\n      .then(defaultResolver('shop'));\n  }\n\n  /**\n   * Fetches shop policies (privacy policy, terms of service and refund policy).\n   *\n   * @example\n   * client.shop.fetchPolicies().then((shop) => {\n   *   // Do something with the shop\n   * });\n   *\n   * @return {Promise|GraphModel} A promise resolving with a `GraphModel` of the shop.\n   */\n  fetchPolicies() {\n    return this.graphQLClient\n      .send(shopPolicyQuery)\n      .then(defaultResolver('shop'));\n  }\n}\n\nexport default ShopResource;\n", "export function getDiscountAllocationId(discountAllocation) {\n  const discountApp = discountAllocation.discountApplication;\n  const discountId = discountAllocation.code || discountAllocation.title || discountApp.code || discountApp.title;\n\n  if (!discountId) {\n    throw new Error(\n      `Discount allocation must have either code or title in discountApplication: ${JSON.stringify(\n        discountAllocation\n      )}`\n    );\n  }\n\n  return discountId;\n}\n\nexport function getDiscountApplicationId(discountApplication) {\n  const discountId = discountApplication.code || discountApplication.title;\n\n  if (!discountId) {\n    throw new Error(\n      `Discount application must have either code or title: ${JSON.stringify(\n        discountApplication\n      )}`\n    );\n  }\n\n  return discountId;\n}\n\nfunction convertToCheckoutDiscountApplicationType(cartLineItems, cartOrderLevelDiscountAllocations) {\n  // For each discount allocation, move the code/title field to be inside the discountApplication field\n  // This is because the code/title field is part of the discount allocation for a Cart, but part of\n  // the discount application for a checkout\n  for (let i = 0; i < cartLineItems.length; i++) {\n    const {discountAllocations} = cartLineItems[i];\n\n    if (!discountAllocations) { continue; }\n\n    for (let j = 0; j < discountAllocations.length; j++) {\n      const allocation = discountAllocations[j];\n      const newDiscountApplication = Object.assign({},\n        allocation.discountApplication || {},\n        allocation.code ? {code: allocation.code} : null,\n        allocation.title ? {title: allocation.title} : null\n      );\n\n      const newAllocation = Object.assign({}, allocation);\n\n      delete newAllocation.code;\n      delete newAllocation.title;\n      newAllocation.discountApplication = newDiscountApplication;\n\n      discountAllocations[j] = newAllocation;\n    }\n  }\n\n  for (let i = 0; i < cartOrderLevelDiscountAllocations.length; i++) {\n    const allocation = cartOrderLevelDiscountAllocations[i];\n    const newDiscountApplication = Object.assign({},\n      allocation.discountApplication || {},\n      allocation.code ? {code: allocation.code} : null,\n      allocation.title ? {title: allocation.title} : null\n    );\n\n    const newAllocation = Object.assign({}, allocation);\n\n    delete newAllocation.code;\n    delete newAllocation.title;\n    newAllocation.discountApplication = newDiscountApplication;\n\n    cartOrderLevelDiscountAllocations[i] = newAllocation;\n  }\n}\n\nfunction groupOrderLevelDiscountAllocationsByDiscountId(cartDiscountAllocations) {\n  return cartDiscountAllocations.reduce((acc, discountAllocation) => {\n    const id = getDiscountAllocationId(discountAllocation);\n    const key = id.toLowerCase();\n\n    acc.set(key, [...(acc.get(key) || []), discountAllocation]);\n\n    return acc;\n  }, new Map());\n}\n\nfunction findLineIdForEachOrderLevelDiscountAllocation(\n  cartLines,\n  orderLevelDiscountAllocations\n) {\n  if (!cartLines.length || !orderLevelDiscountAllocations.length) {\n    return [];\n  }\n\n  if (orderLevelDiscountAllocations.length % cartLines.length !== 0) {\n    throw new Error(\n      `Invalid number of order-level discount allocations. For each order-level discount, there must be 1 order-level discount allocation for each line item. \n      Number of line items: ${cartLines.length}. Number of discount allocations: ${orderLevelDiscountAllocations.length}`\n    );\n  }\n\n  // May have multiple order-level discount allocations for a given line item\n  const discountIdToDiscountAllocationsMap =\n    groupOrderLevelDiscountAllocationsByDiscountId(orderLevelDiscountAllocations);\n\n  // Sort each array within the Map by discountedAmount so that the lowest discounted amount appears first\n  discountIdToDiscountAllocationsMap.forEach((allocations) => {\n    allocations.sort(\n      (first, second) => first.discountedAmount.amount - second.discountedAmount.amount\n    );\n  });\n\n  // Sort cart line items so that the item with the lowest cost (after line-level discounts) appears first\n  const sortedCartLineItems = [...cartLines].sort((first, second) => {\n    return first.cost.totalAmount.amount - second.cost.totalAmount.amount;\n  });\n\n  // For each discount, the discount allocation with the smallest amount should be applied\n  // to the item with the lowest cost (after line-level discounts)\n  return Array.from(discountIdToDiscountAllocationsMap.values()).flatMap(\n    (allocations) => {\n      return sortedCartLineItems.map((lineItem, index) => {\n        return {\n          id: lineItem.id,\n          discountAllocation: {\n            discountedAmount: allocations[index].discountedAmount,\n            discountApplication: allocations[index].discountApplication\n          }\n        };\n      });\n    }\n  );\n}\n\nexport function discountMapper({cartLineItems, cartDiscountAllocations, cartDiscountCodes}) {\n  let hasDiscountAllocations = false;\n\n  for (let i = 0; i < cartLineItems.length; i++) {\n    const {discountAllocations} = cartLineItems[i];\n\n    if (discountAllocations && discountAllocations.length) {\n      hasDiscountAllocations = true;\n      break;\n    }\n  }\n  if (\n    !hasDiscountAllocations &&\n    !cartDiscountAllocations.length\n  ) {\n    return {\n      discountApplications: [],\n      cartLinesWithAllDiscountAllocations: cartLineItems\n    };\n  }\n\n  // For each discount allocation, move the code/title field to be inside the discountApplication.\n  // This is because the code/title field is part of the discount allocation for a Cart, but part of\n  // the discount application for a Checkout\n  //\n  // CART EXAMPLE:                                  | CHECKOUT EXAMPLE:\n  // \"cart\": {                                      | \"checkout\": {\n  //   \"discountAllocations\": [                     |   \"discountApplications\": {\n  //     {                                          |     \"nodes\": [\n  //       \"discountedAmount\": {                    |       {\n  //         \"amount\": \"18.0\",                      |         \"targetSelection\": \"ALL\",\n  //         \"currencyCode\": \"CAD\"                  |         \"allocationMethod\": \"EACH\",\n  //       },                                       |         \"targetType\": \"SHIPPING_LINE\",\n  //       \"discountApplication\": {                 |         \"value\": {\n  //         \"targetType\": \"SHIPPING_LINE\",         |           \"percentage\": 100.0\n  //         \"allocationMethod\": \"EACH\",            |         },\n  //         \"targetSelection\": \"ALL\",              |         \"code\": \"FREESHIPPINGALLCOUNTRIES\",\n  //         \"value\": {                             |         \"applicable\": true\n  //           \"percentage\": 100.0                  |       }\n  //         }                                      |     ]\n  //       },                                       |   },\n  //       \"code\": \"FREESHIPPINGALLCOUNTRIES\"       | }\n  //     }                                          |\n  //   ]                                            |\n  //   \"discountCodes\": [                           |\n  //     {                                          |\n  //       \"code\": \"FREESHIPPINGALLCOUNTRIES\",      |\n  //       \"applicable\": true                       |\n  //     }                                          |\n  //   ],                                           |\n  // }                                              |\n  convertToCheckoutDiscountApplicationType(cartLineItems, cartDiscountAllocations);\n\n  // While both the Cart and Checkout API return discount allocations for line items and therefore appear similar, they are\n  // substantially different in how they handle order-level discounts.\n  //\n  // The Checkout API ONLY returns discount allocations as a field on line items (for both product-level and order-level discounts).\n  // Shipping discounts are only returned as part of `checkout.discountApplications` (and do NOT have any discount allocations).\n  //\n  // Unlike the Checkout API, the Cart API returns different types of discount allocations in 2 different places:\n  // 1. Discount allocations as a field on line items (for product-level discounts)\n  // 2. Discount allocations as a field on the Cart itself (for order-level discounts and shipping discounts)\n  //\n  // Therefore, to map the Cart API payload to the equivalent Checkout API payload, we need to go through all of the order-level discount\n  // allocations on the *Cart*, and determine which line item the discount is allocated to. But first, we must go through the cart-level\n  // discount allocations to split them into order-level and shipping-level discount allocations.\n  //     - ONLY the order-level discount allocations go onto line items.\n  const [shippingDiscountAllocations, orderLevelDiscountAllocations] = cartDiscountAllocations.reduce((acc, discountAllocation) => {\n    if (discountAllocation.discountApplication.targetType === 'SHIPPING_LINE') {\n      acc[0].push(discountAllocation);\n    } else {\n      acc[1].push(discountAllocation);\n    }\n\n    return acc;\n  }, [[], []]);\n  const cartLinesWithAllDiscountAllocations =\n    mergeCartOrderLevelDiscountAllocationsToCartLineDiscountAllocations({\n      lineItems: cartLineItems,\n      orderLevelDiscountAllocationsForLines: findLineIdForEachOrderLevelDiscountAllocation(\n        cartLineItems,\n        orderLevelDiscountAllocations\n      )\n    });\n\n  // The Cart API and Checkout API have almost identical fields for discount applications, but the `value` field's behaviour (for fixed-amount discounts)\n  // is different.\n  //\n  // With the Checkout API, the `value` field of a discount application is equal to the SUM of all of the `allocatedAmount`s of all of the discount allocations\n  // for that discount.\n  // With the Cart API, the `value` field of a discount application is always equal to the `allocatedAmount` of the discount allocation that the discount\n  // application is inside of. Therefore, to map this to the equivalent Checkout API payload, we need to find all of the discount allocations for the same\n  // discount, and sum up all of the allocated amounts to determine the TOTAL value of the discount.\n  const discountIdToDiscountApplicationMap = generateDiscountApplications(\n    cartLinesWithAllDiscountAllocations,\n    shippingDiscountAllocations,\n    cartDiscountCodes\n  );\n\n  return {\n    discountApplications: Array.from(\n      discountIdToDiscountApplicationMap.values()\n    ),\n    cartLinesWithAllDiscountAllocations\n  };\n}\n\nfunction mergeCartOrderLevelDiscountAllocationsToCartLineDiscountAllocations({\n  lineItems,\n  orderLevelDiscountAllocationsForLines\n}) {\n  return lineItems.map((line) => {\n    const lineItemId = line.id;\n    // Could have multiple order-level discount allocations for a given line item\n    const orderLevelDiscountAllocationsForLine =\n      orderLevelDiscountAllocationsForLines\n        .filter(({id}) => id === lineItemId)\n        .map(({discountAllocation}) => ({\n          discountedAmount: discountAllocation.discountedAmount,\n          discountApplication: discountAllocation.discountApplication\n        }));\n\n    const mergedDiscountAllocations = (line.discountAllocations || []).concat(orderLevelDiscountAllocationsForLine);\n    const result = Object.assign({}, line, {\n      discountAllocations: mergedDiscountAllocations\n    });\n\n    return result;\n  });\n}\n\nfunction generateDiscountApplications(cartLinesWithAllDiscountAllocations, shippingDiscountAllocations, discountCodes) {\n  const discountIdToDiscountApplicationMap = new Map();\n\n  if (!cartLinesWithAllDiscountAllocations) { return discountIdToDiscountApplicationMap; }\n\n  cartLinesWithAllDiscountAllocations.forEach(({discountAllocations}) => {\n    if (!discountAllocations) { return; }\n\n    discountAllocations.forEach((discountAllocation) => {\n      createCheckoutDiscountApplicationFromCartDiscountAllocation(discountAllocation, discountIdToDiscountApplicationMap, discountCodes);\n    });\n  });\n\n  shippingDiscountAllocations.forEach((discountAllocation) => {\n    createCheckoutDiscountApplicationFromCartDiscountAllocation(discountAllocation, discountIdToDiscountApplicationMap, discountCodes);\n  });\n\n  return discountIdToDiscountApplicationMap;\n}\n\nfunction createCheckoutDiscountApplicationFromCartDiscountAllocation(discountAllocation, discountIdToDiscountApplicationMap, discountCodes) {\n  const discountApp = discountAllocation.discountApplication;\n  const discountId = getDiscountAllocationId(discountAllocation);\n\n  if (!discountId) {\n    throw new Error(\n      `Discount allocation must have either code or title in discountApplication: ${JSON.stringify(\n        discountAllocation\n      )}`\n    );\n  }\n\n  if (discountIdToDiscountApplicationMap.has(discountId.toLowerCase())) {\n    const existingDiscountApplication =\n      discountIdToDiscountApplicationMap.get(discountId.toLowerCase());\n\n    // if existingDiscountApplication.value is an amount rather than a percentage discount\n    if (existingDiscountApplication.value && 'amount' in existingDiscountApplication.value) {\n      existingDiscountApplication.value = {\n        amount: (Number(existingDiscountApplication.value.amount) + Number(discountAllocation.discountedAmount.amount)).toFixed(2),\n        currencyCode: existingDiscountApplication.value.currencyCode,\n        type: existingDiscountApplication.value.type\n      };\n    }\n  } else {\n    let discountApplication = {\n      __typename: 'DiscountApplication',\n      targetSelection: discountApp.targetSelection,\n      allocationMethod: discountApp.allocationMethod,\n      targetType: discountApp.targetType,\n      value: discountApp.value,\n      hasNextPage: false,\n      hasPreviousPage: false\n    };\n\n    if ('code' in discountAllocation.discountApplication) {\n      const discountCode = discountCodes.find(\n        ({code}) => code.toLowerCase() === discountId.toLowerCase()\n      );\n\n      if (!discountCode) {\n        throw new Error(\n          `Discount code ${discountId} not found in cart discount codes. Discount codes: ${JSON.stringify(\n            discountCodes\n          )}`\n        );\n      }\n      discountApplication = Object.assign({}, discountApplication, {\n        code: discountAllocation.discountApplication.code,\n        applicable: discountCode.applicable,\n        type: {\n          fieldBaseTypes: {\n            applicable: 'Boolean',\n            code: 'String'\n          },\n          implementsNode: false,\n          kind: 'OBJECT',\n          name: 'DiscountApplication'\n        }\n      });\n    } else {\n      discountApplication = Object.assign({}, discountApplication, {\n        title: discountAllocation.discountApplication.title,\n        type: {\n          fieldBaseTypes: {\n            applicable: 'Boolean',\n            title: 'String'\n          },\n          implementsNode: false,\n          kind: 'OBJECT',\n          name: 'DiscountApplication'\n        }\n      });\n    }\n\n    discountIdToDiscountApplicationMap.set(discountId.toLowerCase(), discountApplication);\n  }\n}\n\nexport function deepSortLines(lineItems) {\n  return lineItems\n    .map((lineItem) => {\n      const sortedDiscountAllocations = lineItem.discountAllocations.sort((first, second) =>\n        getDiscountApplicationId(first.discountApplication).localeCompare(\n          getDiscountApplicationId(second.discountApplication)\n        )\n      );\n\n      return Object.assign({}, lineItem, {\n        discountAllocations: sortedDiscountAllocations\n      });\n    })\n    .sort((first, second) => first.id.localeCompare(second.id));\n}\n\nexport function deepSortDiscountApplications(discountApplications) {\n  return discountApplications.sort((first, second) =>\n    getDiscountApplicationId(first).localeCompare(getDiscountApplicationId(second))\n  );\n}\n", "import {discountMapper, getDiscountAllocationId, getDiscountApplicationId} from './cart-discount-mapping';\n\nexport function getVariantType() {\n  return {\n    name: 'ProductVariant',\n    kind: 'OBJECT',\n    fieldBaseTypes: {\n      availableForSale: 'Boolean',\n      compareAtPrice: 'MoneyV2',\n      id: 'ID',\n      image: 'Image',\n      price: 'MoneyV2',\n      product: 'Product',\n      selectedOptions: 'SelectedOption',\n      sku: 'String',\n      title: 'String',\n      unitPrice: 'MoneyV2',\n      unitPriceMeasurement: 'UnitPriceMeasurement',\n      weight: 'Float'\n    },\n    implementsNode: true\n  };\n}\n\nexport function getLineItemType() {\n  return {\n    name: 'CheckoutLineItem',\n    kind: 'OBJECT',\n    fieldBaseTypes: {\n      customAttributes: 'Attribute',\n      discountAllocations: 'Object[]',\n      id: 'ID',\n      quantity: 'Int',\n      title: 'String',\n      variant: 'Merchandise'\n    },\n    implementsNode: true\n  };\n}\n\nfunction getDiscountAllocationType() {\n  return {\n    fieldBaseTypes: {\n      allocatedAmount: 'MoneyV2',\n      discountApplication: 'DiscountApplication'\n    },\n    implementsNode: false,\n    kind: 'OBJECT',\n    name: 'DiscountAllocation'\n  };\n}\n\nexport function mapVariant(merchandise) {\n  // Copy all properties except 'product'\n  const result = {};\n\n  for (const key in merchandise) {\n    if (merchandise.hasOwnProperty(key) && key !== 'product') {\n      result[key] = merchandise[key];\n    }\n  }\n\n  // The actual Cart merchandise and Checkout variant objects map cleanly to each other,\n  // but the SDK wasn't fetching the title from the product object, so we need to remove it\n  const productWithoutTitle = {};\n\n  if (merchandise.product) {\n    for (const key in merchandise.product) {\n      if (merchandise.product.hasOwnProperty(key) && key !== 'title') {\n        productWithoutTitle[key] = merchandise.product[key];\n      }\n    }\n  }\n\n  // Add additional properties\n  result.priceV2 = merchandise.price;\n  result.compareAtPriceV2 = merchandise.compareAtPrice;\n  result.product = productWithoutTitle;\n  result.type = getVariantType();\n\n  return result;\n}\n\nexport function mapDiscountAllocations(discountAllocations, discountApplications) {\n  if (!discountAllocations) { return []; }\n\n  const result = [];\n\n  for (let i = 0; i < discountAllocations.length; i++) {\n    const allocation = discountAllocations[i];\n    let application = null;\n\n    for (let j = 0; j < discountApplications.length; j++) {\n      if (getDiscountAllocationId(allocation) === getDiscountApplicationId(discountApplications[j])) {\n        application = discountApplications[j];\n        break;\n      }\n    }\n\n    if (!application) {\n      throw new Error(`Missing discount application for allocation: ${JSON.stringify(allocation)}`);\n    }\n\n    const discountApp = Object.assign({}, application);\n\n    result.push({\n      allocatedAmount: allocation.discountedAmount,\n      discountApplication: discountApp,\n      type: getDiscountAllocationType()\n    });\n  }\n\n  return result;\n}\n\nexport function mapLineItems(lines, discountApplications, skipDiscounts = false) {\n  if (!lines || !Array.isArray(lines)) { return []; }\n\n  const result = [];\n\n  for (let i = 0; i < lines.length; i++) {\n    const line = lines[i];\n\n    if (!line || !line.merchandise || !line.merchandise.product) { continue; }\n\n    const variant = mapVariant(line.merchandise);\n\n    let discountAllocations = [];\n\n    if (!skipDiscounts) {\n      try {\n        discountAllocations = mapDiscountAllocations(line.discountAllocations || [], discountApplications);\n      } catch (error) {\n        // eslint-disable-next-line no-console\n        console.error('Error mapping discount allocations:', error.message);\n        discountAllocations = [];\n      }\n    }\n\n    result.push({\n      customAttributes: line.attributes,\n      discountAllocations,\n      id: line.id,\n      quantity: line.quantity,\n      title: line.merchandise.product.title,\n      variant,\n      hasNextPage: false,\n      hasPreviousPage: false,\n      variableValues: line.variableValues,\n      type: getLineItemType()\n    });\n  }\n\n  return result;\n}\n\nexport function mapDiscountsAndLines(cart) {\n  if (!cart) { return {discountApplications: [], cartLinesWithDiscounts: []}; }\n\n  let discountMappingResult;\n  let discountMappingFailed = false;\n\n  try {\n    discountMappingResult = discountMapper({\n      cartLineItems: cart.lines || [],\n      cartDiscountAllocations: cart.discountAllocations || [],\n      cartDiscountCodes: cart.discountCodes || []\n    });\n  } catch (error) {\n    // eslint-disable-next-line no-console\n    console.error('Error mapping discounts:', error.message);\n    discountMappingFailed = true;\n  }\n\n  let mappedLines;\n\n  if (discountMappingFailed) {\n    // When discount mapping fails, still map the lines but without any discount allocations\n    // eslint-disable-next-line newline-after-var\n    const linesWithClearedDiscounts = (cart.lines || []).map((line) => {\n      const lineCopy = Object.assign({}, line);\n\n      lineCopy.discountAllocations = [];\n\n      return lineCopy;\n    });\n    mappedLines = mapLineItems(linesWithClearedDiscounts, [], true);\n  } else {\n    // Normal path when discount mapping succeeds\n    mappedLines = mapLineItems(\n      discountMappingResult.cartLinesWithAllDiscountAllocations || [],\n      discountMappingResult.discountApplications || []\n    );\n  }\n\n  return {\n    discountApplications: discountMappingFailed ? [] : ((discountMappingResult && discountMappingResult.discountApplications) || []),\n    cartLinesWithDiscounts: mappedLines\n  };\n}\n", "import {mapDiscountsAndLines} from './utilities/cart-mapping-utils';\n\n// NOTE: fields such as availableShippingRates are not included because they are not queried by the JS Buy SDK\nconst UNSUPPORTED_FIELDS = {\n  completedAt: null,\n  order: null,\n  orderStatusUrl: null,\n  ready: false,\n  requiresShipping: true,\n  shippingLine: null,\n  taxExempt: false,\n  taxesIncluded: false\n};\n\nexport function mapCartPayload(cart) {\n  if (!cart) { return null; }\n\n  const result = mapDiscountsAndLines(cart);\n  const discountApplications = result.discountApplications;\n  const cartLinesWithDiscounts = result.cartLinesWithDiscounts;\n\n  const buyerIdentity = {\n    countryCode: cart.buyerIdentity && cart.buyerIdentity.countryCode\n  };\n\n  let email = null;\n\n  if (cart.buyerIdentity && cart.buyerIdentity.email) {\n    email = cart.buyerIdentity.email;\n  }\n\n  let shippingAddress = null;\n\n  if (cart.buyerIdentity &&\n    cart.buyerIdentity.deliveryAddressPreferences &&\n    cart.buyerIdentity.deliveryAddressPreferences.length) {\n    shippingAddress = cart.buyerIdentity.deliveryAddressPreferences[0];\n  }\n\n  let currencyCode = null;\n  let totalAmount = null;\n  let totalTaxAmount = null;\n  let totalDutyAmount = null;\n  let checkoutChargeAmount = null;\n\n  if (cart.cost) {\n    if (cart.cost.totalAmount) {\n      currencyCode = cart.cost.totalAmount.currencyCode;\n      totalAmount = cart.cost.totalAmount;\n    }\n    if (cart.cost.totalTaxAmount) {\n      totalTaxAmount = cart.cost.totalTaxAmount;\n    }\n    if (cart.cost.totalDutyAmount) {\n      totalDutyAmount = cart.cost.totalDutyAmount;\n    }\n    if (cart.cost.checkoutChargeAmount) {\n      checkoutChargeAmount = cart.cost.checkoutChargeAmount;\n    }\n  }\n\n  const appliedGiftCards = cart.appliedGiftCards || [];\n  let totalPrice = null;\n\n  // This field will be defined in the API, but we're making it \"optional\" here so that our\n  // unit tests for other fields work while only passing in the relevant fields\n  if (totalAmount) {\n    totalPrice = calculateTotalPrice(cart, totalAmount);\n  }\n\n  let subtotalPrice = null;\n\n  // This field will be defined in the API, but we're making it \"optional\" here so that our\n  // unit tests for other fields work while only passing in the relevant fields\n  if (totalPrice) {\n    subtotalPrice = calculateSubtotalPrice(totalPrice, totalDutyAmount, totalTaxAmount);\n  }\n\n  let webUrl = cart.checkoutUrl;\n\n  if (webUrl) {\n    try {\n      const url = new URL(webUrl);\n\n      if (!url.searchParams.has('_fd') || url.searchParams.get('_fd') !== '0') {\n        url.searchParams.set('_fd', '0');\n        webUrl = url.toString();\n      }\n    } catch (error) {\n      // If URL parsing fails, return the original URL unchanged\n      // rather than risk breaking the checkout flow\n    }\n  }\n\n  const checkoutPayload = Object.assign({\n    appliedGiftCards,\n    buyerIdentity,\n    createdAt: cart.createdAt,\n    currencyCode,\n    customAttributes: cart.attributes,\n    discountApplications,\n    email,\n    id: cart.id,\n    lineItems: cartLinesWithDiscounts,\n    lineItemsSubtotalPrice: checkoutChargeAmount,\n    note: cart.note,\n    paymentDue: totalAmount,\n    paymentDueV2: totalAmount,\n    shippingAddress,\n    subtotalPrice,\n    subtotalPriceV2: subtotalPrice,\n    totalPrice,\n    totalPriceV2: totalPrice,\n    totalTax: totalTaxAmount || getDefaultMoneyObject(currencyCode, totalAmount),\n    totalTaxV2: totalTaxAmount || getDefaultMoneyObject(currencyCode, totalAmount),\n    updatedAt: cart.updatedAt,\n    webUrl\n  }, UNSUPPORTED_FIELDS);\n\n  normalizeCartMoneyFieldDecimalPlaces(checkoutPayload);\n\n  return checkoutPayload;\n}\n\n/**\n *\n * @description Normalize all currency fields in the checkout payload to contain\n * the same number of decimal places that would be returned by the storefront API.\n *\n * In the storefront API, currency amounts are returned as a string that contains\n * 1 decimal place (if the 2nd decimal place is 0) or else 2 decimal places.\n *\n * In our mapping functions, we are typically converting to strings with 2 decimal\n * places. In case any clients of the JS Buy SDK are relying only a single decimal\n * place being returned in some cases, we want to normalize the decimal places.\n */\nfunction normalizeCartMoneyFieldDecimalPlaces(checkout) {\n  // The fields that we have mapped the currency for (that we therefore need to normalize)\n  // are: discountApplication amounts, subtotalPrice, and totalPrice.\n\n  if (checkout.discountApplications) {\n    for (let i = 0; i < checkout.discountApplications.length; i++) {\n      if (typeof checkout.discountApplications[i].value.percentage !== 'undefined') {\n        continue;\n      }\n\n      const discountApplicationAmount = checkout.discountApplications[i].value.amount.toString();\n\n      if (!shouldReturnWithOneDecimalPlace(discountApplicationAmount)) {\n        continue;\n      }\n\n      checkout.discountApplications[i].value.amount = convertToOneDecimalPlace(discountApplicationAmount);\n    }\n  }\n\n  if (checkout.lineItems) {\n    for (let i = 0; i < checkout.lineItems.length; i++) {\n      for (let j = 0; j < checkout.lineItems[i].discountAllocations.length; j++) {\n        const discountApplication = checkout.lineItems[i].discountAllocations[j].discountApplication;\n\n        if (typeof discountApplication.value.percentage !== 'undefined') {\n          continue;\n        }\n\n        const discountApplicationAmount = discountApplication.value.amount.toString();\n\n        if (!shouldReturnWithOneDecimalPlace(discountApplicationAmount)) {\n          continue;\n        }\n\n        discountApplication.value.amount = convertToOneDecimalPlace(discountApplicationAmount);\n      }\n    }\n  }\n\n  if (checkout.subtotalPrice) {\n    if (shouldReturnWithOneDecimalPlace(checkout.subtotalPrice.amount)) {\n      checkout.subtotalPrice.amount = convertToOneDecimalPlace(checkout.subtotalPrice.amount);\n      checkout.subtotalPriceV2.amount = convertToOneDecimalPlace(checkout.subtotalPriceV2.amount);\n    }\n  }\n\n  if (checkout.totalPrice) {\n    if (shouldReturnWithOneDecimalPlace(checkout.totalPrice.amount)) {\n      checkout.totalPrice.amount = convertToOneDecimalPlace(checkout.totalPrice.amount);\n      checkout.totalPriceV2.amount = convertToOneDecimalPlace(checkout.totalPriceV2.amount);\n    }\n  }\n}\n\n/**\n * @description Whether the SF API would return this amount with 1 decimal place\n * (as opposed to 2 decimal places). See normalizeCartMoneyFieldDecimalPlaces\n * for more information.\n * @param {string} currency field to check\n * @returns {boolean} whether the SF API would return this amount with 1 decimal place\n */\nfunction shouldReturnWithOneDecimalPlace(amount) {\n  if (!amount || !amount.toString().includes('.')) {\n    return false;\n  }\n\n  const currencyDecimals = amount.toString().split('.')[1];\n\n  if (currencyDecimals.length === 2 && currencyDecimals[1] === '0') {\n    return true;\n  }\n\n  return false;\n}\n\nfunction convertToOneDecimalPlace(stringAmount) {\n  return parseFloat(stringAmount).toFixed(1);\n}\n\nfunction getDefaultMoneyObject(currencyCode, totalAmount) {\n  return {\n    amount: '0.0',\n    currencyCode,\n    type: totalAmount && totalAmount.type\n  };\n}\n\nfunction calculateTotalPrice(cart, totalAmount) {\n  if (!cart.appliedGiftCards || !cart.appliedGiftCards.length) {\n    return totalAmount;\n  }\n\n  // Assuming cart's totalAmount will have the same currency code as gift cards' presentmentAmountUsed\n  let giftCardTotal = 0;\n\n  for (let i = 0; i < cart.appliedGiftCards.length; i++) {\n    giftCardTotal += parseFloat(cart.appliedGiftCards[i].presentmentAmountUsed.amount);\n  }\n\n  return {\n    amount: (parseFloat(totalAmount.amount) + giftCardTotal).toFixed(2),\n    currencyCode: totalAmount.currencyCode,\n    type: totalAmount.type\n  };\n}\n\nfunction calculateSubtotalPrice(totalPrice, totalDutyAmount, totalTaxAmount) {\n  const dutyAmount = totalDutyAmount ? totalDutyAmount.amount : 0;\n  const taxAmount = totalTaxAmount ? totalTaxAmount.amount : 0;\n\n  return {\n    amount: (parseFloat(totalPrice.amount) - parseFloat(dutyAmount) - parseFloat(taxAmount)).toFixed(2),\n    currencyCode: totalPrice.currencyCode,\n    type: totalPrice.type\n  };\n}\n", "const CartErrorCodeToCheckoutErrorCode = {\n  ADDRESS_FIELD_CONTAINS_EMOJIS: 'NOT_SUPPORTED',\n  ADDRESS_FIELD_CONTAINS_HTML_TAGS: 'NOT_SUPPORTED',\n  ADDRESS_FIELD_CONTAINS_URL: 'NOT_SUPPORTED',\n  ADDRESS_FIELD_DOES_NOT_MATCH_EXPECTED_PATTERN: 'NOT_SUPPORTED',\n  ADDRESS_FIELD_IS_REQUIRED: 'PRESENT',\n  ADDRESS_FIELD_IS_TOO_LONG: 'TOO_LONG',\n  INVALID: 'INVALID',\n  INVALID_COMPANY_LOCATION: 'INVALID',\n  INVALID_DELIVERY_GROUP: 'INVALID',\n  INVALID_DELIVERY_OPTION: 'INVALID',\n  INVALID_INCREMENT: 'INVALID',\n  INVALID_MERCHANDISE_LINE: 'LINE_ITEM_NOT_FOUND',\n  INVALID_METAFIELDS: 'INVALID',\n  INVALID_PAYMENT: 'INVALID',\n  INVALID_PAYMENT_EMPTY_CART: 'INVALID',\n  INVALID_ZIP_CODE_FOR_COUNTRY: 'INVALID_FOR_COUNTRY',\n  INVALID_ZIP_CODE_FOR_PROVINCE: 'INVALID_FOR_COUNTRY_AND_PROVINCE',\n  LESS_THAN: 'LESS_THAN',\n  MAXIMUM_EXCEEDED: 'NOT_ENOUGH_IN_STOCK',\n  MINIMUM_NOT_MET: 'GREATER_THAN_OR_EQUAL_TO',\n  MISSING_CUSTOMER_ACCESS_TOKEN: 'PRESENT',\n  MISSING_DISCOUNT_CODE: 'PRESENT',\n  MISSING_NOTE: 'PRESENT',\n  NOTE_TOO_LONG: 'TOO_LONG',\n  PAYMENT_METHOD_NOT_SUPPORTED: 'NOT_SUPPORTED',\n  PROVINCE_NOT_FOUND: 'INVALID_PROVINCE_IN_COUNTRY',\n  UNSPECIFIED_ADDRESS_ERROR: 'INVALID',\n  VALIDATION_CUSTOM: 'INVALID',\n  ZIP_CODE_NOT_SUPPORTED: 'NOT_SUPPORTED'\n};\n\nconst CartWarningCodeToCheckoutErrorCode = {\n  MERCHANDISE_NOT_ENOUGH_STOCK: 'NOT_ENOUGH_IN_STOCK',\n  MERCHANDISE_OUT_OF_STOCK: 'NOT_ENOUGH_IN_STOCK',\n  PAYMENTS_GIFT_CARDS_UNAVAILABLE: 'NOT_SUPPORTED'\n};\n\nfunction userErrorsMapper(userErrors) {\n  return userErrors.map(({code, field, message}) => ({\n    // eslint-disable-next-line no-undefined\n    code: code ? CartErrorCodeToCheckoutErrorCode[code] : undefined,\n    field,\n    message\n  }));\n}\n\nfunction warningsMapper(warnings) {\n  return warnings.map(({code, message}) => ({\n    // eslint-disable-next-line no-undefined\n    code: code ? CartWarningCodeToCheckoutErrorCode[code] : undefined,\n    message\n  }));\n}\n\nexport default function checkoutUserErrorsMapper(userErrors, warnings) {\n  const hasUserErrors = userErrors && userErrors.length;\n  const hasWarnings = warnings && warnings.length;\n\n  if (!hasUserErrors && !hasWarnings) {\n    return [];\n  }\n\n  const checkoutUserErrors = hasUserErrors ? userErrorsMapper(userErrors) : [];\n  const checkoutWarnings = hasWarnings ? warningsMapper(warnings) : [];\n\n  return [...checkoutUserErrors, ...checkoutWarnings];\n}\n", "import {mapCartPayload} from './cart-payload-mapper';\nimport checkoutUserErrorsMapper from './checkout-map-user-error-codes';\n\nexport default function handleCartMutation(mutationRoot<PERSON>ey, client) {\n  return function({data = {}, errors, model = {}}) {\n    const rootData = data[mutationRootKey];\n    const rootModel = model[mutationRootKey];\n\n    if (rootData && rootData.cart) {\n      return client.fetchAllPages(rootModel.cart.lines, {pageSize: 250}).then((lines) => {\n        rootModel.cart.attrs.lines = lines;\n        const checkoutUserErrors = checkoutUserErrorsMapper(rootData.userErrors, rootData.warnings);\n\n        try {\n          return Object.assign({},\n            mapCartPayload(rootModel.cart, mutationRootKey),\n            {userErrors: checkoutUserErrors, errors: rootModel.cart.errors}\n          );\n        } catch (error) {\n          return Promise.reject(error);\n        }\n      });\n    }\n\n    if (errors && errors.length) {\n      return Promise.reject(new Error(JSON.stringify(errors)));\n    }\n\n    if (rootData && (rootData.userErrors.length || rootData.warnings.length)) {\n      const checkoutUserErrors = checkoutUserErrorsMapper(rootData.userErrors, rootData.warnings);\n\n      return Promise.reject(checkoutUserErrors);\n    }\n\n    return Promise.reject(new Error(`The ${mutationRootKey} mutation failed due to an unknown error.`));\n  };\n}\n", "import Resource from './resource';\nimport handleCartMutation from './handle-cart-mutation';\nimport {mapCartPayload} from './cart-payload-mapper';\n\nimport cartNodeQuery from './graphql/CartNodeQuery.graphql';\nimport cartCreateMutation from './graphql/CartCreateMutation.graphql';\nimport cartAttributesUpdateMutation from './graphql/CartAttributesUpdateMutation.graphql';\nimport cartBuyerIdentityUpdateMutation from './graphql/CartBuyerIdentityUpdateMutation.graphql';\nimport cartDiscountCodesUpdateMutation from './graphql/CartDiscountCodesUpdateMutation.graphql';\nimport cartGiftCardCodesUpdateMutation from './graphql/CartGiftCardCodesUpdateMutation.graphql';\nimport cartLinesAddMutation from './graphql/CartLinesAddMutation.graphql';\nimport cartLinesRemoveMutation from './graphql/CartLinesRemoveMutation.graphql';\nimport cartLinesUpdateMutation from './graphql/CartLinesUpdateMutation.graphql';\nimport cartNoteUpdateMutation from './graphql/CartNoteUpdateMutation.graphql';\nimport cartGiftCardCodesRemoveMutation from './graphql/CartGiftCardCodesRemoveMutation.graphql';\n\n/**\n * The JS Buy SDK cart resource\n * @class\n */\nclass CheckoutResource extends Resource {\n\n  /**\n   * Fetches a card by ID.\n   *\n   * @example\n   * client.cart.fetch('FlZj9rZXlN5MDY4ZDFiZTUyZTUwNTE2MDNhZjg=').then((cart) => {\n   *   // Do something with the cart\n   * });\n   *\n   * @param {String} id The id of the card to fetch.\n   * @return {Promise|GraphModel} A promise resolving with a `GraphModel` of the cart.\n   */\n  fetch(id) {\n    return this.graphQLClient\n      .send(cartNodeQuery, {id})\n      .then(({model, data}) => {\n        return new Promise((resolve, reject) => {\n          try {\n            const cart = data.cart || data.node;\n\n            if (!cart) {\n              return resolve(null);\n            }\n\n            return this.graphQLClient\n              .fetchAllPages(model.cart.lines, {pageSize: 250})\n              .then((lines) => {\n                model.cart.attrs.lines = lines;\n\n                return resolve(mapCartPayload(model.cart));\n              });\n          } catch (error) {\n            if (error) {\n              reject(error);\n            } else {\n              reject([{message: 'an unknown error has occurred.'}]);\n            }\n          }\n\n          return resolve(null);\n        });\n      });\n  }\n\n  /**\n   * Creates a checkout.\n   *\n   * @example\n   * const input = {\n   *   lineItems: [\n   *     {variantId: 'Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC8yOTEwNjAyMjc5Mg==', quantity: 5}\n   *   ]\n   * };\n   *\n   * client.checkout.create(input).then((checkout) => {\n   *   // Do something with the newly created checkout\n   * });\n   *\n   * @param {Object} [input] An input object containing zero or more of:\n   *   @param {String} [input.email] An email connected to the checkout.\n   *   @param {Object[]} [input.lineItems] A list of line items in the checkout. See the {@link https://help.shopify.com/api/storefront-api/reference/input-object/checkoutlineiteminput|Storefront API reference} for valid input fields for each line item.\n   *   @param {Object} [input.shippingAddress] A shipping address. See the {@link https://help.shopify.com/api/storefront-api/reference/input-object/mailingaddressinput|Storefront API reference} for valid input fields.\n   *   @param {String} [input.note] A note for the checkout.\n   *   @param {Object[]} [input.customAttributes] A list of custom attributes for the checkout. See the {@link https://help.shopify.com/api/storefront-api/reference/input-object/attributeinput|Storefront API reference} for valid input fields.\n   *   @deprecated {String} [input.presentmentCurrencyCode ] A presentment currency code. See the {@link https://help.shopify.com/en/api/storefront-api/reference/enum/currencycode|Storefront API reference} for valid currency code values.\n   *   @return {Promise|GraphModel} A promise resolving with the created checkout.\n   */\n  create(i = {}) {\n    const input = this.inputMapper.create(i);\n\n    return this.graphQLClient\n      .send(cartCreateMutation, {input})\n      .then(handleCartMutation('cartCreate', this.graphQLClient));\n  }\n\n  /**\n   * Replaces the value of checkout's custom attributes and/or note with values defined in the input\n   *\n   * @example\n   * const checkoutId = 'Z2lkOi8vc2hvcGlmeS9DaGVja291dC9kMTZmM2EzMDM4Yjc4N=';\n   * const input = {customAttributes: [{key: \"MyKey\", value: \"MyValue\"}]};\n   *\n   * client.checkout.updateAttributes(checkoutId, input).then((checkout) => {\n   *   // Do something with the updated checkout\n   * });\n   *\n   * @param {String} checkoutId The ID of the checkout to update.\n   * @param {Object} [input] An input object containing zero or more of:\n   *   @param {Object[]} [input.customAttributes] A list of custom attributes for the checkout. See the {@link https://help.shopify.com/api/storefront-api/reference/input-object/attributeinput|Storefront API reference} for valid input fields.\n   *   @param {String} [input.note] A note for the checkout.\n   * @return {Promise|GraphModel} A promise resolving with the updated checkout.\n   */\n  updateAttributes(checkoutId, input = {}) {\n    const {cartAttributesUpdateInput, cartNoteUpdateInput} =\n      this.inputMapper.updateAttributes(checkoutId, input);\n    let promise = Promise.resolve();\n\n    function updateNote() {\n      return this.graphQLClient\n        .send(cartNoteUpdateMutation, cartNoteUpdateInput)\n        .then(handleCartMutation('cartNoteUpdate', this.graphQLClient));\n    }\n\n    function updateAttributes() {\n      return this.graphQLClient\n        .send(cartAttributesUpdateMutation, cartAttributesUpdateInput)\n        .then(handleCartMutation('cartAttributesUpdate', this.graphQLClient));\n    }\n\n    if (typeof cartNoteUpdateInput.note !== 'undefined') {\n      promise = promise.then(() => updateNote.call(this));\n    }\n\n    if (cartAttributesUpdateInput.attributes.length) {\n      promise = promise.then(() => updateAttributes.call(this));\n    }\n\n    return promise;\n  }\n\n  /**\n   * Replaces the value of checkout's email address\n   *\n   * @example\n   * const checkoutId = 'Z2lkOi8vc2hvcGlmeS9DaGVja291dC9kMTZmM2EzMDM4Yjc4N=';\n   * const email = '<EMAIL>';\n   *\n   * client.checkout.updateEmail(checkoutId, email).then((checkout) => {\n   *   // Do something with the updated checkout\n   * });\n   *\n   * @param {String} checkoutId The ID of the checkout to update.\n   * @param {String} email The email address to apply to the checkout.\n   * @return {Promise|GraphModel} A promise resolving with the updated checkout.\n   */\n  updateEmail(checkoutId, email) {\n    const variables = this.inputMapper.updateEmail(checkoutId, email);\n\n    return this.graphQLClient\n      .send(cartBuyerIdentityUpdateMutation, variables)\n      .then(handleCartMutation('cartBuyerIdentityUpdate', this.graphQLClient));\n  }\n\n  /**\n   * Adds line items to an existing checkout.\n   *\n   * @example\n   * const checkoutId = 'Z2lkOi8vc2hvcGlmeS9DaGVja291dC9kMTZmM2EzMDM4Yjc4N=';\n   * const lineItems = [{variantId: 'Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC8yOTEwNjAyMjc5Mg==', quantity: 5}];\n   *\n   * client.checkout.addLineItems(checkoutId, lineItems).then((checkout) => {\n   *   // Do something with the updated checkout\n   * });\n   *\n   * @param {String} checkoutId The ID of the checkout to add line items to.\n   * @param {Object[]} lineItems A list of line items to add to the checkout. See the {@link https://help.shopify.com/api/storefront-api/reference/input-object/checkoutlineiteminput|Storefront API reference} for valid input fields for each line item.\n   * @return {Promise|GraphModel} A promise resolving with the updated checkout.\n   */\n  addLineItems(checkoutId, lineItems = []) {\n    const variables = this.inputMapper.addLineItems(checkoutId, lineItems);\n\n    return this.graphQLClient\n      .send(cartLinesAddMutation, variables)\n      .then(handleCartMutation('cartLinesAdd', this.graphQLClient));\n  }\n\n  /**\n   * Applies a discount to an existing checkout using a discount code.\n   *\n   * @example\n   * const checkoutId = 'Z2lkOi8vc2hvcGlmeS9DaGVja291dC9kMTZmM2EzMDM4Yjc4N=';\n   * const discountCode = 'best-discount-ever';\n   *\n   * client.checkout.addDiscount(checkoutId, discountCode).then((checkout) => {\n   *   // Do something with the updated checkout\n   * });\n   *\n   * @param {String} checkoutId The ID of the checkout to add discount to.\n   * @param {String} discountCode The discount code to apply to the checkout.\n   * @return {Promise|GraphModel} A promise resolving with the updated checkout.\n   */\n  addDiscount(checkoutId, discountCode) {\n    // We want access to Cart's `discountCodes` field, so we can't just use the\n    // existing `fetch` method since that also maps and removes the `discountCodes` field.\n    // We must therefore look at the raw Cart data to be able to see ALL existing discount codes,\n    // whether they are `applied` or not.\n\n    // The query below is identical to the `fetch` method's query EXCEPT we don't call `mapCartPayload` here\n    return this.graphQLClient.send(cartNodeQuery, {id: checkoutId}).then(({model, data}) => {\n      return new Promise((resolve, reject) => {\n        try {\n          const cart = data.cart || data.node;\n\n          if (!cart) {\n            return resolve(null);\n          }\n\n          return this.graphQLClient\n            .fetchAllPages(model.cart.lines, {pageSize: 250})\n            .then((lines) => {\n              model.cart.attrs.lines = lines;\n\n              return resolve(model.cart);\n            });\n        } catch (error) {\n          if (error) {\n            reject(error);\n          } else {\n            reject([{message: 'an unknown error has occurred.'}]);\n          }\n        }\n\n        return resolve(null);\n      });\n    }).then((checkout) => {\n      const existingCodes = checkout.discountCodes.map(\n        (code) => code.code\n      );\n\n      const variables = this.inputMapper.addDiscount(\n        checkoutId,\n        existingCodes.concat(discountCode)\n      );\n\n      return this.graphQLClient\n        .send(cartDiscountCodesUpdateMutation, variables)\n        .then(\n          handleCartMutation('cartDiscountCodesUpdate', this.graphQLClient)\n        );\n    });\n  }\n\n  /**\n   * Removes the applied discount from an existing checkout.\n   *\n   * @example\n   * const checkoutId = 'Z2lkOi8vc2hvcGlmeS9DaGVja291dC9kMTZmM2EzMDM4Yjc4N=';\n   *\n   * client.checkout.removeDiscount(checkoutId).then((checkout) => {\n   *   // Do something with the updated checkout\n   * });\n   *\n   * @param {String} checkoutId The ID of the checkout to remove the discount from.\n   * @return {Promise|GraphModel} A promise resolving with the updated checkout.\n   */\n  removeDiscount(checkoutId) {\n    const variables = this.inputMapper.removeDiscount(checkoutId);\n\n    return this.graphQLClient\n      .send(cartDiscountCodesUpdateMutation, variables)\n      .then(handleCartMutation('cartDiscountCodesUpdate', this.graphQLClient));\n  }\n\n  /**\n   * Applies gift cards to an existing checkout using a list of gift card codes\n   *\n   * @example\n   * const checkoutId = 'Z2lkOi8vc2hvcGlmeS9DaGVja291dC9kMTZmM2EzMDM4Yjc4N=';\n   * const giftCardCodes = ['6FD8853DAGAA949F'];\n   *\n   * client.checkout.addGiftCards(checkoutId, giftCardCodes).then((checkout) => {\n   *   // Do something with the updated checkout\n   * });\n   *\n   * @param {String} checkoutId The ID of the checkout to add gift cards to.\n   * @param {String[]} giftCardCodes The gift card codes to apply to the checkout.\n   * @return {Promise|GraphModel} A promise resolving with the updated checkout.\n   */\n  addGiftCards(checkoutId, giftCardCodes) {\n    const variables = this.inputMapper.addGiftCards(checkoutId, giftCardCodes);\n\n    return this.graphQLClient\n      .send(cartGiftCardCodesUpdateMutation, variables)\n      .then(handleCartMutation('cartGiftCardCodesUpdate', this.graphQLClient));\n  }\n\n  /**\n   * Remove a gift card from an existing checkout\n   *\n   * @example\n   * const checkoutId = 'Z2lkOi8vc2hvcGlmeS9DaGVja291dC9kMTZmM2EzMDM4Yjc4N=';\n   * const appliedGiftCardId = 'Z2lkOi8vc2hvcGlmeS9BcHBsaWVkR2lmdENhcmQvNDI4NTQ1ODAzMTI=';\n   *\n   * client.checkout.removeGiftCard(checkoutId, appliedGiftCardId).then((checkout) => {\n   *   // Do something with the updated checkout\n   * });\n   *\n   * @param {String} checkoutId The ID of the checkout to add gift cards to.\n   * @param {String} appliedGiftCardId The gift card id to remove from the checkout.\n   * @return {Promise|GraphModel} A promise resolving with the updated checkout.\n   */\n  removeGiftCard(checkoutId, appliedGiftCardId) {\n    const variables = this.inputMapper.removeGiftCard(\n      checkoutId,\n      appliedGiftCardId\n    );\n\n    return this.graphQLClient\n      .send(cartGiftCardCodesRemoveMutation, variables)\n      .then(handleCartMutation('cartGiftCardCodesRemove', this.graphQLClient));\n  }\n\n  /**\n   * Removes line items from an existing checkout.\n   *\n   * @example\n   * const checkoutId = 'Z2lkOi8vc2hvcGlmeS9DaGVja291dC9kMTZmM2EzMDM4Yjc4N=';\n   * const lineItemIds = ['TViZGE5Y2U1ZDFhY2FiMmM2YT9rZXk9NTc2YjBhODcwNWIxYzg0YjE5ZjRmZGQ5NjczNGVkZGU='];\n   *\n   * client.checkout.removeLineItems(checkoutId, lineItemIds).then((checkout) => {\n   *   // Do something with the updated checkout\n   * });\n   *\n   * @param {String} checkoutId The ID of the checkout to remove line items from.\n   * @param {String[]} lineItemIds A list of the ids of line items to remove from the checkout.\n   * @return {Promise|GraphModel} A promise resolving with the updated checkout.\n   */\n  removeLineItems(checkoutId, lineIds = []) {\n    const variables = this.inputMapper.removeLineItems(checkoutId, lineIds);\n\n    return this.graphQLClient\n      .send(cartLinesRemoveMutation, variables)\n      .then(handleCartMutation('cartLinesRemove', this.graphQLClient));\n  }\n\n  /**\n   * Replace line items on an existing checkout.\n   *\n   * @example\n   * const checkoutId = 'Z2lkOi8vc2hvcGlmeS9DaGVja291dC9kMTZmM2EzMDM4Yjc4N=';\n   * const lineItems = [{variantId: 'Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC8yOTEwNjAyMjc5Mg==', quantity: 5}];\n   *\n   * client.checkout.replaceLineItems(checkoutId, lineItems).then((checkout) => {\n   *   // Do something with the updated checkout\n   * });\n   *\n   * @param {String} checkoutId The ID of the checkout to add line items to.\n   * @param {Object[]} lineItems A list of line items to set on the checkout. See the {@link https://help.shopify.com/api/storefront-api/reference/input-object/checkoutlineiteminput|Storefront API reference} for valid input fields for each line item.\n   * @return {Promise|GraphModel} A promise resolving with the updated checkout.\n   */\n  replaceLineItems(checkoutId, lineItems) {\n    return this.fetch(checkoutId)\n      .then((checkout) => {\n        const lineIds = checkout.lineItems.map((lineItem) => lineItem.id);\n\n        return this.removeLineItems(checkoutId, lineIds);\n      })\n      .then(() => {\n        return this.addLineItems(checkoutId, lineItems);\n      });\n  }\n\n  /**\n   * Updates line items on an existing checkout.\n   *\n   * @example\n   * const checkoutId = 'Z2lkOi8vc2hvcGlmeS9DaGVja291dC9kMTZmM2EzMDM4Yjc4N=';\n   * const lineItems = [\n   *   {\n   *     id: 'TViZGE5Y2U1ZDFhY2FiMmM2YT9rZXk9NTc2YjBhODcwNWIxYzg0YjE5ZjRmZGQ5NjczNGVkZGU=',\n   *     quantity: 5,\n   *     variantId: 'Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC8yOTEwNjAyMjc5Mg=='\n   *   }\n   * ];\n   *\n   * client.checkout.updateLineItems(checkoutId, lineItems).then(checkout => {\n   *   // Do something with the updated checkout\n   * });\n   *\n   * @param {String} checkoutId The ID of the checkout to update a line item on.\n   * @param {Object[]} lineItems A list of line item information to update. See the {@link https://help.shopify.com/api/storefront-api/reference/input-object/checkoutlineitemupdateinput|Storefront API reference} for valid input fields for each line item.\n   * @return {Promise|GraphModel} A promise resolving with the updated checkout.\n   */\n  updateLineItems(checkoutId, lineItems) {\n    const variables = this.inputMapper.updateLineItems(checkoutId, lineItems);\n\n    return this.graphQLClient\n      .send(cartLinesUpdateMutation, variables)\n      .then(handleCartMutation('cartLinesUpdate', this.graphQLClient));\n  }\n\n  /**\n   * Updates shipping address on an existing checkout.\n   *\n   * @example\n   * const checkoutId = `'Z2lkOi8vc2hvcGlmeS9DaGVja291dC9kMTZmM2EzMDM4Yjc4N=';\n   * const shippingAddress = {\n   *    address1: 'Chestnut Street 92',\n   *    address2: 'Apartment 2',\n   *    city: 'Louisville',\n   *    company: null,\n   *    country: 'United States',\n   *    firstName: 'Bob',\n   *    lastName: 'Norman',\n   *    phone: '************',\n   *    province: 'Kentucky',\n   *    zip: '40202'\n   *  };\n   *\n   * client.checkout.updateShippingAddress(checkoutId, shippingAddress).then(checkout => {\n   *   // Do something with the updated checkout\n   * });\n   *\n   * @param  {String} checkoutId The ID of the checkout to update shipping address.\n   * @param  {Object} shippingAddress A shipping address.\n   * @return {Promise|GraphModel} A promise resolving with the updated checkout.\n   */\n  updateShippingAddress(checkoutId, shippingAddress) {\n    const variables = this.inputMapper.updateShippingAddress(\n      checkoutId,\n      shippingAddress\n    );\n\n    return this.graphQLClient\n      .send(cartBuyerIdentityUpdateMutation, variables)\n      .then(handleCartMutation('cartBuyerIdentityUpdate', this.graphQLClient));\n  }\n}\n\nexport default CheckoutResource;\n", "/**\n * @namespace ImageHelpers\n */\nexport default {\n\n  /**\n   * Generates the image src for a resized image with maximum dimensions `maxWidth` and `maxHeight`.\n   * Images do not scale up.\n   *\n   * @example\n   * const url = client.image.helpers.imageForSize(product.variants[0].image, {maxWidth: 50, maxHeight: 50});\n   *\n   * @memberof ImageHelpers\n   * @method imageForSize\n   * @param {Object} image The original image model to generate the image src for.\n   * @param {Object} options An options object containing:\n   *  @param {Integer} options.maxWidth The maximum width for the image.\n   *  @param {Integer} options.maxHeight The maximum height for the image.\n   * @return {String} The image src for the resized image.\n   */\n  imageForSize(image, {maxWidth, maxHeight}) {\n    const splitUrl = image.src.split('?');\n    const notQuery = splitUrl[0];\n    const query = splitUrl[1] ? `?${splitUrl[1]}` : '';\n\n    // Use the section before the query\n    const imageTokens = notQuery.split('.');\n\n    // Take the token before the file extension and append the dimensions\n    const imagePathIndex = imageTokens.length - 2;\n\n    imageTokens[imagePathIndex] = `${imageTokens[imagePathIndex]}_${maxWidth}x${maxHeight}`;\n\n    return `${imageTokens.join('.')}${query}`;\n  }\n};\n", "import Resource from './resource';\nimport imageHelpers from './image-helpers';\n\n/**\n * The JS Buy SDK image resource\n * @class\n */\nclass ImageResource extends Resource {\n  get helpers() {\n    return imageHelpers;\n  }\n}\n\nexport default ImageResource;\n", "import GraphQLJSClient from './graphql-client';\nimport Config from './config';\nimport ProductResource from './product-resource';\nimport CollectionResource from './collection-resource';\nimport ShopResource from './shop-resource';\nimport CheckoutResource from './checkout-resource';\nimport ImageResource from './image-resource';\nimport {version} from '../package.json';\n\n// GraphQL\nimport types from '../schema.json';\n\n/**\n * The JS Buy SDK Client.\n * @class\n *\n * @property {ProductResource} product The property under which product fetching methods live.\n * @property {CollectionResource} collection The property under which collection fetching methods live.\n * @property {ShopResource} shop The property under which shop fetching methods live.\n * @property {CartResource} cart The property under which shop fetching and mutating methods live.\n * @property {ImageResource} image The property under which image helper methods live.\n */\nclass Client {\n\n  /**\n   * Primary entry point for building a new Client.\n   */\n  static buildClient(config, fetchFunction) {\n    const newConfig = new Config(config);\n    const client = new Client(newConfig, GraphQLJSClient, fetchFunction);\n\n    client.config = newConfig;\n\n    return client;\n  }\n\n  /**\n   * @constructs Client\n   * @param {Config} config An instance of {@link Config} used to configure the Client.\n   */\n  constructor(config, GraphQLClientClass = GraphQLJSClient, fetchFunction) {\n    const url = `https://${config.domain}/api/2025-01/graphql`;\n\n    const headers = {\n      'X-SDK-Variant': 'javascript',\n      'X-SDK-Version': version,\n      'X-Shopify-Storefront-Access-Token': config.storefrontAccessToken\n    };\n\n    if (config.source) {\n      headers['X-SDK-Variant-Source'] = config.source;\n    }\n\n    const languageHeader = config.language ? config.language : '*';\n\n    headers['Accept-Language'] = languageHeader;\n\n    if (fetchFunction) {\n      headers['Content-Type'] = 'application/json';\n      headers.Accept = 'application/json';\n\n      this.graphQLClient = new GraphQLClientClass(types, {\n        fetcher: function fetcher(graphQLParams) {\n          return fetchFunction(url, {\n            body: JSON.stringify(graphQLParams),\n            method: 'POST',\n            mode: 'cors',\n            headers\n          }).then((response) => response.json());\n        }\n      });\n    } else {\n      this.graphQLClient = new GraphQLClientClass(types, {\n        url,\n        fetcherOptions: {headers}\n      });\n    }\n\n    this.product = new ProductResource(this.graphQLClient);\n    this.collection = new CollectionResource(this.graphQLClient);\n    this.shop = new ShopResource(this.graphQLClient);\n    this.checkout = new CheckoutResource(this.graphQLClient);\n    this.image = new ImageResource(this.graphQLClient);\n  }\n\n  /**\n   * Fetches the next page of models\n   *\n   * @example\n   * client.fetchNextPage(products).then((nextProducts) => {\n   *   // Do something with the products\n   * });\n   *\n   * @param {models} [Array] The paginated set to fetch the next page of\n   * @return {Promise|GraphModel[]} A promise resolving with an array of `GraphModel`s of the type provided.\n   */\n  fetchNextPage(models) {\n    return this.graphQLClient.fetchNextPage(models);\n  }\n}\n\nexport default Client;\n"], "names": ["join", "_len", "arguments", "length", "fields", "Array", "_key", "isObject", "value", "Boolean", "Object", "prototype", "toString", "call", "valueOf", "deepFreezeCopyExcept", "predicate", "structure", "freeze", "keys", "reduce", "copy", "key", "isArray", "map", "item", "schemaForType", "typeBundle", "typeName", "typeSchema", "undefined", "type", "types", "kind", "Error", "classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "createClass", "defineProperties", "target", "props", "i", "descriptor", "enumerable", "configurable", "writable", "defineProperty", "protoProps", "staticProps", "_extends", "assign", "source", "hasOwnProperty", "inherits", "subClass", "superClass", "create", "setPrototypeOf", "__proto__", "possibleConstructorReturn", "self", "ReferenceError", "slicedToArray", "sliceIterator", "arr", "_arr", "_n", "_d", "_e", "_i", "Symbol", "iterator", "_s", "next", "done", "push", "err", "toConsumableArray", "arr2", "from", "VariableDefinition", "name", "defaultValue", "toInputValueString", "defaultValueString", "formatInputValue", "isVariable", "isPrototypeOf", "variable", "Enum", "enumFunction", "<PERSON><PERSON><PERSON>", "get$$1", "String", "JSON", "stringify", "apply", "formatObject", "openChar", "closeChar", "argPairs", "formatArgs", "args", "formatDirectives", "directives", "directiveStrings", "directiveArgs", "arg", "noop", "Profiler", "trackTypeDependency", "trackFieldDependency", "parseFieldCreationArgs", "creationA<PERSON>s", "callback", "options", "selectionSet", "_creationArgs", "_creationArgs2", "SelectionSet", "emptyArgs", "emptyDirectives", "Field", "alias", "responseKey", "aliasPrefix", "Spread", "InlineFragment", "_Spread", "_this", "getPrototypeOf", "FragmentSpread", "_Spread2", "fragmentDefinition", "_this2", "toDefinition", "FragmentDefinition", "spread", "selectionsHaveIdField", "selections", "some", "fieldOrFragment", "implementsNode", "selectionsHaveTypenameField", "indexSelectionsByResponseKey", "assignOrPush", "obj", "unfrozenObject", "acc", "selection", "responseKeys", "selectionsByResponseKey", "for<PERSON>ach", "builderFunction", "SelectionSetBuilder", "unshift", "hasSelectionWithResponseKey", "field", "add", "selection<PERSON>rFieldName", "rest", "concat", "_len2", "_key2", "parsedArgs", "fieldBaseTypes", "fieldBaseType", "inlineFragmentOn", "builderFunctionOrSelectionSet", "addField", "_len3", "_key3", "addConnection", "_len4", "_key4", "_parseFieldCreationAr", "connection", "pageInfo", "edges", "addInlineFragmentOn", "fieldTypeCb", "addFragment", "fragmentSpread", "parseArgs", "variables", "selection<PERSON><PERSON><PERSON><PERSON><PERSON>", "_args", "VariableDefinitions", "variableDefinitions", "Operation", "operationType", "_parseArgs", "queryType", "mutationType", "nameString", "Query", "_Operation", "_ref", "Mutation", "isAnonymous", "operation", "hasAnonymousOperations", "operations", "hasDuplicateOperationNames", "names", "hasDuplicates", "index", "indexOf", "extractOperation", "Function", "bind", "isInvalidOperationCombination", "fragmentNameIsNotUnique", "existingDefinitions", "definition", "Document", "definitions", "addOperation", "<PERSON><PERSON><PERSON><PERSON>", "addMutation", "defineFragment", "onType", "fragmentDefinitions", "fragment", "filter", "GraphModel", "attrs", "ClassRegistry", "classStore", "registerClassForType", "constructor", "unregisterClassForType", "classForType", "isValue", "isNodeContext", "context", "isConnection", "endsWith", "nearestNode", "parent", "contextsFromRoot", "contextsFromNearestNode", "initializeDocumentAndVars", "currentContext", "<PERSON><PERSON><PERSON><PERSON>", "lastIn<PERSON>hain", "first", "firstVar", "find", "document", "addNextFieldTo", "currentSelection", "path", "cursor", "nextContext", "shift", "newSelection", "edgesField", "nodeField", "after", "collectFragments", "nextPageQueryAndPath", "nearestNodeContext", "_document$definitions", "nodeType", "nodeId", "responseData", "id", "_initializeDocumentAn", "_initializeDocumentAn2", "root", "node", "slice", "fragments", "_document$definitions2", "_initializeDocumentAn3", "_initializeDocumentAn4", "hasNextPage$1", "edge", "hasNextPage", "hasPreviousPage", "transformConnections", "variableValues", "DecodingContext", "contextForObjectProperty", "nestedSelections", "nextSelection", "contextForArrayItem", "decodeArrayItems", "transformers", "decodeContext", "decodeObjectValues", "runTransformers", "transformer", "generateRefetchQueries", "refetch<PERSON><PERSON>y", "transformPojosToClassesWithRegistry", "classRegistry", "transformPojosToClasses", "<PERSON><PERSON>", "transformScalars", "recordTypeInformation", "_context$selection$se", "__typename", "defaultTransformers", "_ref$classRegistry", "decode", "httpFetcher", "url", "fetcher", "graphQLParams", "headers", "fetch", "then", "response", "contentType", "get", "json", "text", "paginatedModels", "Client", "fetcherOptions", "_ref$registry", "registry", "query", "mutation", "send", "request", "otherProperties", "operationOrDocument", "operationName", "documentOperation", "data", "model", "fetchNextPage", "nodeOrNodes", "_node$nextPageQueryAn", "_node$nextPageQueryAn2", "object", "fetchAllPages", "_ref2", "pageSize", "_ref3", "pages", "Promise", "resolve", "refetch", "_ref4", "variable$$1", "_enum", "Config", "deprecatedProperties", "warn", "requiredProperties", "apiVersion", "language", "InputMapper", "input", "cartInput", "presentmentCurrencyCode", "lineItems", "lines", "lineItem", "merchandiseId", "variantId", "note", "email", "buyerIdentity", "shippingAddress", "deliveryAddressPreferences", "deliveryAddress", "customAttributes", "attributes", "countryCode", "allowPartialAddresses", "checkoutId", "cartAttributesUpdateInput", "cartNoteUpdateInput", "cartId", "cartBuyerIdentityInput", "mapLineItemToLine", "discountCodes", "flat", "giftCardCodes", "appliedGiftCardId", "lineItemIds", "lineIds", "address1", "address2", "city", "company", "country", "firstName", "lastName", "phone", "zip", "province", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "line", "quantity", "Resource", "client", "graphQLClient", "inputMapper", "defaultErrors", "message", "defaultResolver", "split", "errors", "reject", "result", "ref", "_", "fetchResourcesForProducts", "productOrProduct", "products", "all", "promiseAcc", "product", "images", "variants", "paginateProductConnectionsAndResolve", "paginateCollectionsProductConnectionsAndResolve", "collectionOrCollections", "collections", "collection", "variant", "selectedOptions", "every", "selectedOption", "ProductResource", "productConnectionQuery", "productNodeQuery", "ids", "productNodesQuery", "handle", "productByHandleQuery", "sortKey", "reverse", "productId", "productRecommendationsQuery", "productHelpers", "CollectionResource", "collectionConnectionQuery", "productsFirst", "collectionConnectionWithProductsQuery", "collectionNodeQuery", "collectionNodeWithProductsQuery", "collectionByHandleQuery", "ShopResource", "shopQuery", "shopPolicyQuery", "getDiscountAllocationId", "discountAllocation", "discountApp", "discountApplication", "discountId", "code", "title", "getDiscountApplicationId", "convertToCheckoutDiscountApplicationType", "cartLineItems", "cartOrderLevelDiscountAllocations", "discountAllocations", "j", "allocation", "newDiscountApplication", "newAllocation", "groupOrderLevelDiscountAllocationsByDiscountId", "cartDiscountAllocations", "toLowerCase", "set", "Map", "findLineIdForEachOrderLevelDiscountAllocation", "cartLines", "orderLevelDiscountAllocations", "discountIdToDiscountAllocationsMap", "allocations", "sort", "second", "discountedAmount", "amount", "sortedCartLineItems", "cost", "totalAmount", "values", "flatMap", "discountMapper", "cartDiscountCodes", "hasDiscountAllocations", "targetType", "shippingDiscountAllocations", "cartLinesWithAllDiscountAllocations", "mergeCartOrderLevelDiscountAllocationsToCartLineDiscountAllocations", "discountIdToDiscountApplicationMap", "generateDiscountApplications", "orderLevelDiscountAllocationsForLines", "lineItemId", "orderLevelDiscountAllocationsForLine", "mergedDiscountAllocations", "createCheckoutDiscountApplicationFromCartDiscountAllocation", "has", "existingDiscountApplication", "Number", "toFixed", "currencyCode", "targetSelection", "allocationMethod", "discountCode", "applicable", "getVariantType", "getLineItemType", "getDiscountAllocationType", "mapVariant", "merchandise", "productWithoutTitle", "priceV2", "price", "compareAtPriceV2", "compareAtPrice", "mapDiscountAllocations", "discountApplications", "application", "mapLineItems", "skipDiscounts", "error", "mapDiscountsAndLines", "cart", "cartLinesWithDiscounts", "discountMappingResult", "discountMappingFailed", "mappedLines", "linesWithClearedDiscounts", "lineCopy", "UNSUPPORTED_FIELDS", "mapCartPayload", "totalTaxAmount", "totalDutyAmount", "checkoutChargeAmount", "appliedGiftCards", "totalPrice", "calculateTotalPrice", "subtotalPrice", "calculateSubtotalPrice", "webUrl", "checkoutUrl", "URL", "searchParams", "checkoutPayload", "createdAt", "getDefaultMoneyObject", "updatedAt", "normalizeCartMoneyFieldDecimalPlaces", "checkout", "percentage", "discountApplicationAmount", "shouldReturnWithOneDecimalPlace", "convertToOneDecimalPlace", "subtotalPriceV2", "totalPriceV2", "includes", "currencyDecimals", "stringAmount", "parseFloat", "giftCardTotal", "presentmentAmountUsed", "dutyAmount", "taxAmount", "CartErrorCodeToCheckoutErrorCode", "CartWarningCodeToCheckoutErrorCode", "userErrorsMapper", "userErrors", "warningsMapper", "warnings", "checkoutUserErrorsMapper", "hasUserErrors", "hasWarnings", "checkoutUserErrors", "checkoutWarnings", "handleCartMutation", "mutationRoot<PERSON>ey", "rootData", "rootModel", "CheckoutResource", "cart<PERSON>ode<PERSON>uery", "cartCreateMutation", "updateAttributes", "promise", "updateNote", "cartNoteUpdateMutation", "cartAttributesUpdateMutation", "updateEmail", "cartBuyerIdentityUpdateMutation", "addLineItems", "cartLinesAddMutation", "existingCodes", "addDiscount", "cartDiscountCodesUpdateMutation", "removeDiscount", "addGiftCards", "cartGiftCardCodesUpdateMutation", "removeGiftCard", "cartGiftCardCodesRemoveMutation", "removeLineItems", "cartLinesRemoveMutation", "updateLineItems", "cartLinesUpdateMutation", "updateShippingAddress", "image", "max<PERSON><PERSON><PERSON>", "maxHeight", "splitUrl", "src", "not<PERSON><PERSON>y", "imageTokens", "imagePathIndex", "ImageResource", "imageHelpers", "config", "fetchFunction", "newConfig", "GraphQLJSClient", "GraphQLClientClass", "domain", "version", "storefrontAccessToken", "languageHeader", "Accept", "shop", "models"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;;;;;;;;;;GAwBA,SAASA,IAAT,GAAgB;QACT,IAAIC,OAAOC,UAAUC,MAArB,EAA6BC,SAASC,MAAMJ,IAAN,CAAtC,EAAmDK,OAAO,CAA/D,EAAkEA,OAAOL,IAAzE,EAA+EK,MAA/E,CAAuF;eAC9EA,IAAP,CAAA,GAAeJ,SAAAA,CAAUI,IAAV,CAAf;;WAGKF,OAAOJ,IAAP,CAAY,GAAZ,CAAP;;AAGF,SAASO,QAAT,CAAkBC,KAAlB,EAAyB;WAChBC,QAAQD,KAAR,KAAkBE,OAAOC,SAAP,CAAiBC,QAAjB,CAA0BC,IAA1B,CAA+BL,MAAMM,OAAN,EAA/B,MAAoD,iBAA7E;;AAGF,SAASC,oBAAT,CAA8BC,SAA9B,EAAyCC,SAAzC,EAAoD;QAC9CD,UAAUC,SAAV,CAAJ,EAA0B;eACjBA,SAAP;KADF,MAEO,IAAIV,SAASU,SAAT,CAAJ,EAAyB;eACvBP,OAAOQ,MAAP,CAAcR,OAAOS,IAAP,CAAYF,SAAZ,EAAuBG,MAAvB,CAA8B,SAAUC,IAAV,EAAgBC,GAAhB,EAAqB;iBACjEA,GAAL,CAAA,GAAYP,qBAAqBC,SAArB,EAAgCC,SAAAA,CAAUK,GAAV,CAAhC,CAAZ;mBAEOD,IAAP;SAHmB,EAIlB,CAAA,CAJkB,CAAd,CAAP;KADK,MAMA,IAAIhB,MAAMkB,OAAN,CAAcN,SAAd,CAAJ,EAA8B;eAC5BP,OAAOQ,MAAP,CAAcD,UAAUO,GAAV,CAAc,SAAUC,IAAV,EAAgB;mBAC1CV,qBAAqBC,SAArB,EAAgCS,IAAhC,CAAP;SADmB,CAAd,CAAP;KADK,MAIA;eACER,SAAP;;;AAIJ,SAASS,aAAT,CAAuBC,UAAvB,EAAmCC,QAAnC,EAA6C;QACvCC,aAAa3B,UAAUC,MAAV,GAAmB,CAAnB,IAAwBD,SAAAA,CAAU,CAAV,CAAA,KAAiB4B,SAAzC,GAAqD5B,SAAAA,CAAU,CAAV,CAArD,GAAoE,IAArF;QAEI6B,OAAOJ,WAAWK,KAAX,CAAiBJ,QAAjB,CAAX;QAEIG,IAAJ,EAAU;eACDA,IAAP;KADF,MAEO,IAAIF,cAAcA,WAAWI,IAAX,KAAoB,WAAtC,EAAmD;eACjDJ,UAAP;;UAGI,IAAIK,KAAJ,CAAU,gBAAgBN,QAAhB,GAA2B,kBAArC,CAAN;;AAGF,IAAIO,iBAAiB,SAAjBA,cAAiB,CAAUC,QAAV,EAAoBC,WAApB,EAAiC;QAChD,CAAA,CAAED,oBAAoBC,WAAtB,CAAJ,EAAwC;cAChC,IAAIC,SAAJ,CAAc,mCAAd,CAAN;;CAFJ;AAMA,IAAIC,cAAc,YAAY;aACnBC,gBAAT,CAA0BC,MAA1B,EAAkCC,KAAlC,EAAyC;YAClC,IAAIC,IAAI,CAAb,EAAgBA,IAAID,MAAMvC,MAA1B,EAAkCwC,GAAlC,CAAuC;gBACjCC,aAAaF,KAAAA,CAAMC,CAAN,CAAjB;uBACWE,UAAX,GAAwBD,WAAWC,UAAX,IAAyB,KAAjD;uBACWC,YAAX,GAA0B,IAA1B;gBACI,WAAWF,UAAf,EAA2BA,WAAWG,QAAX,GAAsB,IAAtB;mBACpBC,cAAP,CAAsBP,MAAtB,EAA8BG,WAAWtB,GAAzC,EAA8CsB,UAA9C;;;WAIG,SAAUP,WAAV,EAAuBY,UAAvB,EAAmCC,WAAnC,EAAgD;YACjDD,UAAJ,EAAgBT,iBAAiBH,YAAY1B,SAA7B,EAAwCsC,UAAxC;YACZC,WAAJ,EAAiBV,iBAAiBH,WAAjB,EAA8Ba,WAA9B;eACVb,WAAP;KAHF;CAXgB,EAAlB;AAwBA,IAAIc,WAAWzC,OAAO0C,MAAP,IAAiB,SAAUX,MAAV,EAAkB;QAC3C,IAAIE,IAAI,CAAb,EAAgBA,IAAIzC,UAAUC,MAA9B,EAAsCwC,GAAtC,CAA2C;YACrCU,SAASnD,SAAAA,CAAUyC,CAAV,CAAb;YAEK,IAAIrB,GAAT,IAAgB+B,MAAhB,CAAwB;gBAClB3C,OAAOC,SAAP,CAAiB2C,cAAjB,CAAgCzC,IAAhC,CAAqCwC,MAArC,EAA6C/B,GAA7C,CAAJ,EAAuD;uBAC9CA,GAAP,CAAA,GAAc+B,MAAAA,CAAO/B,GAAP,CAAd;;;;WAKCmB,MAAP;CAXF;AAgBA,IAAIc,WAAW,SAAXA,QAAW,CAAUC,QAAV,EAAoBC,UAApB,EAAgC;QACzC,OAAOA,UAAP,KAAsB,UAAtB,IAAoCA,eAAe,IAAvD,EAA6D;cACrD,IAAInB,SAAJ,CAAc,6DAAA,CAAA,OAAoEmB,UAApE,KAAA,cAAA,cAAA,QAAoEA,UAApE,CAAA,CAAd,CAAN;;aAGO9C,SAAT,GAAqBD,OAAOgD,MAAP,CAAcD,cAAcA,WAAW9C,SAAvC,EAAkD;qBACxD;mBACJ6C,QADI;wBAEC,KAFD;sBAGD,IAHC;0BAIG;;KALG,CAArB;QAQIC,UAAJ,EAAgB/C,OAAOiD,cAAP,GAAwBjD,OAAOiD,cAAP,CAAsBH,QAAtB,EAAgCC,UAAhC,CAAxB,GAAsED,SAASI,SAAT,GAAqBH,UAA3F;CAblB;AA0BA,IAAII,4BAA4B,SAA5BA,yBAA4B,CAAUC,IAAV,EAAgBjD,IAAhB,EAAsB;QAChD,CAACiD,IAAL,EAAW;cACH,IAAIC,cAAJ,CAAmB,2DAAnB,CAAN;;WAGKlD,QAAAA,CAAS,CAAA,OAAOA,IAAP,KAAA,cAAA,cAAA,QAAOA,IAAP,CAAA,MAAgB,QAAhB,IAA4B,OAAOA,IAAP,KAAgB,UAArD,IAAmEA,IAAnE,GAA0EiD,IAAjF;CALF;AAYA,IAAIE,gBAAgB,YAAY;aACrBC,aAAT,CAAuBC,GAAvB,EAA4BvB,CAA5B,EAA+B;YACzBwB,OAAO,EAAX;YACIC,KAAK,IAAT;YACIC,KAAK,KAAT;YACIC,KAAKxC,SAAT;YAEI;gBACG,IAAIyC,KAAKL,GAAAA,CAAIM,OAAOC,QAAX,CAAA,EAAT,EAAiCC,EAAtC,EAA0C,CAAA,CAAEN,KAAK,CAACM,KAAKH,GAAGI,IAAH,EAAN,EAAiBC,IAAxB,CAA1C,EAAyER,KAAK,IAA9E,CAAoF;qBAC7ES,IAAL,CAAUH,GAAGlE,KAAb;oBAEImC,KAAKwB,KAAKhE,MAAL,KAAgBwC,CAAzB,EAA4B;;SAJhC,CAME,OAAOmC,GAAP,EAAY;iBACP,IAAL;iBACKA,GAAL;SARF,QASU;gBACJ;oBACE,CAACV,EAAD,IAAOG,EAAAA,CAAG,QAAH,CAAX,EAAyBA,EAAAA,CAAG,QAAH,CAAA;aAD3B,QAEU;oBACJF,EAAJ,EAAQ,MAAMC,EAAN;;;eAILH,IAAP;;WAGK,SAAUD,GAAV,EAAevB,CAAf,EAAkB;YACnBtC,MAAMkB,OAAN,CAAc2C,GAAd,CAAJ,EAAwB;mBACfA,GAAP;SADF,MAEO,IAAIM,OAAOC,QAAP,IAAmB/D,OAAOwD,GAAP,CAAvB,EAAoC;mBAClCD,cAAcC,GAAd,EAAmBvB,CAAnB,CAAP;SADK,MAEA;kBACC,IAAIL,SAAJ,CAAc,sDAAd,CAAN;;KANJ;CA3BkB,EAApB;AAkDA,IAAIyC,oBAAoB,SAApBA,iBAAoB,CAAUb,GAAV,EAAe;QACjC7D,MAAMkB,OAAN,CAAc2C,GAAd,CAAJ,EAAwB;YACjB,IAAIvB,IAAI,CAAR,EAAWqC,OAAO3E,MAAM6D,IAAI/D,MAAV,CAAvB,EAA0CwC,IAAIuB,IAAI/D,MAAlD,EAA0DwC,GAA1D,CAAA;iBAAoEA,CAAL,CAAA,GAAUuB,GAAAA,CAAIvB,CAAJ,CAAV;;QAE/D,OAAOqC,IAAP;KAHF,MAIO;eACE3E,MAAM4E,IAAN,CAAWf,GAAX,CAAP;;CANJ;AAUA,IAAIgB,qBAAqB,YAAY;;;;;;;;eAU1BA,kBAAT,CAA4BC,IAA5B,EAAkCpD,IAAlC,EAAwCqD,YAAxC,EAAsD;uBACrC,IAAf,EAAqBF,kBAArB;aAEKC,IAAL,GAAYA,IAAZ;aACKpD,IAAL,GAAYA,IAAZ;aACKqD,YAAL,GAAoBA,YAApB;eACOlE,MAAP,CAAc,IAAd;;;;;;kBAUUgE,kBAAZ,EAAgC;QAAC;iBAC1B,oBAD0B;mBAExB,SAASG,kBAAT,GAA8B;uBAC5B,MAAM,IAAA,CAAKF,IAAlB;;SAH4B;QAY7B;iBACI,UADJ;mBAEM,SAASvE,QAAT,GAAoB;oBACrB0E,qBAAqB,IAAA,CAAKF,YAAL,GAAoB,QAAQG,iBAAiB,IAAA,CAAKH,YAAtB,CAA5B,GAAkE,EAA3F;uBAEO,MAAM,IAAA,CAAKD,IAAX,GAAkB,GAAlB,GAAwB,IAAA,CAAKpD,IAA7B,GAAoCuD,kBAA3C;;SAjB4B;KAAhC;WAoBOJ,kBAAP;CA9CuB,EAAzB;AAiDA,SAASM,UAAT,CAAoBhF,KAApB,EAA2B;WAClB0E,mBAAmBvE,SAAnB,CAA6B8E,aAA7B,CAA2CjF,KAA3C,CAAP;;AAGF,SAASkF,QAAT,CAAkBP,IAAlB,EAAwBpD,IAAxB,EAA8BqD,YAA9B,EAA4C;WACnC,IAAIF,kBAAJ,CAAuBC,IAAvB,EAA6BpD,IAA7B,EAAmCqD,YAAnC,CAAP;;AAGF,IAAIO,OAAO,YAAY;;;;;;eAQZA,IAAT,CAAcrE,GAAd,EAAmB;uBACF,IAAf,EAAqBqE,IAArB;aAEKrE,GAAL,GAAWA,GAAX;;;;;;kBAUUqE,IAAZ,EAAkB;QAAC;iBACZ,UADY;mBAEV,SAAS/E,QAAT,GAAoB;uBAClB,IAAA,CAAKU,GAAZ;;SAHc;QAKf;iBACI,SADJ;mBAEM,SAASR,OAAT,GAAmB;uBACjB,IAAA,CAAKQ,GAAL,CAASR,OAAT,EAAP;;SARc;KAAlB;WAWO6E,IAAP;CAhCS,EAAX;AAmCA,IAAIC,eAAgB,SAAhBA,YAAgB,CAAUtE,GAAV,EAAe;WAC1B,IAAIqE,IAAJ,CAASrE,GAAT,CAAP;CADF;AAIA,IAAIuE,SAAS,YAAY;aACdA,MAAT,CAAgBrF,KAAhB,EAAuB;uBACN,IAAf,EAAqBqF,MAArB;aAEKrF,KAAL,GAAaA,KAAb;;gBAGUqF,MAAZ,EAAoB;QAAC;iBACd,UADc;mBAEZ,SAASjF,QAAT,GAAoB;uBAClB,IAAA,CAAKJ,KAAL,CAAWI,QAAX,EAAP;;SAHgB;QAKjB;iBACI,SADJ;mBAEM,SAASE,OAAT,GAAmB;uBACjB,IAAA,CAAKN,KAAL,CAAWM,OAAX,EAAP;;SARgB;QAUjB;iBACI,WADJ;iBAEI,SAASgF,MAAT,GAAkB;uBACd,IAAA,CAAKtF,KAAZ;;SAbgB;KAApB;WAgBOqF,MAAP;CAvBW,EAAb;AA0BA,SAASN,gBAAT,CAA0B/E,KAA1B,EAAiC;QAC3B0E,mBAAmBvE,SAAnB,CAA6B8E,aAA7B,CAA2CjF,KAA3C,CAAJ,EAAuD;eAC9CA,MAAM6E,kBAAN,EAAP;KADF,MAEO,IAAIM,KAAKhF,SAAL,CAAe8E,aAAf,CAA6BjF,KAA7B,CAAJ,EAAyC;eACvCuF,OAAOvF,KAAP,CAAP;KADK,MAEA,IAAIqF,OAAOlF,SAAP,CAAiB8E,aAAjB,CAA+BjF,KAA/B,CAAJ,EAA2C;eACzCwF,KAAKC,SAAL,CAAezF,MAAMM,OAAN,EAAf,CAAP;KADK,MAEA,IAAIT,MAAMkB,OAAN,CAAcf,KAAd,CAAJ,EAA0B;eACxB,MAAMR,KAAKkG,KAAL,CAAWpE,SAAX,EAAsBiD,kBAAkBvE,MAAMgB,GAAN,CAAU+D,gBAAV,CAAlB,CAAtB,CAAN,GAA8E,GAArF;KADK,MAEA,IAAIhF,SAASC,KAAT,CAAJ,EAAqB;eACnB2F,aAAa3F,KAAb,EAAoB,GAApB,EAAyB,GAAzB,CAAP;KADK,MAEA;eACEwF,KAAKC,SAAL,CAAezF,KAAf,CAAP;;;AAIJ,SAAS2F,YAAT,CAAsB3F,KAAtB,EAA6B;QACvB4F,WAAWlG,UAAUC,MAAV,GAAmB,CAAnB,IAAwBD,SAAAA,CAAU,CAAV,CAAA,KAAiB4B,SAAzC,GAAqD5B,SAAAA,CAAU,CAAV,CAArD,GAAoE,EAAnF;QACImG,YAAYnG,UAAUC,MAAV,GAAmB,CAAnB,IAAwBD,SAAAA,CAAU,CAAV,CAAA,KAAiB4B,SAAzC,GAAqD5B,SAAAA,CAAU,CAAV,CAArD,GAAoE,EAApF;QAEIoG,WAAW5F,OAAOS,IAAP,CAAYX,KAAZ,EAAmBgB,GAAnB,CAAuB,SAAUF,GAAV,EAAe;eAC5CA,MAAM,IAAN,GAAaiE,iBAAiB/E,KAAAA,CAAMc,GAAN,CAAjB,CAApB;KADa,CAAf;WAIO,KAAK8E,QAAL,GAAgBpG,KAAKkG,KAAL,CAAWpE,SAAX,EAAsBiD,kBAAkBuB,QAAlB,CAAtB,CAAhB,GAAqED,SAA5E;;AAGF,SAASE,UAAT,CAAoBC,IAApB,EAA0B;QACpB,CAAC9F,OAAOS,IAAP,CAAYqF,IAAZ,EAAkBrG,MAAvB,EAA+B;eACtB,EAAP;;WAGK,OAAOgG,aAAaK,IAAb,CAAP,GAA4B,GAAnC;;AAGF,SAASC,gBAAT,CAA0BC,UAA1B,EAAsC;QAChC,CAAChG,OAAOS,IAAP,CAAYuF,UAAZ,EAAwBvG,MAA7B,EAAqC;eAC5B,EAAP;;QAGEwG,mBAAmBjG,OAAOS,IAAP,CAAYuF,UAAZ,EAAwBlF,GAAxB,CAA4B,SAAUF,GAAV,EAAe;YAC5DsF,gBAAgBF,UAAAA,CAAWpF,GAAX,CAApB;YACIuF,MAAMD,iBAAiBlG,OAAOS,IAAP,CAAYyF,aAAZ,EAA2BzG,MAA5C,GAAqD,MAAMgG,aAAaS,aAAb,CAAN,GAAoC,GAAzF,GAA+F,EAAzG;eAEO,MAAMtF,GAAN,GAAYuF,GAAnB;KAJqB,CAAvB;WAOO,MAAM7G,KAAKkG,KAAL,CAAWpE,SAAX,EAAsBiD,kBAAkB4B,gBAAlB,CAAtB,CAAb;;;AAIF,IAAIG,OAAQ,SAARA,IAAQ,GAAY,CAAA,CAAxB;AAEA,IAAIC,WAAW;yBACQD,IADR;0BAESA;CAFxB;AAKA,IAAIE,sBAAsBD,SAASC,mBAAnC;AACA,IAAIC,uBAAuBF,SAASE,oBAApC;AAGA,SAASC,sBAAT,CAAgCC,YAAhC,EAA8C;QACxCC,WAAWN,IAAf;QACIO,UAAU,CAAA,CAAd;QACIC,eAAe,IAAnB;QAEIH,aAAahH,MAAb,KAAwB,CAA5B,EAA+B;YACzB,OAAOgH,YAAAA,CAAa,CAAb,CAAP,KAA2B,UAA/B,EAA2C;gBACrCI,gBAAgBvD,cAAcmD,YAAd,EAA4B,CAA5B,CAApB;sBAEUI,aAAAA,CAAc,CAAd,CAAV;uBACWA,aAAAA,CAAc,CAAd,CAAX;SAJF,MAKO;gBACDC,iBAAiBxD,cAAcmD,YAAd,EAA4B,CAA5B,CAArB;sBAEUK,cAAAA,CAAe,CAAf,CAAV;2BACeA,cAAAA,CAAe,CAAf,CAAf;;KAVJ,MAYO,IAAIL,aAAahH,MAAb,KAAwB,CAA5B,EAA+B;;;;YAIhCsH,aAAa9G,SAAb,CAAuB8E,aAAvB,CAAqC0B,YAAAA,CAAa,CAAb,CAArC,CAAJ,EAA2D;2BAC1CA,YAAAA,CAAa,CAAb,CAAf;SADF,MAEO,IAAI,OAAOA,YAAAA,CAAa,CAAb,CAAP,KAA2B,UAA/B,EAA2C;uBACrCA,YAAAA,CAAa,CAAb,CAAX;SADK,MAEA;sBACKA,YAAAA,CAAa,CAAb,CAAV;;;WAIG;QAAEE,SAASA,OAAX;QAAoBC,cAAcA,YAAlC;QAAgDF,UAAUA,QAA1D;IAAA,CAAP;;AAGF,IAAIM,YAAYhH,OAAOQ,MAAP,CAAc,CAAA,CAAd,CAAhB;AACA,IAAIyG,kBAAkBjH,OAAOQ,MAAP,CAAc,CAAA,CAAd,CAAtB;AAEA,IAAI0G,QAAQ,YAAY;;;;;;;;;;;;eAcbA,KAAT,CAAezC,IAAf,EAAqBkC,OAArB,EAA8BC,YAA9B,EAA4C;uBAC3B,IAAf,EAAqBM,KAArB;aAEKzC,IAAL,GAAYA,IAAZ;aACK0C,KAAL,GAAaR,QAAQQ,KAAR,IAAiB,IAA9B;aACKC,WAAL,GAAmB,IAAA,CAAKD,KAAL,IAAc,IAAA,CAAK1C,IAAtC;aACKqB,IAAL,GAAYa,QAAQb,IAAR,GAAezF,qBAAqByE,UAArB,EAAiC6B,QAAQb,IAAzC,CAAf,GAAgEkB,SAA5E;aACKhB,UAAL,GAAkBW,QAAQX,UAAR,GAAqB3F,qBAAqByE,UAArB,EAAiC6B,QAAQX,UAAzC,CAArB,GAA4EiB,eAA9F;aACKL,YAAL,GAAoBA,YAApB;eACOpG,MAAP,CAAc,IAAd;;;;;;kBAUU0G,KAAZ,EAAmB;QAAC;iBACb,UADa;mBAEX,SAAShH,QAAT,GAAoB;oBACrBmH,cAAc,IAAA,CAAKF,KAAL,GAAa,IAAA,CAAKA,KAAL,GAAa,IAA1B,GAAiC,EAAnD;uBAEO,KAAKE,WAAL,GAAmB,IAAA,CAAK5C,IAAxB,GAA+BoB,WAAW,IAAA,CAAKC,IAAhB,CAA/B,GAAuDC,iBAAiB,IAAA,CAAKC,UAAtB,CAAvD,GAA2F,IAAA,CAAKY,YAAvG;;SALe;KAAnB;WAQOM,KAAP;CAzCU,EAAZ;;AA6CA,IAAII,SAAS,SAASA,MAAT,GAAkB;mBACd,IAAf,EAAqBA,MAArB;CADF;AAIA,IAAIC,iBAAiB,SAAUC,OAAV,EAAmB;aAC7BD,cAAT,EAAyBC,OAAzB;;;;;;;eASSD,cAAT,CAAwBrG,QAAxB,EAAkC0F,YAAlC,EAAgD;uBAC/B,IAAf,EAAqBW,cAArB;YAEIE,QAAQtE,0BAA0B,IAA1B,EAAgC,CAACoE,eAAerE,SAAf,IAA4BlD,OAAO0H,cAAP,CAAsBH,cAAtB,CAA7B,EAAoEpH,IAApE,CAAyE,IAAzE,CAAhC,CAAZ;cAEMe,QAAN,GAAiBA,QAAjB;cACM0F,YAAN,GAAqBA,YAArB;eACOpG,MAAP,CAAciH,KAAd;eACOA,KAAP;;;;;;kBAUUF,cAAZ,EAA4B;QAAC;iBACtB,UADsB;mBAEpB,SAASrH,QAAT,GAAoB;uBAClB,YAAY,IAAA,CAAKgB,QAAjB,GAA4B,IAAA,CAAK0F,YAAxC;;SAHwB;KAA5B;WAMOW,cAAP;CAlCmB,CAmCnBD,MAnCmB,CAArB;AAqCA,IAAIK,iBAAiB,SAAUC,QAAV,EAAoB;aAC9BD,cAAT,EAAyBC,QAAzB;;;;;;eAQSD,cAAT,CAAwBE,kBAAxB,EAA4C;uBAC3B,IAAf,EAAqBF,cAArB;YAEIG,SAAS3E,0BAA0B,IAA1B,EAAgC,CAACwE,eAAezE,SAAf,IAA4BlD,OAAO0H,cAAP,CAAsBC,cAAtB,CAA7B,EAAoExH,IAApE,CAAyE,IAAzE,CAAhC,CAAb;eAEOsE,IAAP,GAAcoD,mBAAmBpD,IAAjC;eACOmC,YAAP,GAAsBiB,mBAAmBjB,YAAzC;eACOpG,MAAP,CAAcsH,MAAd;eACOA,MAAP;;;;;;kBAUUH,cAAZ,EAA4B;QAAC;iBACtB,UADsB;mBAEpB,SAASzH,QAAT,GAAoB;uBAClB,QAAQ,IAAA,CAAKuE,IAApB;;SAHwB;QAKzB;iBACI,cADJ;mBAEM,SAASsD,YAAT,GAAwB;;uBAEtB,IAAIC,kBAAJ,CAAuB,IAAA,CAAKvD,IAA5B,EAAkC,IAAA,CAAKmC,YAAL,CAAkBzF,UAAlB,CAA6BsD,IAA/D,EAAqE,IAAA,CAAKmC,YAA1E,CAAP;;SATwB;KAA5B;WAYOe,cAAP;CAvCmB,CAwCnBL,MAxCmB,CAArB;AA0CA,IAAIU,qBAAqB,YAAY;;;;;;;eAS1BA,kBAAT,CAA4BvD,IAA5B,EAAkCvD,QAAlC,EAA4C0F,YAA5C,EAA0D;uBACzC,IAAf,EAAqBoB,kBAArB;aAEKvD,IAAL,GAAYA,IAAZ;aACKvD,QAAL,GAAgBA,QAAhB;aACK0F,YAAL,GAAoBA,YAApB;aACKqB,MAAL,GAAc,IAAIN,cAAJ,CAAmB,IAAnB,CAAd;eACOnH,MAAP,CAAc,IAAd;;;;;;kBAUUwH,kBAAZ,EAAgC;QAAC;iBAC1B,UAD0B;mBAExB,SAAS9H,QAAT,GAAoB;uBAClB,cAAc,IAAA,CAAKuE,IAAnB,GAA0B,MAA1B,GAAmC,IAAA,CAAKvD,QAAxC,GAAmD,GAAnD,GAAyD,IAAA,CAAK0F,YAArE;;SAH4B;KAAhC;WAMOoB,kBAAP;CAhCuB,EAAzB;AAmCA,SAASE,qBAAT,CAA+BC,UAA/B,EAA2C;WAClCA,WAAWC,IAAX,CAAgB,SAAUC,eAAV,EAA2B;YAC5CnB,MAAMjH,SAAN,CAAgB8E,aAAhB,CAA8BsD,eAA9B,CAAJ,EAAoD;mBAC3CA,gBAAgB5D,IAAhB,KAAyB,IAAhC;SADF,MAEO,IAAI6C,OAAOrH,SAAP,CAAiB8E,aAAjB,CAA+BsD,eAA/B,KAAmDA,gBAAgBzB,YAAhB,CAA6BzF,UAA7B,CAAwCmH,cAA/F,EAA+G;mBAC7GJ,sBAAsBG,gBAAgBzB,YAAhB,CAA6BuB,UAAnD,CAAP;;eAGK,KAAP;KAPK,CAAP;;AAWF,SAASI,2BAAT,CAAqCJ,UAArC,EAAiD;WACxCA,WAAWC,IAAX,CAAgB,SAAUC,eAAV,EAA2B;YAC5CnB,MAAMjH,SAAN,CAAgB8E,aAAhB,CAA8BsD,eAA9B,CAAJ,EAAoD;mBAC3CA,gBAAgB5D,IAAhB,KAAyB,YAAhC;SADF,MAEO,IAAI6C,OAAOrH,SAAP,CAAiB8E,aAAjB,CAA+BsD,eAA/B,KAAmDA,gBAAgBzB,YAAhB,CAA6BzF,UAA7B,CAAwCmH,cAA/F,EAA+G;mBAC7GC,4BAA4BF,gBAAgBzB,YAAhB,CAA6BuB,UAAzD,CAAP;;eAGK,KAAP;KAPK,CAAP;;AAWF,SAASK,4BAAT,CAAsCL,UAAtC,EAAkD;aACvCM,YAAT,CAAsBC,GAAtB,EAA2B9H,GAA3B,EAAgCd,KAAhC,EAAuC;YACjCH,MAAMkB,OAAN,CAAc6H,GAAAA,CAAI9H,GAAJ,CAAd,CAAJ,EAA6B;gBACvBA,GAAJ,CAAA,CAASuD,IAAT,CAAcrE,KAAd;SADF,MAEO;gBACDc,GAAJ,CAAA,GAAW;gBAACd,KAAD;aAAX;;;QAGA6I,iBAAiBR,WAAWzH,MAAX,CAAkB,SAAUkI,GAAV,EAAeC,SAAf,EAA0B;YAC3DA,UAAUzB,WAAd,EAA2B;yBACZwB,GAAb,EAAkBC,UAAUzB,WAA5B,EAAyCyB,SAAzC;SADF,MAEO;gBACDC,eAAe9I,OAAOS,IAAP,CAAYoI,UAAUjC,YAAV,CAAuBmC,uBAAnC,CAAnB;yBAEaC,OAAb,CAAqB,SAAU5B,WAAV,EAAuB;6BAC7BwB,GAAb,EAAkBxB,WAAlB,EAA+ByB,SAA/B;aADF;;eAKKD,GAAP;KAXmB,EAYlB,CAAA,CAZkB,CAArB;WAcOnI,IAAP,CAAYkI,cAAZ,EAA4BK,OAA5B,CAAoC,SAAUpI,GAAV,EAAe;eAC1CJ,MAAP,CAAcmI,cAAAA,CAAe/H,GAAf,CAAd;KADF;WAIOZ,OAAOQ,MAAP,CAAcmI,cAAd,CAAP;;;;IAOF,IAAI5B,eAAe,YAAY;;;;;;;;eAUpBA,YAAT,CAAsB9F,UAAtB,EAAkCI,IAAlC,EAAwC4H,eAAxC,EAAyD;uBACxC,IAAf,EAAqBlC,YAArB;YAGI,OAAO1F,IAAP,KAAgB,QAApB,EAA8B;iBACvBF,UAAL,GAAkBH,cAAcC,UAAd,EAA0BI,IAA1B,CAAlB;SADF,MAEO;iBACAF,UAAL,GAAkBE,IAAlB;;4BAGkB,IAAA,CAAKF,UAAL,CAAgBsD,IAApC;aAEKxD,UAAL,GAAkBA,UAAlB;aACKkH,UAAL,GAAkB,EAAlB;YACIc,eAAJ,EAAqB;;4BAEH,IAAIC,mBAAJ,CAAwB,IAAA,CAAKjI,UAA7B,EAAyC,IAAA,CAAKE,UAA9C,EAA0D,IAAA,CAAKgH,UAA/D,CAAhB;;YAGE,IAAA,CAAKhH,UAAL,CAAgBmH,cAAhB,IAAkC,IAAA,CAAKnH,UAAL,CAAgBsD,IAAhB,KAAyB,MAA/D,EAAuE;gBACjE,CAACyD,sBAAsB,IAAA,CAAKC,UAA3B,CAAL,EAA6C;qBACtCA,UAAL,CAAgBgB,OAAhB,CAAwB,IAAIjC,KAAJ,CAAU,IAAV,EAAgB,CAAA,CAAhB,EAAoB,IAAIH,YAAJ,CAAiB9F,UAAjB,EAA6B,IAA7B,CAApB,CAAxB;;;YAIA,IAAA,CAAKE,UAAL,CAAgBI,IAAhB,KAAyB,WAA7B,EAA0C;gBACpC,CAACgH,4BAA4B,IAAA,CAAKJ,UAAjC,CAAL,EAAmD;qBAC5CA,UAAL,CAAgBgB,OAAhB,CAAwB,IAAIjC,KAAJ,CAAU,YAAV,EAAwB,CAAA,CAAxB,EAA4B,IAAIH,YAAJ,CAAiB9F,UAAjB,EAA6B,QAA7B,CAA5B,CAAxB;;;aAIC8H,uBAAL,GAA+BP,6BAA6B,IAAA,CAAKL,UAAlC,CAA/B;eACO3H,MAAP,CAAc,IAAA,CAAK2H,UAAnB;eACO3H,MAAP,CAAc,IAAd;;;;;;kBAUUuG,YAAZ,EAA0B;QAAC;iBACpB,UADoB;mBAElB,SAAS7G,QAAT,GAAoB;oBACrB,IAAA,CAAKiB,UAAL,CAAgBI,IAAhB,KAAyB,QAAzB,IAAqC,IAAA,CAAKJ,UAAL,CAAgBI,IAAhB,KAAyB,MAAlE,EAA0E;2BACjE,EAAP;iBADF,MAEO;2BACE,QAAQjC,KAAK,IAAA,CAAK6I,UAAV,CAAR,GAAgC,IAAvC;;;SANoB;KAA1B;WAUOpB,YAAP;CA/DiB,EAAnB;AAkEA,IAAImC,sBAAsB,YAAY;;;;;;;eAS3BA,mBAAT,CAA6BjI,UAA7B,EAAyCE,UAAzC,EAAqDgH,UAArD,EAAiE;uBAChD,IAAf,EAAqBe,mBAArB;aAEKjI,UAAL,GAAkBA,UAAlB;aACKE,UAAL,GAAkBA,UAAlB;aACKgH,UAAL,GAAkBA,UAAlB;;gBAGUe,mBAAZ,EAAiC;QAAC;iBAC3B,6BAD2B;mBAEzB,SAASE,2BAAT,CAAqChC,WAArC,EAAkD;uBAChD,IAAA,CAAKe,UAAL,CAAgBC,IAAhB,CAAqB,SAAUiB,KAAV,EAAiB;2BACpCA,MAAMjC,WAAN,KAAsBA,WAA7B;iBADK,CAAP;;SAH6B;QAyB9B;iBACI,KADJ;mBAEM,SAASkC,GAAT,CAAaC,oBAAb,EAAmC;oBACpCV,YAAY,KAAK,CAArB;oBAEI7I,OAAOC,SAAP,CAAiBC,QAAjB,CAA0BC,IAA1B,CAA+BoJ,oBAA/B,MAAyD,iBAA7D,EAAgF;yCACzD,IAAA,CAAKpI,UAAL,CAAgBsD,IAArC,EAA2C8E,oBAA3C;wBAEK,IAAIhK,OAAOC,UAAUC,MAArB,EAA6B+J,OAAO7J,MAAMJ,OAAO,CAAP,GAAWA,OAAO,CAAlB,GAAsB,CAA5B,CAApC,EAAoEK,OAAO,CAAhF,EAAmFA,OAAOL,IAA1F,EAAgGK,MAAhG,CAAwG;6BACjGA,OAAO,CAAZ,CAAA,GAAiBJ,SAAAA,CAAUI,IAAV,CAAjB;;gCAGU,IAAA,CAAKyJ,KAAL,CAAW7D,KAAX,CAAiB,IAAjB,EAAuB;wBAAC+D,oBAAD;qBAAA,CAAuBE,MAAvB,CAA8BD,IAA9B,CAAvB,CAAZ;iBAPF,MAQO;wBACDtC,MAAMjH,SAAN,CAAgB8E,aAAhB,CAA8BwE,oBAA9B,CAAJ,EAAyD;6CAClC,IAAA,CAAKpI,UAAL,CAAgBsD,IAArC,EAA2C8E,qBAAqB9E,IAAhE;;gCAGU8E,oBAAZ;;oBAGEV,UAAUzB,WAAV,IAAyB,IAAA,CAAKgC,2BAAL,CAAiCP,UAAUzB,WAA3C,CAA7B,EAAsF;0BAC9E,IAAI5F,KAAJ,CAAU,+BAA+BqH,UAAUzB,WAAzC,GAAuD,4BAAjE,CAAN;;qBAEGe,UAAL,CAAgBhE,IAAhB,CAAqB0E,SAArB;;SAjD6B;QAmD9B;iBACI,OADJ;mBAEM,SAASQ,KAAT,CAAe5E,IAAf,EAAqB;oBACrB,IAAIiF,QAAQlK,UAAUC,MAAtB,EAA8BgH,eAAe9G,MAAM+J,QAAQ,CAAR,GAAYA,QAAQ,CAApB,GAAwB,CAA9B,CAA7C,EAA+EC,QAAQ,CAA5F,EAA+FA,QAAQD,KAAvG,EAA8GC,OAA9G,CAAuH;iCACxGA,QAAQ,CAArB,CAAA,GAA0BnK,SAAAA,CAAUmK,KAAV,CAA1B;;oBAGEC,aAAapD,uBAAuBC,YAAvB,CAAjB;oBACIE,UAAUiD,WAAWjD,OAAzB,EACID,WAAWkD,WAAWlD,QAD1B;oBAEIE,eAAegD,WAAWhD,YAA9B;oBAGI,CAACA,YAAL,EAAmB;wBACb,CAAC,IAAA,CAAKzF,UAAL,CAAgB0I,cAAhB,CAA+BpF,IAA/B,CAAL,EAA2C;8BACnC,IAAIjD,KAAJ,CAAU,uBAAuBiD,IAAvB,GAA8B,mBAA9B,GAAoD,IAAA,CAAKtD,UAAL,CAAgBsD,IAApE,GAA2E,aAArF,CAAN;;wBAGEqF,gBAAgB9I,cAAc,IAAA,CAAKC,UAAnB,EAA+B,IAAA,CAAKE,UAAL,CAAgB0I,cAAhB,CAA+BpF,IAA/B,CAA/B,CAApB;mCAEe,IAAIsC,YAAJ,CAAiB,IAAA,CAAK9F,UAAtB,EAAkC6I,aAAlC,EAAiDpD,QAAjD,CAAf;;uBAGK,IAAIQ,KAAJ,CAAUzC,IAAV,EAAgBkC,OAAhB,EAAyBC,YAAzB,CAAP;;SA1E6B;QAsF9B;iBACI,kBADJ;mBAEM,SAASmD,gBAAT,CAA0B7I,QAA1B,EAAoC;oBACrC8I,gCAAgCxK,UAAUC,MAAV,GAAmB,CAAnB,IAAwBD,SAAAA,CAAU,CAAV,CAAA,KAAiB4B,SAAzC,GAAqD5B,SAAAA,CAAU,CAAV,CAArD,GAAoE4G,IAAxG;oBAEIQ,eAAe,KAAK,CAAxB;oBAEIG,aAAa9G,SAAb,CAAuB8E,aAAvB,CAAqCiF,6BAArC,CAAJ,EAAyE;mCACxDA,6BAAf;iBADF,MAEO;mCACU,IAAIjD,YAAJ,CAAiB,IAAA,CAAK9F,UAAtB,EAAkCD,cAAc,IAAA,CAAKC,UAAnB,EAA+BC,QAA/B,CAAlC,EAA4E8I,6BAA5E,CAAf;;uBAGK,IAAIzC,cAAJ,CAAmBrG,QAAnB,EAA6B0F,YAA7B,CAAP;;SAnG6B;QAiH9B;iBACI,UADJ;mBAEM,SAASqD,QAAT,CAAkBxF,IAAlB,EAAwB;oBACxB,IAAIyF,QAAQ1K,UAAUC,MAAtB,EAA8BgH,eAAe9G,MAAMuK,QAAQ,CAAR,GAAYA,QAAQ,CAApB,GAAwB,CAA9B,CAA7C,EAA+EC,QAAQ,CAA5F,EAA+FA,QAAQD,KAAvG,EAA8GC,OAA9G,CAAuH;iCACxGA,QAAQ,CAArB,CAAA,GAA0B3K,SAAAA,CAAU2K,KAAV,CAA1B;;qBAGGb,GAAL,CAAS9D,KAAT,CAAe,IAAf,EAAqB;oBAACf,IAAD;iBAAA,CAAOgF,MAAP,CAAchD,YAAd,CAArB;;SAxH6B;QA+I9B;iBACI,eADJ;mBAEM,SAAS2D,aAAT,CAAuB3F,IAAvB,EAA6B;oBAC7B,IAAI4F,QAAQ7K,UAAUC,MAAtB,EAA8BgH,eAAe9G,MAAM0K,QAAQ,CAAR,GAAYA,QAAQ,CAApB,GAAwB,CAA9B,CAA7C,EAA+EC,QAAQ,CAA5F,EAA+FA,QAAQD,KAAvG,EAA8GC,OAA9G,CAAuH;iCACxGA,QAAQ,CAArB,CAAA,GAA0B9K,SAAAA,CAAU8K,KAAV,CAA1B;;oBAGEC,wBAAwB/D,uBAAuBC,YAAvB,CAA5B,EACIE,UAAU4D,sBAAsB5D,OADpC,EAEID,WAAW6D,sBAAsB7D,QAFrC,EAGIE,eAAe2D,sBAAsB3D,YAHzC;qBAKK0C,GAAL,CAAS7E,IAAT,EAAekC,OAAf,EAAwB,SAAU6D,UAAV,EAAsB;+BACjClB,GAAX,CAAe,UAAf,EAA2B,CAAA,CAA3B,EAA+B,SAAUmB,QAAV,EAAoB;iCACxCnB,GAAT,CAAa,aAAb;iCACSA,GAAT,CAAa,iBAAb;qBAFF;+BAIWA,GAAX,CAAe,OAAf,EAAwB,CAAA,CAAxB,EAA4B,SAAUoB,KAAV,EAAiB;8BACrCpB,GAAN,CAAU,QAAV;8BACMW,QAAN,CAAe,MAAf,EAAuB,CAAA,CAAvB,EAA2BrD,gBAAgBF,QAA3C,EAF2C,CAAA,6BAAA;qBAA7C;iBALF;;SA3J6B;QAuL9B;iBACI,qBADJ;mBAEM,SAASiE,mBAAT,CAA6BzJ,QAA7B,EAAuC;oBACxC0J,cAAcpL,UAAUC,MAAV,GAAmB,CAAnB,IAAwBD,SAAAA,CAAU,CAAV,CAAA,KAAiB4B,SAAzC,GAAqD5B,SAAAA,CAAU,CAAV,CAArD,GAAoE4G,IAAtF;qBAEKkD,GAAL,CAAS,IAAA,CAAKS,gBAAL,CAAsB7I,QAAtB,EAAgC0J,WAAhC,CAAT;;SA5L6B;QA0M9B;iBACI,aADJ;mBAEM,SAASC,WAAT,CAAqBC,cAArB,EAAqC;qBACrCxB,GAAL,CAASwB,cAAT;;SA7M6B;KAAjC;WAgNO5B,mBAAP;CAjOwB,EAA1B;AAoOA,SAAS6B,SAAT,CAAmBjF,IAAnB,EAAyB;QACnBrB,OAAO,KAAK,CAAhB;QACIuG,YAAY,KAAK,CAArB;QACIC,uBAAuB,KAAK,CAAhC;QAEInF,KAAKrG,MAAL,KAAgB,CAApB,EAAuB;YACjByL,QAAQ5H,cAAcwC,IAAd,EAAoB,CAApB,CAAZ;eAEOoF,KAAAA,CAAM,CAAN,CAAP;oBACYA,KAAAA,CAAM,CAAN,CAAZ;+BACuBA,KAAAA,CAAM,CAAN,CAAvB;KALF,MAMO,IAAIpF,KAAKrG,MAAL,KAAgB,CAApB,EAAuB;YACxBO,OAAOC,SAAP,CAAiBC,QAAjB,CAA0BC,IAA1B,CAA+B2F,IAAAA,CAAK,CAAL,CAA/B,MAA4C,iBAAhD,EAAmE;mBAC1DA,IAAAA,CAAK,CAAL,CAAP;wBACY,IAAZ;SAFF,MAGO,IAAInG,MAAMkB,OAAN,CAAciF,IAAAA,CAAK,CAAL,CAAd,CAAJ,EAA4B;wBACrBA,IAAAA,CAAK,CAAL,CAAZ;mBACO,IAAP;;+BAGqBA,IAAAA,CAAK,CAAL,CAAvB;KATK,MAUA;+BACkBA,IAAAA,CAAK,CAAL,CAAvB;eACO,IAAP;;WAGK;QAAErB,MAAMA,IAAR;QAAcuG,WAAWA,SAAzB;QAAoCC,sBAAsBA,oBAA1D;IAAA,CAAP;;AAGF,IAAIE,sBAAsB,YAAY;aAC3BA,mBAAT,CAA6BC,mBAA7B,EAAkD;uBACjC,IAAf,EAAqBD,mBAArB;aAEKC,mBAAL,GAA2BA,sBAAsB,EAAA,CAAG3B,MAAH,CAAUpF,kBAAkB+G,mBAAlB,CAAV,CAAtB,GAA0E,EAArG;eACO5K,MAAP,CAAc,IAAA,CAAK4K,mBAAnB;eACO5K,MAAP,CAAc,IAAd;;gBAGU2K,mBAAZ,EAAiC;QAAC;iBAC3B,UAD2B;mBAEzB,SAASjL,QAAT,GAAoB;oBACrB,IAAA,CAAKkL,mBAAL,CAAyB3L,MAAzB,KAAoC,CAAxC,EAA2C;2BAClC,EAAP;;uBAGK,OAAOH,KAAK,IAAA,CAAK8L,mBAAV,CAAP,GAAwC,IAA/C;;SAP6B;KAAjC;WAUOD,mBAAP;CAnBwB,EAA1B;;;;IA4BA,IAAIE,YAAY,YAAY;;;eAKjBA,SAAT,CAAmBpK,UAAnB,EAA+BqK,aAA/B,EAA8C;uBAC7B,IAAf,EAAqBD,SAArB;YAEK,IAAI9L,OAAOC,UAAUC,MAArB,EAA6BqG,OAAOnG,MAAMJ,OAAO,CAAP,GAAWA,OAAO,CAAlB,GAAsB,CAA5B,CAApC,EAAoEK,OAAO,CAAhF,EAAmFA,OAAOL,IAA1F,EAAgGK,MAAhG,CAAwG;iBACjGA,OAAO,CAAZ,CAAA,GAAiBJ,SAAAA,CAAUI,IAAV,CAAjB;;YAGE2L,aAAaR,UAAUjF,IAAV,CAAjB,EACIrB,OAAO8G,WAAW9G,IADtB,EAEIuG,YAAYO,WAAWP,SAF3B,EAGIC,uBAAuBM,WAAWN,oBAHtC;aAKKhK,UAAL,GAAkBA,UAAlB;aACKwD,IAAL,GAAYA,IAAZ;aACK2G,mBAAL,GAA2B,IAAID,mBAAJ,CAAwBH,SAAxB,CAA3B;aACKM,aAAL,GAAqBA,aAArB;YACIA,kBAAkB,OAAtB,EAA+B;iBACxB1E,YAAL,GAAoB,IAAIG,YAAJ,CAAiB9F,UAAjB,EAA6BA,WAAWuK,SAAxC,EAAmDP,oBAAnD,CAApB;iBACK9J,UAAL,GAAkBH,cAAcC,UAAd,EAA0BA,WAAWuK,SAArC,CAAlB;SAFF,MAGO;iBACA5E,YAAL,GAAoB,IAAIG,YAAJ,CAAiB9F,UAAjB,EAA6BA,WAAWwK,YAAxC,EAAsDR,oBAAtD,CAApB;iBACK9J,UAAL,GAAkBH,cAAcC,UAAd,EAA0BA,WAAWwK,YAArC,CAAlB;;eAEKjL,MAAP,CAAc,IAAd;;;;kBAQU6K,SAAZ,EAAuB;QAAC;iBACjB,UADiB;;;;;eASf,SAASnL,QAAT,GAAoB;oBACrBwL,aAAa,IAAA,CAAKjH,IAAL,GAAY,MAAM,IAAA,CAAKA,IAAvB,GAA8B,EAA/C;uBAEO,KAAK,IAAA,CAAK6G,aAAV,GAA0BI,UAA1B,GAAuC,IAAA,CAAKN,mBAA5C,GAAkE,IAAA,CAAKxE,YAA9E;;SAZmB;QAcpB;iBACI,aADJ;iBAEI,SAASxB,MAAT,GAAkB;uBACd,CAAC,IAAA,CAAKX,IAAb;;SAjBmB;KAAvB;WAoBO4G,SAAP;CAxDc,EAAhB;;;;IAgEA,IAAIM,QAAQ,SAAUC,UAAV,EAAsB;aACvBD,KAAT,EAAgBC,UAAhB;;;;;;;;;;eAYSD,KAAT,CAAe1K,UAAf,EAA2B;YACrB4K,IAAJ;uBAEe,IAAf,EAAqBF,KAArB;YAEK,IAAIpM,OAAOC,UAAUC,MAArB,EAA6BqG,OAAOnG,MAAMJ,OAAO,CAAP,GAAWA,OAAO,CAAlB,GAAsB,CAA5B,CAApC,EAAoEK,OAAO,CAAhF,EAAmFA,OAAOL,IAA1F,EAAgGK,MAAhG,CAAwG;iBACjGA,OAAO,CAAZ,CAAA,GAAiBJ,SAAAA,CAAUI,IAAV,CAAjB;;eAGKuD,0BAA0B,IAA1B,EAAgC,CAAC0I,OAAOF,MAAMzI,SAAN,IAAmBlD,OAAO0H,cAAP,CAAsBiE,KAAtB,CAA3B,EAAyDxL,IAAzD,CAA8DqF,KAA9D,CAAoEqG,IAApE,EAA0E;YAAC,IAAD;YAAO5K,UAAP;YAAmB,OAAnB;SAAA,CAA4BwI,MAA5B,CAAmC3D,IAAnC,CAA1E,CAAhC,CAAP;;WAGK6F,KAAP;CAzBU,CA0BVN,SA1BU,CAAZ;;;;IAiCA,IAAIS,WAAW,SAAUF,UAAV,EAAsB;aAC1BE,QAAT,EAAmBF,UAAnB;;;;;;;;;;eAYSE,QAAT,CAAkB7K,UAAlB,EAA8B;YACxB4K,IAAJ;uBAEe,IAAf,EAAqBC,QAArB;YAEK,IAAIvM,OAAOC,UAAUC,MAArB,EAA6BqG,OAAOnG,MAAMJ,OAAO,CAAP,GAAWA,OAAO,CAAlB,GAAsB,CAA5B,CAApC,EAAoEK,OAAO,CAAhF,EAAmFA,OAAOL,IAA1F,EAAgGK,MAAhG,CAAwG;iBACjGA,OAAO,CAAZ,CAAA,GAAiBJ,SAAAA,CAAUI,IAAV,CAAjB;;eAGKuD,0BAA0B,IAA1B,EAAgC,CAAC0I,OAAOC,SAAS5I,SAAT,IAAsBlD,OAAO0H,cAAP,CAAsBoE,QAAtB,CAA9B,EAA+D3L,IAA/D,CAAoEqF,KAApE,CAA0EqG,IAA1E,EAAgF;YAAC,IAAD;YAAO5K,UAAP;YAAmB,UAAnB;SAAA,CAA+BwI,MAA/B,CAAsC3D,IAAtC,CAAhF,CAAhC,CAAP;;WAGKgG,QAAP;CAzBa,CA0BbT,SA1Ba,CAAf;AA4BA,SAASU,WAAT,CAAqBC,SAArB,EAAgC;WACvBA,UAAUD,WAAjB;;AAGF,SAASE,sBAAT,CAAgCC,UAAhC,EAA4C;WACnCA,WAAW9D,IAAX,CAAgB2D,WAAhB,CAAP;;AAGF,SAASI,0BAAT,CAAoCD,UAApC,EAAgD;QAC1CE,QAAQF,WAAWpL,GAAX,CAAe,SAAUkL,SAAV,EAAqB;eACvCA,UAAUvH,IAAjB;KADU,CAAZ;WAIO2H,MAAM1L,MAAN,CAAa,SAAU2L,aAAV,EAAyB5H,IAAzB,EAA+B6H,KAA/B,EAAsC;eACjDD,iBAAiBD,MAAMG,OAAN,CAAc9H,IAAd,MAAwB6H,KAAhD;KADK,EAEJ,KAFI,CAAP;;AAKF,SAASE,gBAAT,CAA0BvL,UAA1B,EAAsCqK,aAAtC,EAAqD;QAC9C,IAAI/L,OAAOC,UAAUC,MAArB,EAA6BqG,OAAOnG,MAAMJ,OAAO,CAAP,GAAWA,OAAO,CAAlB,GAAsB,CAA5B,CAApC,EAAoEK,OAAO,CAAhF,EAAmFA,OAAOL,IAA1F,EAAgGK,MAAhG,CAAwG;aACjGA,OAAO,CAAZ,CAAA,GAAiBJ,SAAAA,CAAUI,IAAV,CAAjB;;QAGEyL,UAAUpL,SAAV,CAAoB8E,aAApB,CAAkCe,IAAAA,CAAK,CAAL,CAAlC,CAAJ,EAAgD;eACvCA,IAAAA,CAAK,CAAL,CAAP;;QAGEwF,kBAAkB,OAAtB,EAA+B;eACtB,IAAA,CAAKmB,SAASxM,SAAT,CAAmByM,IAAnB,CAAwBlH,KAAxB,CAA8BmG,KAA9B,EAAqC;YAAC,IAAD;SAAA,CAAOlC,MAAP,CAAc;YAACxI,UAAD;SAAd,EAA4B6E,IAA5B,CAArC,CAAL,GAAP;KADF,MAEO;eACE,IAAA,CAAK2G,SAASxM,SAAT,CAAmByM,IAAnB,CAAwBlH,KAAxB,CAA8BsG,QAA9B,EAAwC;YAAC,IAAD;SAAA,CAAOrC,MAAP,CAAc;YAACxI,UAAD;SAAd,EAA4B6E,IAA5B,CAAxC,CAAL,GAAP;;;AAIJ,SAAS6G,6BAAT,CAAuCT,UAAvC,EAAmD;QAC7CA,WAAWzM,MAAX,KAAsB,CAA1B,EAA6B;eACpB,KAAP;;WAGKwM,uBAAuBC,UAAvB,KAAsCC,2BAA2BD,UAA3B,CAA7C;;AAGF,SAASU,uBAAT,CAAiCC,mBAAjC,EAAsDpI,IAAtD,EAA4D;WACnDoI,oBAAoBzE,IAApB,CAAyB,SAAU0E,UAAV,EAAsB;eAC7CA,WAAWrI,IAAX,KAAoBA,IAA3B;KADK,CAAP;;AAKF,IAAIsI,WAAW,YAAY;;;;;eAOhBA,QAAT,CAAkB9L,UAAlB,EAA8B;uBACb,IAAf,EAAqB8L,QAArB;aAEK9L,UAAL,GAAkBA,UAAlB;aACK+L,WAAL,GAAmB,EAAnB;;;;;;kBAUUD,QAAZ,EAAsB;QAAC;iBAChB,UADgB;mBAEd,SAAS7M,QAAT,GAAoB;uBAClBZ,KAAK,IAAA,CAAK0N,WAAV,CAAP;;SAHkB;QAmBnB;iBACI,cADJ;mBAEM,SAASC,YAAT,CAAsB3B,aAAtB,EAAqC;oBACrC,IAAI5B,QAAQlK,UAAUC,MAAtB,EAA8BqG,OAAOnG,MAAM+J,QAAQ,CAAR,GAAYA,QAAQ,CAApB,GAAwB,CAA9B,CAArC,EAAuEC,QAAQ,CAApF,EAAuFA,QAAQD,KAA/F,EAAsGC,OAAtG,CAA+G;yBACxGA,QAAQ,CAAb,CAAA,GAAkBnK,SAAAA,CAAUmK,KAAV,CAAlB;;oBAGEqC,YAAYQ,iBAAiBhH,KAAjB,CAAuBpE,SAAvB,EAAkC;oBAAC,IAAA,CAAKH,UAAN;oBAAkBqK,aAAlB;iBAAA,CAAiC7B,MAAjC,CAAwC3D,IAAxC,CAAlC,CAAhB;oBAEI6G,8BAA8B,IAAA,CAAKT,UAAL,CAAgBzC,MAAhB,CAAuBuC,SAAvB,CAA9B,CAAJ,EAAsE;0BAC9D,IAAIxK,KAAJ,CAAU,qEAAV,CAAN;;qBAGGwL,WAAL,CAAiB7I,IAAjB,CAAsB6H,SAAtB;;SAhCkB;QAqDnB;iBACI,UADJ;mBAEM,SAASkB,QAAT,GAAoB;oBACpB,IAAIhD,QAAQ1K,UAAUC,MAAtB,EAA8BqG,OAAOnG,MAAMuK,KAAN,CAArC,EAAmDC,QAAQ,CAAhE,EAAmEA,QAAQD,KAA3E,EAAkFC,OAAlF,CAA2F;yBACpFA,KAAL,CAAA,GAAc3K,SAAAA,CAAU2K,KAAV,CAAd;;qBAGG8C,YAAL,CAAkBzH,KAAlB,CAAwB,IAAxB,EAA8B;oBAAC,OAAD;iBAAA,CAAUiE,MAAV,CAAiB3D,IAAjB,CAA9B;;SA5DkB;QAqFnB;iBACI,aADJ;mBAEM,SAASqH,WAAT,GAAuB;oBACvB,IAAI9C,QAAQ7K,UAAUC,MAAtB,EAA8BqG,OAAOnG,MAAM0K,KAAN,CAArC,EAAmDC,QAAQ,CAAhE,EAAmEA,QAAQD,KAA3E,EAAkFC,OAAlF,CAA2F;yBACpFA,KAAL,CAAA,GAAc9K,SAAAA,CAAU8K,KAAV,CAAd;;qBAGG2C,YAAL,CAAkBzH,KAAlB,CAAwB,IAAxB,EAA8B;oBAAC,UAAD;iBAAA,CAAaiE,MAAb,CAAoB3D,IAApB,CAA9B;;SA5FkB;QAyGnB;iBACI,gBADJ;mBAEM,SAASsH,cAAT,CAAwB3I,IAAxB,EAA8B4I,MAA9B,EAAsCpE,eAAtC,EAAuD;oBACxD2D,wBAAwB,IAAA,CAAKU,mBAA7B,EAAkD7I,IAAlD,CAAJ,EAA6D;0BACrD,IAAIjD,KAAJ,CAAU,mEAAV,CAAN;;oBAGEoF,eAAe,IAAIG,YAAJ,CAAiB,IAAA,CAAK9F,UAAtB,EAAkCoM,MAAlC,EAA0CpE,eAA1C,CAAnB;oBACIsE,WAAW,IAAIvF,kBAAJ,CAAuBvD,IAAvB,EAA6B4I,MAA7B,EAAqCzG,YAArC,CAAf;qBAEKoG,WAAL,CAAiB7I,IAAjB,CAAsBoJ,QAAtB;uBAEOA,SAAStF,MAAhB;;SArHkB;QA4HnB;iBACI,YADJ;iBAEI,SAAS7C,MAAT,GAAkB;uBACd,IAAA,CAAK4H,WAAL,CAAiBQ,MAAjB,CAAwB,SAAUV,UAAV,EAAsB;2BAC5CzB,UAAUpL,SAAV,CAAoB8E,aAApB,CAAkC+H,UAAlC,CAAP;iBADK,CAAP;;SA/HkB;QAwInB;iBACI,qBADJ;iBAEI,SAAS1H,MAAT,GAAkB;uBACd,IAAA,CAAK4H,WAAL,CAAiBQ,MAAjB,CAAwB,SAAUV,UAAV,EAAsB;2BAC5C9E,mBAAmB/H,SAAnB,CAA6B8E,aAA7B,CAA2C+H,UAA3C,CAAP;iBADK,CAAP;;SA3IkB;KAAtB;WAgJOC,QAAP;CArKa,EAAf;;;;;;IA8KA,IAAIU;;IAKJ,SAASA,UAAT,CAAoBC,KAApB,EAA2B;QACrBjG,QAAQ,IAAZ;mBAEe,IAAf,EAAqBgG,UAArB;WAEOnL,cAAP,CAAsB,IAAtB,EAA4B,OAA5B,EAAqC;QAAExC,OAAO4N,KAAT;QAAgBvL,YAAY,KAA5B;IAAA,CAArC;WAEO1B,IAAP,CAAY,IAAA,CAAKiN,KAAjB,EAAwBF,MAAxB,CAA+B,SAAU5M,GAAV,EAAe;eACrC,CAAA,CAAEA,OAAO6G,KAAT,CAAP;KADF,EAEGuB,OAFH,CAEW,SAAUpI,GAAV,EAAe;YACpBsB,aAAa,KAAK,CAAtB;YAEIwL,KAAAA,CAAM9M,GAAN,CAAA,KAAe,IAAnB,EAAyB;yBACV;4BACC,IADD;qBAEN,SAASwE,MAAT,GAAkB;2BACd,IAAP;;aAHJ;SADF,MAOO;yBACQ;4BACC,IADD;qBAEN,SAASA,MAAT,GAAkB;2BACd,IAAA,CAAKsI,KAAL,CAAW9M,GAAX,CAAA,CAAgBR,OAAhB,EAAP;;aAHJ;;eAOKkC,cAAP,CAAsBmF,KAAtB,EAA6B7G,GAA7B,EAAkCsB,UAAlC;KApBF;CAZF;;;IAwCA,IAAIyL,gBAAgB,YAAY;aACrBA,aAAT,GAAyB;uBACR,IAAf,EAAqBA,aAArB;aAEKC,UAAL,GAAkB,CAAA,CAAlB;;;;;;;kBAWUD,aAAZ,EAA2B;QAAC;iBACrB,sBADqB;mBAEnB,SAASE,oBAAT,CAA8BC,WAA9B,EAA2CzM,IAA3C,EAAiD;qBACjDuM,UAAL,CAAgBvM,IAAhB,CAAA,GAAwByM,WAAxB;;SAHuB;QAYxB;iBACI,wBADJ;mBAEM,SAASC,sBAAT,CAAgC1M,IAAhC,EAAsC;uBACpC,IAAA,CAAKuM,UAAL,CAAgBvM,IAAhB,CAAP;;SAfuB;QAyBxB;iBACI,cADJ;mBAEM,SAAS2M,YAAT,CAAsB3M,IAAtB,EAA4B;uBAC1B,IAAA,CAAKuM,UAAL,CAAgBvM,IAAhB,CAAA,IAAyBoM,UAAhC;;SA5BuB;KAA3B;WA+BOE,aAAP;CA9CkB,EAApB;AAiDA,SAASM,OAAT,CAAiB9H,GAAjB,EAAsB;WACbnG,OAAOC,SAAP,CAAiBC,QAAjB,CAA0BC,IAA1B,CAA+BgG,GAA/B,MAAwC,eAAxC,IAA2DnG,OAAOC,SAAP,CAAiBC,QAAjB,CAA0BC,IAA1B,CAA+BgG,GAA/B,MAAwC,oBAA1G;;AAGF,SAAS+H,aAAT,CAAuBC,OAAvB,EAAgC;WACvBA,QAAQtF,SAAR,CAAkBjC,YAAlB,CAA+BzF,UAA/B,CAA0CmH,cAAjD;;AAGF,SAAS8F,YAAT,CAAsBD,OAAtB,EAA+B;WACtBA,QAAQtF,SAAR,CAAkBjC,YAAlB,CAA+BzF,UAA/B,CAA0CsD,IAA1C,CAA+C4J,QAA/C,CAAwD,YAAxD,CAAP;;AAGF,SAASC,WAAT,CAAqBH,OAArB,EAA8B;QACxBA,WAAW,IAAf,EAAqB;eACZ,IAAP;KADF,MAEO,IAAID,cAAcC,OAAd,CAAJ,EAA4B;eAC1BA,OAAP;KADK,MAEA;eACEG,YAAYH,QAAQI,MAApB,CAAP;;;AAIJ,SAASC,gBAAT,CAA0BL,OAA1B,EAAmC;QAC7BA,QAAQI,MAAZ,EAAoB;eACXC,iBAAiBL,QAAQI,MAAzB,EAAiC9E,MAAjC,CAAwC0E,OAAxC,CAAP;KADF,MAEO;eACE;YAACA,OAAD;SAAP;;;AAIJ,SAASM,uBAAT,CAAiCN,OAAjC,EAA0C;QACpCA,QAAQtF,SAAR,CAAkBjC,YAAlB,CAA+BzF,UAA/B,CAA0CmH,cAA9C,EAA8D;eACrD;YAAC6F,OAAD;SAAP;KADF,MAEO;eACEM,wBAAwBN,QAAQI,MAAhC,EAAwC9E,MAAxC,CAA+C0E,OAA/C,CAAP;;;AAIJ,SAASO,yBAAT,CAAmCC,cAAnC,EAAmDC,YAAnD,EAAiE;QAC3DC,cAAcD,YAAAA,CAAaA,aAAanP,MAAb,GAAsB,CAAnC,CAAlB;QACIqP,QAAQD,YAAYhG,SAAZ,CAAsB/C,IAAtB,CAA2BgJ,KAAvC;QACI1D,sBAAsBpL,OAAOS,IAAP,CAAYoO,YAAYhG,SAAZ,CAAsB/C,IAAlC,EAAwC0H,MAAxC,CAA+C,SAAU5M,GAAV,EAAe;eAC/EkE,WAAW+J,YAAYhG,SAAZ,CAAsB/C,IAAtB,CAA2BlF,GAA3B,CAAX,CAAP;KADwB,EAEvBE,GAFuB,CAEnB,SAAUF,GAAV,EAAe;eACbiO,YAAYhG,SAAZ,CAAsB/C,IAAtB,CAA2BlF,GAA3B,CAAP;KAHwB,CAA1B;QAMImO,WAAW3D,oBAAoB4D,IAApB,CAAyB,SAAUlC,UAAV,EAAsB;eACrDA,WAAWrI,IAAX,KAAoB,OAA3B;KADa,CAAf;QAII,CAACsK,QAAL,EAAe;YACTjK,WAAWgK,KAAX,CAAJ,EAAuB;uBACVA,KAAX;SADF,MAEO;uBACM9J,SAAS,OAAT,EAAkB,KAAlB,EAAyB8J,KAAzB,CAAX;gCACoB3K,IAApB,CAAyB4K,QAAzB;;;QAIAE,WAAW,IAAIlC,QAAJ,CAAa4B,eAAe9F,SAAf,CAAyBjC,YAAzB,CAAsC3F,UAAnD,CAAf;WAEO;QAACgO,QAAD;QAAW7D,mBAAX;QAAgC2D,QAAhC;KAAP;;AAGF,SAASG,cAAT,CAAwBC,gBAAxB,EAA0CP,YAA1C,EAAwDQ,IAAxD,EAA8DC,MAA9D,EAAsE;;QAEhEC,cAAcV,aAAaW,KAAb,EAAlB;SAEKpL,IAAL,CAAUmL,YAAYzG,SAAZ,CAAsBzB,WAAhC;QAEIwH,aAAanP,MAAjB,EAAyB;yBACN6J,GAAjB,CAAqBgG,YAAYzG,SAAZ,CAAsBpE,IAA3C,EAAiD;YAAE0C,OAAOmI,YAAYzG,SAAZ,CAAsB1B,KAA/B;YAAsCrB,MAAMwJ,YAAYzG,SAAZ,CAAsB/C,IAAlE;QAAA,CAAjD,EAA2H,SAAU0J,YAAV,EAAwB;2BAClIA,YAAf,EAA6BZ,YAA7B,EAA2CQ,IAA3C,EAAiDC,MAAjD;SADF;KADF,MAIO;YACDI,aAAaH,YAAYzG,SAAZ,CAAsBjC,YAAtB,CAAmCuB,UAAnC,CAA8C6G,IAA9C,CAAmD,SAAU3F,KAAV,EAAiB;mBAC5EA,MAAM5E,IAAN,KAAe,OAAtB;SADe,CAAjB;YAGIiL,YAAYD,WAAW7I,YAAX,CAAwBuB,UAAxB,CAAmC6G,IAAnC,CAAwC,SAAU3F,KAAV,EAAiB;mBAChEA,MAAM5E,IAAN,KAAe,MAAtB;SADc,CAAhB;YAGIqK,QAAQ,KAAK,CAAjB;YAEIhK,WAAWwK,YAAYzG,SAAZ,CAAsB/C,IAAtB,CAA2BgJ,KAAtC,CAAJ,EAAkD;oBACxCQ,YAAYzG,SAAZ,CAAsB/C,IAAtB,CAA2BgJ,KAAnC;SADF,MAEO;oBACG9J,SAAS,OAAT,EAAkB,KAAlB,EAAyBsK,YAAYzG,SAAZ,CAAsB/C,IAAtB,CAA2BgJ,KAApD,CAAR;;YAGEnI,UAAU;mBACL2I,YAAYzG,SAAZ,CAAsB1B,KADjB;kBAENnH,OAAO0C,MAAP,CAAc,CAAA,CAAd,EAAkB4M,YAAYzG,SAAZ,CAAsB/C,IAAxC,EAA8C;gBAAE6J,OAAON,MAAT;gBAAiBP,OAAOA,KAAxB;YAAA,CAA9C;SAFR;yBAKiB1E,aAAjB,CAA+BkF,YAAYzG,SAAZ,CAAsBpE,IAArD,EAA2DkC,OAA3D,EAAoE+I,UAAU9I,YAA9E;;;AAIJ,SAASgJ,gBAAT,CAA0BzH,UAA1B,EAAsC;WAC7BA,WAAWzH,MAAX,CAAkB,SAAU4M,mBAAV,EAA+BjE,KAA/B,EAAsC;YACzD1B,eAAe1H,SAAf,CAAyB8E,aAAzB,CAAuCsE,KAAvC,CAAJ,EAAmD;gCAC7BlF,IAApB,CAAyBkF,MAAMtB,YAAN,EAAzB;;4BAGkB5D,IAApB,CAAyBqB,KAAzB,CAA+B8H,mBAA/B,EAAoDjJ,kBAAkBuL,iBAAiBvG,MAAMzC,YAAN,CAAmBuB,UAApC,CAAlB,CAApD;eAEOmF,mBAAP;KAPK,EAQJ,EARI,CAAP;;AAWF,SAASuC,oBAAT,CAA8B1B,OAA9B,EAAuCkB,MAAvC,EAA+C;QACzCS,qBAAqBxB,YAAYH,OAAZ,CAAzB;QAEI2B,kBAAJ,EAAwB;eACf,YAAY;gBACbC,qBAAJ;gBAEIX,OAAO,EAAX;gBACIY,WAAWF,mBAAmBjH,SAAnB,CAA6BjC,YAA7B,CAA0CzF,UAAzD;gBACI8O,SAASH,mBAAmBI,YAAnB,CAAgCC,EAA7C;gBACIvB,eAAeH,wBAAwBN,OAAxB,CAAnB;gBAEIiC,wBAAwB1B,0BAA0BP,OAA1B,EAAmCS,YAAnC,CAA5B,EACIyB,yBAAyB/M,cAAc8M,qBAAd,EAAqC,CAArC,CAD7B,EAEInB,WAAWoB,sBAAAA,CAAuB,CAAvB,CAFf,EAGIjF,sBAAsBiF,sBAAAA,CAAuB,CAAvB,CAH1B;qBAKSnD,QAAT,CAAkB9B,mBAAlB,EAAuC,SAAUkF,IAAV,EAAgB;qBAChDnM,IAAL,CAAU,MAAV;qBACKmF,GAAL,CAAS,MAAT,EAAiB;oBAAExD,MAAM;wBAAEqK,IAAIF,MAAN;oBAAA,CAAR;gBAAA,CAAjB,EAA2C,SAAUM,IAAV,EAAgB;yBACpD5F,mBAAL,CAAyBqF,SAASvL,IAAlC,EAAwC,SAAU8I,QAAV,EAAoB;uCAC3CA,QAAf,EAAyBqB,aAAa4B,KAAb,CAAmB,CAAnB,CAAzB,EAAgDpB,IAAhD,EAAsDC,MAAtD;qBADF;iBADF;aAFF;gBASIoB,YAAYb,iBAAiBX,SAAS/C,UAAT,CAAoB,CAApB,CAAA,CAAuBtF,YAAvB,CAAoCuB,UAArD,CAAhB;aAEC4H,wBAAwBd,SAASjC,WAAlC,EAA+C7D,OAA/C,CAAuD3D,KAAvD,CAA6DuK,qBAA7D,EAAoF1L,kBAAkBoM,SAAlB,CAApF;mBAEO;gBAACxB,QAAD;gBAAWG,IAAX;aAAP;SA1BF;KADF,MA6BO;eACE,YAAY;gBACbsB,sBAAJ;gBAEItB,OAAO,EAAX;gBACIR,eAAeJ,iBAAiBL,OAAjB,CAAnB;gBAEIwC,yBAAyBjC,0BAA0BP,OAA1B,EAAmCS,YAAnC,CAA7B,EACIgC,yBAAyBtN,cAAcqN,sBAAd,EAAsC,CAAtC,CAD7B,EAEI1B,WAAW2B,sBAAAA,CAAuB,CAAvB,CAFf,EAGIxF,sBAAsBwF,sBAAAA,CAAuB,CAAvB,CAH1B;qBAKS1D,QAAT,CAAkB9B,mBAAlB,EAAuC,SAAUkF,IAAV,EAAgB;+BACtCA,IAAf,EAAqB1B,aAAa4B,KAAb,CAAmB,CAAnB,CAArB,EAA4CpB,IAA5C,EAAkDC,MAAlD;aADF;gBAIIoB,YAAYb,iBAAiBX,SAAS/C,UAAT,CAAoB,CAApB,CAAA,CAAuBtF,YAAvB,CAAoCuB,UAArD,CAAhB;aAECuI,yBAAyBzB,SAASjC,WAAnC,EAAgD7D,OAAhD,CAAwD3D,KAAxD,CAA8DkL,sBAA9D,EAAsFrM,kBAAkBoM,SAAlB,CAAtF;mBAEO;gBAACxB,QAAD;gBAAWG,IAAX;aAAP;SAnBF;;;AAwBJ,SAASyB,aAAT,CAAuBrG,UAAvB,EAAmCsG,IAAnC,EAAyC;QACnCA,SAAStG,WAAWE,KAAX,CAAiBF,WAAWE,KAAX,CAAiBjL,MAAjB,GAA0B,CAA3C,CAAb,EAA4D;eACnD,IAAI0F,MAAJ,CAAW,IAAX,CAAP;;WAGKqF,WAAWC,QAAX,CAAoBsG,WAA3B;;AAGF,SAASC,eAAT,CAAyBxG,UAAzB,EAAqCsG,IAArC,EAA2C;QACrCA,SAAStG,WAAWE,KAAX,CAAiB,CAAjB,CAAb,EAAkC;eACzB,IAAIvF,MAAJ,CAAW,IAAX,CAAP;;WAGKqF,WAAWC,QAAX,CAAoBuG,eAA3B;;AAGF,SAASC,oBAAT,CAA8BC,cAA9B,EAA8C;WACrC,SAAU/C,OAAV,EAAmBrO,KAAnB,EAA0B;YAC3BsO,aAAaD,OAAb,CAAJ,EAA2B;gBACrB,CAAA,CAAErO,MAAM2K,QAAN,IAAkB3K,MAAM2K,QAAN,CAAe7H,cAAf,CAA8B,aAA9B,CAAlB,IAAkE9C,MAAM2K,QAAN,CAAe7H,cAAf,CAA8B,iBAA9B,CAApE,CAAJ,EAA2H;sBACnH,IAAIpB,KAAJ,CAAU,sFAAV,CAAN;;mBAGK1B,MAAM4K,KAAN,CAAY5J,GAAZ,CAAgB,SAAUgQ,IAAV,EAAgB;uBAC9B9Q,OAAO0C,MAAP,CAAcoO,KAAKP,IAAnB,EAAyB;0CACRV,qBAAqB1B,OAArB,EAA8B2C,KAAKzB,MAAnC,CADQ;iCAEjBwB,cAAc/Q,KAAd,EAAqBgR,IAArB,CAFiB;qCAGbE,gBAAgBlR,KAAhB,EAAuBgR,IAAvB,CAHa;oCAIdI;iBAJX,CAAP;aADK,CAAP;SALF,MAaO;mBACEpR,KAAP;;KAfJ;;yCAqBF,IAAIqR,kBAAkB,YAAY;aACvBA,eAAT,CAAyBtI,SAAzB,EAAoCqH,YAApC,EAAkD;YAC5C3B,SAAS/O,UAAUC,MAAV,GAAmB,CAAnB,IAAwBD,SAAAA,CAAU,CAAV,CAAA,KAAiB4B,SAAzC,GAAqD5B,SAAAA,CAAU,CAAV,CAArD,GAAoE,IAAjF;uBACe,IAAf,EAAqB2R,eAArB;aAEKtI,SAAL,GAAiBA,SAAjB;aACKqH,YAAL,GAAoBA,YAApB;aACK3B,MAAL,GAAcA,MAAd;eACO/N,MAAP,CAAc,IAAd;;gBAGU2Q,eAAZ,EAA6B;QAAC;iBACvB,0BADuB;mBAErB,SAASC,wBAAT,CAAkChK,WAAlC,EAA+C;oBAChDiK,mBAAmB,IAAA,CAAKxI,SAAL,CAAejC,YAAf,CAA4BmC,uBAA5B,CAAoD3B,WAApD,CAAvB;oBACIkK,gBAAgBD,oBAAoBA,gBAAAA,CAAiB,CAAjB,CAAxC;oBACI/B,cAAc,KAAK,CAAvB;;;oBAIIhI,OAAOrH,SAAP,CAAiB8E,aAAjB,CAA+BuM,aAA/B,CAAJ,EAAmD;kCACnC,IAAIH,eAAJ,CAAoBG,aAApB,EAAmC,IAAA,CAAKpB,YAAxC,EAAsD,IAAA,CAAK3B,MAA3D,CAAd;iBADF,MAEO;kCACS,IAAI4C,eAAJ,CAAoBG,aAApB,EAAmC,IAAA,CAAKpB,YAAL,CAAkB9I,WAAlB,CAAnC,EAAmE,IAAnE,CAAd;;oBAGE,CAACkK,aAAL,EAAoB;0BACZ,IAAI9P,KAAJ,CAAU,8BAA8B4F,WAA9B,GAA4C,iCAA5C,GAAgF,IAAA,CAAKyB,SAAL,CAAejC,YAAzG,CAAN;;oBAGEM,MAAMjH,SAAN,CAAgB8E,aAAhB,CAA8BuM,aAA9B,CAAJ,EAAkD;2BACzChC,WAAP;iBADF,MAEO;2BACEA,YAAY8B,wBAAZ,CAAqChK,WAArC,CAAP;;;SAtBuB;QAyB1B;iBACI,qBADJ;mBAEM,SAASmK,mBAAT,CAA6BxQ,IAA7B,EAAmC;uBACjC,IAAIoQ,eAAJ,CAAoB,IAAA,CAAKtI,SAAzB,EAAoC9H,IAApC,EAA0C,IAAA,CAAKwN,MAA/C,CAAP;;SA5ByB;KAA7B;WA+BO4C,eAAP;CA1CoB,EAAtB;AA6CA,SAASK,gBAAT,CAA0BrD,OAA1B,EAAmCsD,YAAnC,EAAiD;WACxCtD,QAAQ+B,YAAR,CAAqBpP,GAArB,CAAyB,SAAUC,IAAV,EAAgB;eACvC2Q,cAAcvD,QAAQoD,mBAAR,CAA4BxQ,IAA5B,CAAd,EAAiD0Q,YAAjD,CAAP;KADK,CAAP;;AAKF,SAASE,kBAAT,CAA4BxD,OAA5B,EAAqCsD,YAArC,EAAmD;WAC1CzR,OAAOS,IAAP,CAAY0N,QAAQ+B,YAApB,EAAkCxP,MAAlC,CAAyC,SAAUkI,GAAV,EAAexB,WAAf,EAA4B;YACtEA,WAAJ,CAAA,GAAmBsK,cAAcvD,QAAQiD,wBAAR,CAAiChK,WAAjC,CAAd,EAA6DqK,YAA7D,CAAnB;eAEO7I,GAAP;KAHK,EAIJ,CAAA,CAJI,CAAP;;AAOF,SAASgJ,eAAT,CAAyBH,YAAzB,EAAuCtD,OAAvC,EAAgDrO,KAAhD,EAAuD;WAC9C2R,aAAa/Q,MAAb,CAAoB,SAAUkI,GAAV,EAAeiJ,WAAf,EAA4B;eAC9CA,YAAY1D,OAAZ,EAAqBvF,GAArB,CAAP;KADK,EAEJ9I,KAFI,CAAP;;AAKF,SAAS4R,aAAT,CAAuBvD,OAAvB,EAAgCsD,YAAhC,EAA8C;QACxC3R,QAAQqO,QAAQ+B,YAApB;QAEIvQ,MAAMkB,OAAN,CAAcf,KAAd,CAAJ,EAA0B;gBAChB0R,iBAAiBrD,OAAjB,EAA0BsD,YAA1B,CAAR;KADF,MAEO,IAAI5R,SAASC,KAAT,CAAJ,EAAqB;gBAClB6R,mBAAmBxD,OAAnB,EAA4BsD,YAA5B,CAAR;;WAGKG,gBAAgBH,YAAhB,EAA8BtD,OAA9B,EAAuCrO,KAAvC,CAAP;;AAGF,SAASgS,sBAAT,CAAgC3D,OAAhC,EAAyCrO,KAAzC,EAAgD;QAC1CmO,QAAQnO,KAAR,KAAkBoO,cAAcC,OAAd,CAAtB,EAA8C;cACtC4D,YAAN,GAAqB,YAAY;mBACxB,IAAIpG,KAAJ,CAAUwC,QAAQtF,SAAR,CAAkBjC,YAAlB,CAA+B3F,UAAzC,EAAqD,SAAUqP,IAAV,EAAgB;qBACrEhH,GAAL,CAAS,MAAT,EAAiB;oBAAExD,MAAM;wBAAEqK,IAAIhC,QAAQ+B,YAAR,CAAqBC,EAA3B;oBAAA,CAAR;gBAAA,CAAjB,EAA4D,SAAUI,IAAV,EAAgB;yBACrE5F,mBAAL,CAAyBwD,QAAQtF,SAAR,CAAkBjC,YAAlB,CAA+BzF,UAA/B,CAA0CsD,IAAnE,EAAyE0J,QAAQtF,SAAR,CAAkBjC,YAA3F;iBADF;aADK,CAAP;SADF;;WASK9G,KAAP;;AAGF,SAASkS,mCAAT,CAA6CC,aAA7C,EAA4D;WACnD,SAASC,uBAAT,CAAiC/D,OAAjC,EAA0CrO,KAA1C,EAAiD;YAClDD,SAASC,KAAT,CAAJ,EAAqB;gBACfqS,QAAQF,cAAcjE,YAAd,CAA2BG,QAAQtF,SAAR,CAAkBjC,YAAlB,CAA+BzF,UAA/B,CAA0CsD,IAArE,CAAZ;mBAEO,IAAI0N,KAAJ,CAAUrS,KAAV,CAAP;SAHF,MAIO;mBACEA,KAAP;;KANJ;;AAWF,SAASsS,gBAAT,CAA0BjE,OAA1B,EAAmCrO,KAAnC,EAA0C;QACpCmO,QAAQnO,KAAR,CAAJ,EAAoB;YACdqO,QAAQtF,SAAR,CAAkBjC,YAAlB,CAA+BzF,UAA/B,CAA0CI,IAA1C,KAAmD,QAAvD,EAAiE;mBACxD,IAAI4D,MAAJ,CAAWrF,KAAX,CAAP;SADF,MAEO,IAAIqO,QAAQtF,SAAR,CAAkBjC,YAAlB,CAA+BzF,UAA/B,CAA0CI,IAA1C,KAAmD,MAAvD,EAA+D;mBAC7D,IAAI0D,IAAJ,CAASnF,KAAT,CAAP;;;WAIGA,KAAP;;AAGF,SAASuS,qBAAT,CAA+BlE,OAA/B,EAAwCrO,KAAxC,EAA+C;QACzCwS,wBAAwBnE,QAAQtF,SAAR,CAAkBjC,YAA9C,EACI3F,aAAaqR,sBAAsBrR,UADvC,EAEIE,aAAamR,sBAAsBnR,UAFvC;QAKI8M,QAAQnO,KAAR,CAAJ,EAAoB;YACdA,MAAMyS,UAAV,EAAsB;kBACdlR,IAAN,GAAaL,cAAcC,UAAd,EAA0BnB,MAAMyS,UAAhC,EAA4CpR,UAA5C,CAAb;SADF,MAEO;kBACCE,IAAN,GAAaF,UAAb;;;WAIGrB,KAAP;;AAGF,SAAS0S,mBAAT,CAA6B3G,IAA7B,EAAmC;QAC7B4G,qBAAqB5G,KAAKoG,aAA9B,EACIA,gBAAgBQ,uBAAuBrR,SAAvB,GAAmC,IAAIuM,aAAJ,EAAnC,GAAyD8E,kBAD7E,EAEIvB,iBAAiBrF,KAAKqF,cAF1B;WAIO;QAACkB,gBAAD;QAAmBN,sBAAnB;QAA2Cb,qBAAqBC,cAArB,CAA3C;QAAiFmB,qBAAjF;QAAwGL,oCAAoCC,aAApC,CAAxG;KAAP;;;;;;;;;;;IAaF,SAASS,MAAT,CAAgB7J,SAAhB,EAA2BqH,YAA3B,EAAyC;QACnCvJ,UAAUnH,UAAUC,MAAV,GAAmB,CAAnB,IAAwBD,SAAAA,CAAU,CAAV,CAAA,KAAiB4B,SAAzC,GAAqD5B,SAAAA,CAAU,CAAV,CAArD,GAAoE,CAAA,CAAlF;QAEIiS,eAAe9K,QAAQ8K,YAAR,IAAwBe,oBAAoB7L,OAApB,CAA3C;QACIwH,UAAU,IAAIgD,eAAJ,CAAoBtI,SAApB,EAA+BqH,YAA/B,CAAd;WAEOwB,cAAcvD,OAAd,EAAuBsD,YAAvB,CAAP;;AAGF,SAASkB,WAAT,CAAqBC,GAArB,EAA0B;QACpBjM,UAAUnH,UAAUC,MAAV,GAAmB,CAAnB,IAAwBD,SAAAA,CAAU,CAAV,CAAA,KAAiB4B,SAAzC,GAAqD5B,SAAAA,CAAU,CAAV,CAArD,GAAoE,CAAA,CAAlF;WAEO,SAASqT,OAAT,CAAiBC,aAAjB,EAAgCC,OAAhC,EAAyC;eACvCC,MAAMJ,GAAN,EAAWnQ,SAAS;kBACnB6C,KAAKC,SAAL,CAAeuN,aAAf,CADmB;oBAEjB,MAFiB;kBAGnB;SAHU,EAIfnM,OAJe,EAIN;qBACDlE,SAAS;gCACA,kBADA;wBAER;aAFD,EAGNkE,QAAQoM,OAHF,EAGWA,OAHX;SALO,CAAX,EASHE,IATG,CASE,SAAUC,QAAV,EAAoB;gBACvBC,cAAcD,SAASH,OAAT,CAAiBK,GAAjB,CAAqB,cAArB,CAAlB;gBAEID,YAAY5G,OAAZ,CAAoB,kBAApB,IAA0C,CAAC,CAA/C,EAAkD;uBACzC2G,SAASG,IAAT,EAAP;;mBAGKH,SAASI,IAAT,GAAgBL,IAAhB,CAAqB,SAAUK,IAAV,EAAgB;uBACnC;oBAAEA,MAAMA,IAAR;gBAAA,CAAP;aADK,CAAP;SAhBK,CAAP;KADF;;AAwBF,SAASvC,WAAT,CAAqBwC,eAArB,EAAsC;WAC7BA,mBAAmBA,gBAAgB9T,MAAnC,IAA6C8T,eAAAA,CAAgBA,gBAAgB9T,MAAhB,GAAyB,CAAzC,CAAA,CAA4CsR,WAAhG;;;;IAOF,IAAIyC,WAAS,YAAY;;;;;;;eASdA,MAAT,CAAgBvS,UAAhB,EAA4B4K,IAA5B,EAAkC;YAC5B+G,MAAM/G,KAAK+G,GAAf,EACIa,iBAAiB5H,KAAK4H,cAD1B,EAEIZ,UAAUhH,KAAKgH,OAFnB,EAGIa,gBAAgB7H,KAAK8H,QAHzB,EAIIA,WAAWD,kBAAkBtS,SAAlB,GAA8B,IAAIuM,aAAJ,EAA9B,GAAoD+F,aAJnE;uBAKe,IAAf,EAAqBF,MAArB;aAEKvS,UAAL,GAAkBA,UAAlB;aACKgR,aAAL,GAAqB0B,QAArB;YAEIf,OAAOC,OAAX,EAAoB;kBACZ,IAAIrR,KAAJ,CAAU,mIAAV,CAAN;;YAGEoR,GAAJ,EAAS;iBACFC,OAAL,GAAeF,YAAYC,GAAZ,EAAiBa,cAAjB,CAAf;SADF,MAEO,IAAIZ,OAAJ,EAAa;gBACdY,cAAJ,EAAoB;sBACZ,IAAIjS,KAAJ,CAAU,mHAAV,CAAN;;iBAGGqR,OAAL,GAAeA,OAAf;SALK,MAMA;kBACC,IAAIrR,KAAJ,CAAU,yDAAV,CAAN;;;;;;;;;;kBAcQgS,MAAZ,EAAoB;QAAC;iBACd,UADc;mBAEZ,SAASvE,QAAT,GAAoB;uBAClB,IAAIlC,QAAJ,CAAa,IAAA,CAAK9L,UAAlB,CAAP;;SAHgB;QAuBjB;iBACI,OADJ;mBAEM,SAAS2S,KAAT,GAAiB;oBACjB,IAAIrU,OAAOC,UAAUC,MAArB,EAA6BqG,OAAOnG,MAAMJ,IAAN,CAApC,EAAiDK,OAAO,CAA7D,EAAgEA,OAAOL,IAAvE,EAA6EK,MAA7E,CAAqF;yBAC9EA,IAAL,CAAA,GAAaJ,SAAAA,CAAUI,IAAV,CAAb;;uBAGK,IAAA,CAAK6M,SAASxM,SAAT,CAAmByM,IAAnB,CAAwBlH,KAAxB,CAA8BmG,KAA9B,EAAqC;oBAAC,IAAD;iBAAA,CAAOlC,MAAP,CAAc;oBAAC,IAAA,CAAKxI,UAAN;iBAAd,EAAiC6E,IAAjC,CAArC,CAAL,GAAP;;SA9BgB;QAsDjB;iBACI,UADJ;mBAEM,SAAS+N,QAAT,GAAoB;oBACpB,IAAInK,QAAQlK,UAAUC,MAAtB,EAA8BqG,OAAOnG,MAAM+J,KAAN,CAArC,EAAmDC,QAAQ,CAAhE,EAAmEA,QAAQD,KAA3E,EAAkFC,OAAlF,CAA2F;yBACpFA,KAAL,CAAA,GAAcnK,SAAAA,CAAUmK,KAAV,CAAd;;uBAGK,IAAA,CAAK8C,SAASxM,SAAT,CAAmByM,IAAnB,CAAwBlH,KAAxB,CAA8BsG,QAA9B,EAAwC;oBAAC,IAAD;iBAAA,CAAOrC,MAAP,CAAc;oBAAC,IAAA,CAAKxI,UAAN;iBAAd,EAAiC6E,IAAjC,CAAxC,CAAL,GAAP;;SA7DgB;QAiFjB;iBACI,MADJ;mBAEM,SAASgO,IAAT,CAAcC,OAAd,EAAuB;oBACxB7C,iBAAiB1R,UAAUC,MAAV,GAAmB,CAAnB,IAAwBD,SAAAA,CAAU,CAAV,CAAA,KAAiB4B,SAAzC,GAAqD5B,SAAAA,CAAU,CAAV,CAArD,GAAoE,IAAzF;oBAEIiI,QAAQ,IAAZ;oBAEIuM,kBAAkBxU,UAAUC,MAAV,GAAmB,CAAnB,IAAwBD,SAAAA,CAAU,CAAV,CAAA,KAAiB4B,SAAzC,GAAqD5B,SAAAA,CAAU,CAAV,CAArD,GAAoE,IAA1F;oBACIuT,UAAUvT,UAAUC,MAAV,GAAmB,CAAnB,IAAwBD,SAAAA,CAAU,CAAV,CAAA,KAAiB4B,SAAzC,GAAqD5B,SAAAA,CAAU,CAAV,CAArD,GAAoE,IAAlF;oBAEIyU,sBAAsB,KAAK,CAA/B;oBAEIxH,SAASxM,SAAT,CAAmB8E,aAAnB,CAAiCgP,OAAjC,CAAJ,EAA+C;0CACvBA,QAAQ,IAAR,CAAtB;iBADF,MAEO;0CACiBA,OAAtB;;oBAGEjB,gBAAgB;oBAAEc,OAAOK,oBAAoB/T,QAApB,EAAT;gBAAA,CAApB;oBAEIgR,cAAJ,EAAoB;kCACJlG,SAAd,GAA0BkG,cAA1B;;uBAGKxO,MAAP,CAAcoQ,aAAd,EAA6BkB,eAA7B;oBAEIhI,YAAY,KAAK,CAArB;oBAEIX,UAAUpL,SAAV,CAAoB8E,aAApB,CAAkCkP,mBAAlC,CAAJ,EAA4D;gCAC9CA,mBAAZ;iBADF,MAEO;wBACDhF,WAAWgF,mBAAf;wBAEIhF,SAAS/C,UAAT,CAAoBzM,MAApB,KAA+B,CAAnC,EAAsC;oCACxBwP,SAAS/C,UAAT,CAAoB,CAApB,CAAZ;qBADF,MAEO,IAAI8H,gBAAgBE,aAApB,EAAmC;oCAC5BjF,SAAS/C,UAAT,CAAoB8C,IAApB,CAAyB,SAAUmF,iBAAV,EAA6B;mCACzDA,kBAAkB1P,IAAlB,KAA2BuP,gBAAgBE,aAAlD;yBADU,CAAZ;qBADK,MAIA;8BACC,IAAI1S,KAAJ,CAAU,gNAAV,CAAN;;;uBAIG,IAAA,CAAKqR,OAAL,CAAaC,aAAb,EAA4BC,OAA5B,EAAqCE,IAArC,CAA0C,SAAUC,QAAV,EAAoB;wBAC/DA,SAASkB,IAAb,EAAmB;iCACRC,KAAT,GAAiB3B,OAAO1G,SAAP,EAAkBkH,SAASkB,IAA3B,EAAiC;2CACjC3M,MAAMwK,aAD2B;4CAEhCf;yBAFD,CAAjB;;2BAMKgC,QAAP;iBARK,CAAP;;SA7HgB;QAwJjB;iBACI,eADJ;mBAEM,SAASoB,aAAT,CAAuBC,WAAvB,EAAoC5N,OAApC,EAA6C;oBAC9C4J,OAAO,KAAK,CAAhB;oBAEI5Q,MAAMkB,OAAN,CAAc0T,WAAd,CAAJ,EAAgC;2BACvBA,WAAAA,CAAYA,YAAY9U,MAAZ,GAAqB,CAAjC,CAAP;iBADF,MAEO;2BACE8U,WAAP;;oBAGEC,wBAAwBjE,KAAKV,oBAAL,EAA5B,EACI4E,yBAAyBnR,cAAckR,qBAAd,EAAqC,CAArC,CAD7B,EAEIZ,QAAQa,sBAAAA,CAAuB,CAAvB,CAFZ,EAGIrF,OAAOqF,sBAAAA,CAAuB,CAAvB,CAHX;oBAKIvD,iBAAiB,KAAK,CAA1B;oBAEIX,KAAKW,cAAL,IAAuBvK,OAA3B,EAAoC;qCACjB3G,OAAO0C,MAAP,CAAc,CAAA,CAAd,EAAkB6N,KAAKW,cAAvB,EAAuCvK,OAAvC,CAAjB;;uBAGK,IAAA,CAAKmN,IAAL,CAAUF,KAAV,EAAiB1C,cAAjB,EAAiC+B,IAAjC,CAAsC,SAAUC,QAAV,EAAoB;6BACtDmB,KAAT,GAAiBjF,KAAK1O,MAAL,CAAY,SAAUgU,MAAV,EAAkB9T,GAAlB,EAAuB;+BAC3C8T,MAAAA,CAAO9T,GAAP,CAAP;qBADe,EAEdsS,SAASmB,KAFK,CAAjB;2BAIOnB,QAAP;iBALK,CAAP;;SA9KgB;QAsMjB;iBACI,eADJ;mBAEM,SAASyB,aAAT,CAAuBpB,eAAvB,EAAwCqB,KAAxC,EAA+C;oBAChD9M,SAAS,IAAb;oBAEI+M,WAAWD,MAAMC,QAArB;oBAEI9D,YAAYwC,eAAZ,CAAJ,EAAkC;2BACzB,IAAA,CAAKe,aAAL,CAAmBf,eAAnB,EAAoC;wBAAEzE,OAAO+F,QAAT;oBAAA,CAApC,EAAyD5B,IAAzD,CAA8D,SAAU6B,KAAV,EAAiB;4BAChFT,QAAQS,MAAMT,KAAlB;4BAEIU,QAAQxB,gBAAgB9J,MAAhB,CAAuB4K,KAAvB,CAAZ;+BAEOvM,OAAO6M,aAAP,CAAqBI,KAArB,EAA4B;4BAAEF,UAAUA,QAAZ;wBAAA,CAA5B,CAAP;qBALK,CAAP;;uBASKG,QAAQC,OAAR,CAAgB1B,eAAhB,CAAP;;SAvNgB;QAuOjB;iBACI,SADJ;mBAEM,SAAS2B,OAAT,CAAiBlF,QAAjB,EAA2B;oBAC5B,CAACA,QAAL,EAAe;0BACP,IAAIxO,KAAJ,CAAU,uEAAV,CAAN;iBADF,MAEO,IAAI,CAACwO,SAAS3O,IAAT,CAAciH,cAAnB,EAAmC;0BAClC,IAAI9G,KAAJ,CAAU,kFAAkFwO,SAAS3O,IAAT,CAAcoD,IAAhG,GAAuG,GAAjH,CAAN;;uBAGK,IAAA,CAAKqP,IAAL,CAAU9D,SAAS+B,YAAT,EAAV,EAAmCkB,IAAnC,CAAwC,SAAUkC,KAAV,EAAiB;wBAC1Dd,QAAQc,MAAMd,KAAlB;2BACOA,MAAM9D,IAAb;iBAFK,CAAP;;SAhPgB;QAkQjB;iBACI,UADJ;mBAEM,SAAS6E,WAAT,CAAqB3Q,IAArB,EAA2BpD,IAA3B,EAAiCqD,YAAjC,EAA+C;uBAC7CM,SAASP,IAAT,EAAepD,IAAf,EAAqBqD,YAArB,CAAP;;SArQgB;QAkRjB;iBACI,MADJ;mBAEM,SAAS2Q,KAAT,CAAezU,GAAf,EAAoB;uBAClBsE,aAAatE,GAAb,CAAP;;SArRgB;KAApB;WAwRO4S,MAAP;CAvUW,EAAb;AC/yDA;;;QAIM8B,SAAAA;;;;;;;;;;+BASqB;uBAChB;oBACL,uBADK;oBAEL,QAFK;iBAAP;;;;;mCAayB;uBAClB;iCACQ,uBADR;4BAEG;iBAFV;;;;oBAYU5H,KAAZ,EAAmB;;;eACVjN,IAAP,CAAY,IAAA,CAAK8U,oBAAjB,EAAuCvM,OAAvC,CAA+C,SAACpI,GAAD,EAAS;gBAClD,CAAC8M,MAAM9K,cAAN,CAAqBhC,GAArB,CAAL,EAAgC;;;;oBAExB4U,IAAR,CAAA,kCAA6C5U,GAA7C,GAAA,2CAAyF,MAAK2U,oBAAL,CAA0B3U,GAA1B,CAAzF,GAAA;kBACM,MAAK2U,oBAAL,CAA0B3U,GAA1B,CAAN,CAAA,GAAwC8M,KAAAA,CAAM9M,GAAN,CAAxC;SAJF;aAOK6U,kBAAL,CAAwBzM,OAAxB,CAAgC,SAACpI,GAAD,EAAS;gBACnC8M,MAAM9K,cAAN,CAAqBhC,GAArB,CAAJ,EAA+B;sBACxBA,GAAL,CAAA,GAAY8M,KAAAA,CAAM9M,GAAN,CAAZ;aADF,MAEO;sBACC,IAAIY,KAAJ,CAAA,wCAA+CZ,GAA/C,GAAA,KAAN;;SAJJ;YAQI8M,MAAM9K,cAAN,CAAqB,YAArB,CAAJ,EAAwC;iBACjC8S,UAAL,GAAkBhI,MAAMgI,UAAxB;SADF,MAEO;iBACAA,UAAL,GAAkB,SAAlB;;YAGEhI,MAAM9K,cAAN,CAAqB,QAArB,CAAJ,EAAoC;iBAC7BD,MAAL,GAAc+K,MAAM/K,MAApB;;YAGE+K,MAAM9K,cAAN,CAAqB,UAArB,CAAJ,EAAsC;iBAC/B+S,QAAL,GAAgBjI,MAAMiI,QAAtB;;;;;ICnEeC,cAAAA;;;;;;;qCACA;oBAAZC,KAAY,GAAA,UAAA,MAAA,GAAA,KAAA,SAAA,CAAA,EAAA,KAAA,YAAA,SAAA,CAAA,EAAA,GAAJ,CAAA,CAAI;oBACXC,YAAY,CAAA,CAAlB;oBAEID,MAAME,uBAAV,EAAmC;;4BAEzBP,IAAR,CAAa,0DAAb;;;oBAIEK,MAAMG,SAAN,IAAmBH,MAAMG,SAAN,CAAgBvW,MAAvC,EAA+C;8BACnCwW,KAAV,GAAkBJ,MAAMG,SAAN,CAAgBlV,GAAhB,CAAoB,SAACoV,QAAD,EAAc;iCACzCC,aAAT,GAAyBD,SAASE,SAAlC;+BACOF,SAASE,SAAhB;+BAEOF,QAAP;qBAJgB,CAAlB;;oBAQEL,MAAMQ,IAAV,EAAgB;8BACJA,IAAV,GAAiBR,MAAMQ,IAAvB;;oBAGER,MAAMS,KAAV,EAAiB;8BACLC,aAAV,GAA0B;wBAACD,OAAOT,MAAMS,KAAd;oBAAA,CAA1B;;oBAGET,MAAMW,eAAV,EAA2B;wBACrB,CAACV,UAAUS,aAAf,EAA8B;kCAClBA,aAAV,GAA0B,CAAA,CAA1B;;8BAEQA,aAAV,CAAwBE,0BAAxB,GAAqD;wBACnD;4BAACC,iBAAiBb,MAAMW,eAAxB;wBAAA,CADmD;qBAArD;;oBAKEX,MAAMc,gBAAV,EAA4B;8BAChBC,UAAV,GAAuBf,MAAMc,gBAA7B;;;oBAIEd,MAAMU,aAAV,EAAyB;wBACnB,CAACT,UAAUS,aAAf,EAA8B;kCAClBA,aAAV,GAA0B,CAAA,CAA1B;;8BAEQA,aAAV,CAAwBM,WAAxB,GAAsChB,MAAMU,aAAN,CAAoBM,WAA1D;;oBAGEhB,MAAMiB,qBAAV,EAAiC;;4BAEvBtB,IAAR,CAAa,wDAAb;;uBAGKM,SAAP;;;;;6CAGeiB,UAAAA,EAAYlB,KAAAA,EAAO;oBAC5BmB,4BAA4B;gCACpB,EADoB;4BAExB;iBAFV;oBAKMC,sBAAsB;4BAClB,EADkB;0BAEpB;iBAFR;oBAKIF,UAAJ,EAAgB;8CACYG,MAA1B,GAAmCH,UAAnC;wCACoBG,MAApB,GAA6BH,UAA7B;;oBAGElB,MAAMc,gBAAV,EAA4B;8CACAC,UAA1B,GAAuCf,MAAMc,gBAA7C;;oBAGEd,MAAMQ,IAAV,EAAgB;wCACMA,IAApB,GAA2BR,MAAMQ,IAAjC;;oBAGER,MAAMiB,qBAAV,EAAiC;;4BAEvBtB,IAAR,CAAa,wDAAb;;;uBAIK;oBAACwB,2BAAAA,yBAAD;oBAA4BC,qBAAAA,mBAA5B;gBAAA,CAAP;;;;;wCAGUF,UAAAA,EAAYT,KAAAA,EAAO;oBACvBa,yBAAyB;mCACd;wBAACb,OAAAA,KAAD;oBAAA,CADc;4BAErBS;iBAFV;uBAKOI,sBAAP;;;;;yCAGWJ,UAAAA,EAAYf,SAAAA,EAAW;oBAC5BC,QAAQtW,MAAMkB,OAAN,CAAcmV,SAAd,IAA2BA,SAA3B,GAAuC;oBAACA,SAAD;iBAArD;uBAEO;4BACGe,UADH;2BAEEd,MAAMnV,GAAN,CAAUsW,iBAAV,EAA6B5J,MAA7B,CAAoCzN,OAApC;iBAFT;;;;;wCAMUgX,UAAAA,EAAYM,aAAAA,EAAe;uBAC9B;4BACGN,UADH;mCAEUpX,MAAMkB,OAAN,CAAcwW,aAAd,IAA+BA,cAAcC,IAAd,EAA/B,GAAsD,EAAA;iBAFvE;;;;;2CAMaP,UAAAA,EAAY;uBAClB;4BACGA,UADH;mCAEU,EAAA;iBAFjB;;;;;yCAMWA,UAAAA,EAAYQ,aAAAA,EAAe;uBAC/B;4BACGR,UADH;mCAEUQ,iBAAiB,EAAA;iBAFlC;;;;;2CAMaR,UAAAA,EAAYS,iBAAAA,EAAmB;uBACrC;4BACGT,UADH;wCAEeS,oBAAoB;wBAACA,iBAAD;qBAApB,GAA0C,EAAA;iBAFhE;;;;;4CAMcT,UAAAA,EAAYU,WAAAA,EAAa;oBACjCC,UAAU/X,MAAMkB,OAAN,CAAc4W,WAAd,IAA6BA,WAA7B,GAA2C;oBAACA,WAAD;iBAA3D;uBAEO;4BACGV,UADH;;iBAAP;;;;;6CAMeA,UAAAA,EAAYf,SAAAA,EAAW;oBAChCC,QAAQtW,MAAMkB,OAAN,CAAcmV,SAAd,IAA2BA,SAA3B,GAAuC;oBAACA,SAAD;iBAArD;uBAEO;4BACGe,UADH;2BAEEd,MAAMnV,GAAN,CAAUsW,iBAAV,EAA6B5J,MAA7B,CAAoCzN,OAApC;iBAFT;;;;;4CAMcgX,UAAAA,EAAYf,SAAAA,EAAW;oBAC/BC,QAAQtW,MAAMkB,OAAN,CAAcmV,SAAd,IAA2BA,SAA3B,GAAuC;oBAACA,SAAD;iBAArD;uBAEO;4BACGe,UADH;2BAEEd,MAAMnV,GAAN,CAAUsW,iBAAV,EAA6B5J,MAA7B,CAAoCzN,OAApC;iBAFT;;;;;kDAMoBgX,UAAAA,EAAYP,eAAAA,EAAiB;oBAC3CE,kBAAkB,CAAA,CAAxB;oBAEIF,gBAAgBmB,QAApB,EAA8B;oCACZA,QAAhB,GAA2BnB,gBAAgBmB,QAA3C;;oBAGEnB,gBAAgBoB,QAApB,EAA8B;oCACZA,QAAhB,GAA2BpB,gBAAgBoB,QAA3C;;oBAGEpB,gBAAgBqB,IAApB,EAA0B;oCACRA,IAAhB,GAAuBrB,gBAAgBqB,IAAvC;;oBAGErB,gBAAgBsB,OAApB,EAA6B;oCACXA,OAAhB,GAA0BtB,gBAAgBsB,OAA1C;;oBAGEtB,gBAAgBuB,OAApB,EAA6B;oCACXA,OAAhB,GAA0BvB,gBAAgBuB,OAA1C;;oBAGEvB,gBAAgBwB,SAApB,EAA+B;oCACbA,SAAhB,GAA4BxB,gBAAgBwB,SAA5C;;oBAGExB,gBAAgByB,QAApB,EAA8B;oCACZA,QAAhB,GAA2BzB,gBAAgByB,QAA3C;;oBAGEzB,gBAAgB0B,KAApB,EAA2B;oCACTA,KAAhB,GAAwB1B,gBAAgB0B,KAAxC;;oBAGE1B,gBAAgB2B,GAApB,EAAyB;oCACPA,GAAhB,GAAsB3B,gBAAgB2B,GAAtC;;oBAGE3B,gBAAgB4B,QAApB,EAA8B;oCACZA,QAAhB,GAA2B5B,gBAAgB4B,QAA3C;;oBAGIC,sBACJ3B,mBAAoB1W,OAAOS,IAAP,CAAYiW,eAAZ,EAA6BjX,MAA7B,GAAsC,CAD5D;uBAGO;4BACGsX,UADH;mCAEU;oDACesB,sBACxB;4BAAC;gCAAC3B,iBAAAA,eAAD;4BAAA,CAAD;yBADwB,GAExB,EAAA;;iBALR;;;;;;AAWJ,SAASU,iBAAT,CAA2BlB,QAA3B,EAAqC;QAC7BoC,OAAO,CAAA,CAAb;QAEI,OAAOpC,SAAS/F,EAAhB,KAAuB,WAA3B,EAAwC;aACjCA,EAAL,GAAU+F,SAAS/F,EAAnB;;QAGE,OAAO+F,SAASS,gBAAhB,KAAqC,WAAzC,EAAsD;aAC/CC,UAAL,GAAkBV,SAASS,gBAA3B;;QAGE,OAAOT,SAASqC,QAAhB,KAA6B,WAAjC,EAA8C;aACvCA,QAAL,GAAgBrC,SAASqC,QAAzB;;QAGE,OAAOrC,SAASE,SAAhB,KAA8B,WAAlC,EAA+C;aACxCD,aAAL,GAAqBD,SAASE,SAA9B;;QAGEpW,OAAOS,IAAP,CAAY6X,IAAZ,EAAkB7Y,MAAlB,KAA6B,CAAjC,EAAoC;eAC3B,IAAP;;WAGK6Y,IAAP;;IChPmBE,WACnB,SAAA,SAAYC,MAAZ,EAAoB;;SACbC,aAAL,GAAqBD,MAArB;SACKE,WAAL,GAAmB,IAAI/C,WAAJ,EAAnB;;ACLG,IAAMgD,gBAAgB;IAAC;QAACC,SAAS,gCAAV;IAAA,CAAD;CAAtB;AAEQ,SAASC,eAAT,CAAyB1J,IAAzB,EAA+B;QACtC3O,OAAO2O,KAAK2J,KAAL,CAAW,GAAX,CAAb;WAEO,SAAA,IAAA,EAA0B;YAAhB1E,KAAgB,GAAA,KAAhBA,KAAgB,EAAT2E,MAAS,GAAA,KAATA,MAAS;eACxB,IAAIhE,OAAJ,CAAY,SAACC,OAAD,EAAUgE,MAAV,EAAqB;gBAClC;oBACIC,SAASzY,KAAKC,MAAL,CAAY,SAACyY,GAAD,EAAMvY,GAAN,EAAc;2BAChCuY,GAAAA,CAAIvY,GAAJ,CAAP;iBADa,EAEZyT,KAFY,CAAf;wBAIQ6E,MAAR;aALF,CAME,OAAOE,CAAP,EAAU;oBACNJ,MAAJ,EAAY;2BACHA,MAAP;iBADF,MAEO;2BACEJ,aAAP;;;SAXC,CAAP;KADF;;ACLa,SAASS,yBAAT,CAAmCC,gBAAnC,EAAqDb,MAArD,EAA6D;QACpEc,WAAW,EAAA,CAAG9P,MAAH,CAAU6P,gBAAV,CAAjB;WAEOtE,QAAQwE,GAAR,CAAYD,SAAS7Y,MAAT,CAAgB,SAAC+Y,UAAD,EAAaC,OAAb,EAAyB;;YAGtDA,YAAY,IAAhB,EAAsB;mBACbD,UAAP;;;mBAIStV,IAAX,CAAgBsU,OAAO9D,aAAP,CAAqB+E,QAAQC,MAA7B,EAAqC;YAAC9E,UAAU,GAAX;QAAA,CAArC,EAAsD5B,IAAtD,CAA2D,SAAC0G,MAAD,EAAY;oBAC7EjM,KAAR,CAAciM,MAAd,GAAuBA,MAAvB;SADc,CAAhB;mBAIWxV,IAAX,CAAgBsU,OAAO9D,aAAP,CAAqB+E,QAAQE,QAA7B,EAAuC;YAAC/E,UAAU,GAAX;QAAA,CAAvC,EAAwD5B,IAAxD,CAA6D,SAAC2G,QAAD,EAAc;oBACjFlM,KAAR,CAAckM,QAAd,GAAyBA,QAAzB;SADc,CAAhB;eAIOH,UAAP;KAhBiB,EAiBhB,EAjBgB,CAAZ,CAAP;;ACDK,SAASI,oCAAT,CAA8CpB,MAA9C,EAAsD;WACpD,SAASc,QAAT,EAAmB;eACjBF,0BAA0BE,QAA1B,EAAoCd,MAApC,EAA4CxF,IAA5C,CAAiD,YAAM;mBACrDsG,QAAP;SADK,CAAP;KADF;;AAOK,SAASO,+CAAT,CAAyDrB,MAAzD,EAAiE;WAC/D,SAASsB,uBAAT,EAAkC;YACjCC,cAAc,EAAA,CAAGvQ,MAAH,CAAUsQ,uBAAV,CAApB;eAEO/E,QAAQwE,GAAR,CAAYQ,YAAYtZ,MAAZ,CAAmB,SAAC+Y,UAAD,EAAaQ,UAAb,EAA4B;mBACzDR,WAAWhQ,MAAX,CAAkB4P,0BAA0BY,WAAWV,QAArC,EAA+Cd,MAA/C,CAAlB,CAAP;SADiB,EAEhB,EAFgB,CAAZ,EAECxF,IAFD,CAEM,YAAM;mBACV8G,uBAAP;SAHK,CAAP;KAHF;;ACXF;;IAGA,IAAA,iBAAe;;;;;;;;;;;;;;;uBAAA,EAAA,SAAA,kBAiBKL,OAjBL,EAiBc/S,OAjBd,EAiBuB;eAC3B+S,QAAQE,QAAR,CAAiB5K,IAAjB,CAAsB,SAACkL,OAAD,EAAa;mBACjCA,QAAQC,eAAR,CAAwBC,KAAxB,CAA8B,SAACC,cAAD,EAAoB;uBAChD1T,OAAAA,CAAQ0T,eAAe5V,IAAvB,CAAA,KAAiC4V,eAAeva,KAAf,CAAqBM,OAArB,EAAxC;aADK,CAAP;SADK,CAAP;;CAlBJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACEA,UAAA;AAOA;;;QAIMka,kBAAAA,SAAAA,SAAAA;;;;;;;;;;;;;;;;;;;mCAgBiB;oBAAZxL,KAAY,GAAA,UAAA,MAAA,GAAA,KAAA,SAAA,CAAA,EAAA,KAAA,YAAA,SAAA,CAAA,EAAA,GAAJ,EAAI;uBACZ,IAAA,CAAK4J,aAAL,CACJ5E,IADI,CACCyG,OADD,EACyB;oBAACzL,OAAAA,KAAD;gBAAA,CADzB,EAEJmE,IAFI,CAEC6F,gBAAgB,UAAhB,CAFD,EAGJ7F,IAHI,CAGC4G,qCAAqC,IAAA,CAAKnB,aAA1C,CAHD,CAAP;;;;;mCAiBIvI,EAAAA,EAAI;uBACD,IAAA,CAAKuI,aAAL,CACJ5E,IADI,CACC0G,KADD,EACmB;oBAACrK,IAAAA,EAAD;gBAAA,CADnB,EAEJ8C,IAFI,CAEC6F,gBAAgB,MAAhB,CAFD,EAGJ7F,IAHI,CAGC4G,qCAAqC,IAAA,CAAKnB,aAA1C,CAHD,CAAP;;;;;0CAkBY+B,GAAAA,EAAK;uBACV,IAAA,CAAK/B,aAAL,CACJ5E,IADI,CACC4G,OADD,EACoB;oBAACD,KAAAA,GAAD;gBAAA,CADpB,EAEJxH,IAFI,CAEC6F,gBAAgB,OAAhB,CAFD,EAGJ7F,IAHI,CAGC4G,qCAAqC,IAAA,CAAKnB,aAA1C,CAHD,CAAP;;;;;0CAiBYiC,MAAAA,EAAQ;uBACb,IAAA,CAAKjC,aAAL,CACJ5E,IADI,CACC8G,OADD,EACuB;oBAACD,QAAAA,MAAD;gBAAA,CADvB,EAEJ1H,IAFI,CAEC6F,gBAAgB,iBAAhB,CAFD,EAGJ7F,IAHI,CAGC4G,qCAAqC,IAAA,CAAKnB,aAA1C,CAHD,CAAP;;;;;yCAsB4D;+FAAJ,CAAA,CAAI,oBAAlD5J,KAAkD,EAAlDA,KAAkD,GAAA,eAAA,YAA1C,EAA0C,GAAA,gCAAtC+L,OAAsC,EAAtCA,OAAsC,GAAA,iBAAA,YAA5B,IAA4B,GAAA,cAAtBjH,QAAsB,GAAA,KAAtBA,KAAsB,EAAfkH,OAAe,GAAA,KAAfA,OAAe;uBACrD,IAAA,CAAKpC,aAAL,CACJ5E,IADI,CACCyG,OADD,EACyB;gCAAA;oCAAA;mCAAA;;iBADzB,EAOJtH,IAPI,CAOC6F,gBAAgB,UAAhB,CAPD,EAQJ7F,IARI,CAQC4G,qCAAqC,IAAA,CAAKnB,aAA1C,CARD,CAAP;;;;;iDAwBmBqC,SAAAA,EAAW;uBACvB,IAAA,CAAKrC,aAAL,CACJ5E,IADI,CACCkH,OADD,EAC8B;oBAACD,WAAAA,SAAD;gBAAA,CAD9B,EAEJ9H,IAFI,CAEC6F,gBAAgB,wBAAhB,CAFD,EAGJ7F,IAHI,CAGC4G,qCAAqC,IAAA,CAAKnB,aAA1C,CAHD,CAAP;;;;;mCAvHY;uBACLuC,cAAP;;;;;EAF0BzC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACZ9B,UAAA;AAOA;;;QAIM0C,qBAAAA,SAAAA,SAAAA;;;;;;;;;;;;;;;;;;;mCAaiB;oBAAZpM,KAAY,GAAA,UAAA,MAAA,GAAA,KAAA,SAAA,CAAA,EAAA,KAAA,YAAA,SAAA,CAAA,EAAA,GAAJ,EAAI;uBACZ,IAAA,CAAK4J,aAAL,CACJ5E,IADI,CACCqH,OADD,EAC4B;oBAACrM,OAAAA,KAAD;gBAAA,CAD5B,EAEJmE,IAFI,CAEC6F,gBAAgB,aAAhB,CAFD,CAAP;;;;;mDAe0D;+FAAJ,CAAA,CAAI,oBAAtChK,KAAsC,EAAtCA,KAAsC,GAAA,eAAA,YAA9B,EAA8B,GAAA,sCAA1BsM,aAA0B,EAA1BA,aAA0B,GAAA,uBAAA,YAAV,EAAU,GAAA;uBACnD,IAAA,CAAK1C,aAAL,CACJ5E,IADI,CACCuH,OADD,EACwC;oBAACvM,OAAAA,KAAD;oBAAQsM,eAAAA,aAAR;gBAAA,CADxC,EAEJnI,IAFI,CAEC6F,gBAAgB,aAAhB,CAFD,EAGJ7F,IAHI,CAGC6G,gDAAgD,IAAA,CAAKpB,aAArD,CAHD,CAAP;;;;;mCAkBIvI,EAAAA,EAAI;uBACD,IAAA,CAAKuI,aAAL,CACJ5E,IADI,CACCwH,OADD,EACsB;oBAACnL,IAAAA,EAAD;gBAAA,CADtB,EAEJ8C,IAFI,CAEC6F,gBAAgB,MAAhB,CAFD,CAAP;;;;;8CAgBgB3I,EAAAA,EAA+B;gGAAJ,CAAA,CAAI,8BAA1BiL,aAA0B,EAA1BA,aAA0B,GAAA,wBAAA,YAAV,EAAU,GAAA;uBACxC,IAAA,CAAK1C,aAAL,CACJ5E,IADI,CACCyH,OADD,EACkC;oBAACpL,IAAAA,EAAD;oBAAKiL,eAAAA,aAAL;gBAAA,CADlC,EAEJnI,IAFI,CAEC6F,gBAAgB,MAAhB,CAFD,EAGJ7F,IAHI,CAGC6G,gDAAgD,IAAA,CAAKpB,aAArD,CAHD,CAAP;;;;;0CAiBYiC,MAAAA,EAAQ;uBACb,IAAA,CAAKjC,aAAL,CACJ5E,IADI,CACC0H,OADD,EAC0B;oBAACb,QAAAA,MAAD;gBAAA,CAD1B,EAEJ1H,IAFI,CAEC6F,gBAAgB,oBAAhB,CAFD,CAAP;;;;;yCAqB4D;gGAAJ,CAAA,CAAI,sBAAlDhK,KAAkD,EAAlDA,KAAkD,GAAA,gBAAA,YAA1C,EAA0C,GAAA,mCAAtC+L,OAAsC,EAAtCA,OAAsC,GAAA,kBAAA,YAA5B,IAA4B,GAAA,eAAtBjH,KAAsB,GAAA,MAAtBA,KAAsB,EAAfkH,OAAe,GAAA,MAAfA,OAAe;uBACrD,IAAA,CAAKpC,aAAL,CAAmB5E,IAAnB,CAAwBqH,OAAxB,EAAmD;gCAAA;oCAAA;gCAAA;;iBAAnD,EAKJlI,IALI,CAKC6F,gBAAgB,aAAhB,CALD,CAAP;;;;;EA1G6BN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACZjC,UAAA;AAIA;;;QAIMiD,eAAAA,SAAAA,SAAAA;;;;;;;;;;;;;;;;;;;oCAaQ;uBACH,IAAA,CAAK/C,aAAL,CACJ5E,IADI,CACC4H,QADD,EAEJzI,IAFI,CAEC6F,gBAAgB,MAAhB,CAFD,CAAP;;;;;4CAec;uBACP,IAAA,CAAKJ,aAAL,CACJ5E,IADI,CACC6H,QADD,EAEJ1I,IAFI,CAEC6F,gBAAgB,MAAhB,CAFD,CAAP;;;;;EA9BuBN;ACXpB,SAASoD,uBAAT,CAAiCC,kBAAjC,EAAqD;QACpDC,cAAcD,mBAAmBE,mBAAvC;QACMC,aAAaH,mBAAmBI,IAAnB,IAA2BJ,mBAAmBK,KAA9C,IAAuDJ,YAAYG,IAAnE,IAA2EH,YAAYI,KAA1G;QAEI,CAACF,UAAL,EAAiB;cACT,IAAIxa,KAAJ,CAAA,gFAC0E8D,KAAKC,SAAL,CAC5EsW,kBAD4E,CAD1E,CAAN;;WAOKG,UAAP;;AAGK,SAASG,wBAAT,CAAkCJ,mBAAlC,EAAuD;QACtDC,aAAaD,oBAAoBE,IAApB,IAA4BF,oBAAoBG,KAAnE;QAEI,CAACF,UAAL,EAAiB;cACT,IAAIxa,KAAJ,CAAA,0DACoD8D,KAAKC,SAAL,CACtDwW,mBADsD,CADpD,CAAN;;WAOKC,UAAP;;AAGF,SAASI,wCAAT,CAAkDC,aAAlD,EAAiEC,iCAAjE,EAAoG;;;;QAI7F,IAAIra,IAAI,CAAb,EAAgBA,IAAIoa,cAAc5c,MAAlC,EAA0CwC,GAA1C,CAA+C;YACtCsa,mBADsC,GACfF,aAAAA,CAAcpa,CAAd,CADe,CACtCsa,mBADsC;YAGzC,CAACA,mBAAL,EAA0B;;;YAErB,IAAIC,IAAI,CAAb,EAAgBA,IAAID,oBAAoB9c,MAAxC,EAAgD+c,GAAhD,CAAqD;gBAC7CC,aAAaF,mBAAAA,CAAoBC,CAApB,CAAnB;gBACME,yBAAyB1c,OAAO0C,MAAP,CAAc,CAAA,CAAd,EAC7B+Z,WAAWV,mBAAX,IAAkC,CAAA,CADL,EAE7BU,WAAWR,IAAX,GAAkB;gBAACA,MAAMQ,WAAWR,IAAlB;YAAA,CAAlB,GAA4C,IAFf,EAG7BQ,WAAWP,KAAX,GAAmB;gBAACA,OAAOO,WAAWP,KAAnB;YAAA,CAAnB,GAA+C,IAHlB,CAA/B;gBAMMS,gBAAgB3c,OAAO0C,MAAP,CAAc,CAAA,CAAd,EAAkB+Z,UAAlB,CAAtB;mBAEOE,cAAcV,IAArB;mBACOU,cAAcT,KAArB;0BACcH,mBAAd,GAAoCW,sBAApC;gCAEoBF,CAApB,CAAA,GAAyBG,aAAzB;;;QAIC,IAAI1a,KAAI,CAAb,EAAgBA,KAAIqa,kCAAkC7c,MAAtD,EAA8DwC,IAA9D,CAAmE;YAC3Dwa,cAAaH,iCAAAA,CAAkCra,EAAlC,CAAnB;YACMya,0BAAyB1c,OAAO0C,MAAP,CAAc,CAAA,CAAd,EAC7B+Z,YAAWV,mBAAX,IAAkC,CAAA,CADL,EAE7BU,YAAWR,IAAX,GAAkB;YAACA,MAAMQ,YAAWR,IAAlB;QAAA,CAAlB,GAA4C,IAFf,EAG7BQ,YAAWP,KAAX,GAAmB;YAACA,OAAOO,YAAWP,KAAnB;QAAA,CAAnB,GAA+C,IAHlB,CAA/B;YAMMS,iBAAgB3c,OAAO0C,MAAP,CAAc,CAAA,CAAd,EAAkB+Z,WAAlB,CAAtB;eAEOE,eAAcV,IAArB;eACOU,eAAcT,KAArB;uBACcH,mBAAd,GAAoCW,uBAApC;0CAEkCza,EAAlC,CAAA,GAAuC0a,cAAvC;;;AAIJ,SAASC,8CAAT,CAAwDC,uBAAxD,EAAiF;WACxEA,wBAAwBnc,MAAxB,CAA+B,SAACkI,GAAD,EAAMiT,kBAAN,EAA6B;YAC3D1L,KAAKyL,wBAAwBC,kBAAxB,CAAX;YACMjb,MAAMuP,GAAG2M,WAAH,EAAZ;YAEIC,GAAJ,CAAQnc,GAAR,EAAA,EAAA,CAAA,MAAA,CAAA,oBAAkBgI,IAAIwK,GAAJ,CAAQxS,GAAR,KAAgB,EAAlC,GAAA;YAAuCib,kBAAvC;SAAA;eAEOjT,GAAP;KANK,EAOJ,IAAIoU,GAAJ,EAPI,CAAP;;AAUF,SAASC,6CAAT,CACEC,SADF,EAEEC,6BAFF,EAGE;QACI,CAACD,UAAUzd,MAAX,IAAqB,CAAC0d,8BAA8B1d,MAAxD,EAAgE;eACvD,EAAP;;QAGE0d,8BAA8B1d,MAA9B,GAAuCyd,UAAUzd,MAAjD,KAA4D,CAAhE,EAAmE;cAC3D,IAAI+B,KAAJ,CAAA,0LAEoB0b,UAAUzd,MAF9B,GAAA,uCAEyE0d,8BAA8B1d,MAFvG,CAAN;;;QAOI2d,qCACJR,+CAA+CO,6BAA/C,CADF;;uCAImCnU,OAAnC,CAA2C,SAACqU,WAAD,EAAiB;oBAC9CC,IAAZ,CACE,SAACxO,KAAD,EAAQyO,MAAR;mBAAmBzO,MAAM0O,gBAAN,CAAuBC,MAAvB,GAAgCF,OAAOC,gBAAP,CAAwBC,MAA3E;SADF;KADF;;QAOMC,sBAAsB,EAAA,CAAA,MAAA,CAAA,oBAAIR,SAAJ,GAAeI,IAAf,CAAoB,SAACxO,KAAD,EAAQyO,MAAR,EAAmB;eAC1DzO,MAAM6O,IAAN,CAAWC,WAAX,CAAuBH,MAAvB,GAAgCF,OAAOI,IAAP,CAAYC,WAAZ,CAAwBH,MAA/D;KAD0B,CAA5B;;;WAMO9d,MAAM4E,IAAN,CAAW6Y,mCAAmCS,MAAnC,EAAX,EAAwDC,OAAxD,CACL,SAACT,WAAD,EAAiB;eACRK,oBAAoB5c,GAApB,CAAwB,SAACoV,QAAD,EAAW5J,KAAX,EAAqB;mBAC3C;oBACD4J,SAAS/F,EADR;oCAEe;sCACAkN,WAAAA,CAAY/Q,KAAZ,CAAA,CAAmBkR,gBADnB;yCAEGH,WAAAA,CAAY/Q,KAAZ,CAAA,CAAmByP,mBAAAA;;aAJ5C;SADK,CAAP;KAFG,CAAP;;AAeK,SAASgC,cAAT,CAAA,IAAA,EAAqF;QAA5D1B,aAA4D,GAAA,KAA5DA,aAA4D,EAA7CQ,uBAA6C,GAAA,KAA7CA,uBAA6C,EAApBmB,iBAAoB,GAAA,KAApBA,iBAAoB;QACtFC,yBAAyB,KAA7B;QAEK,IAAIhc,IAAI,CAAb,EAAgBA,IAAIoa,cAAc5c,MAAlC,EAA0CwC,GAA1C,CAA+C;YACtCsa,mBADsC,GACfF,aAAAA,CAAcpa,CAAd,CADe,CACtCsa,mBADsC;YAGzCA,uBAAuBA,oBAAoB9c,MAA/C,EAAuD;qCAC5B,IAAzB;;;;QAKF,CAACwe,sBAAD,IACA,CAACpB,wBAAwBpd,MAF3B,EAGE;eACO;kCACiB,EADjB;iDAEgC4c;SAFvC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6CAoCuCA,aAAzC,EAAwDQ,uBAAxD;;;;;;;;;;;;;;;gCAgBqEA,wBAAwBnc,MAAxB,CAA+B,SAACkI,GAAD,EAAMiT,kBAAN,EAA6B;YAC3HA,mBAAmBE,mBAAnB,CAAuCmC,UAAvC,KAAsD,eAA1D,EAA2E;gBACrE,CAAJ,CAAA,CAAO/Z,IAAP,CAAY0X,kBAAZ;SADF,MAEO;gBACD,CAAJ,CAAA,CAAO1X,IAAP,CAAY0X,kBAAZ;;eAGKjT,GAAP;KAPmE,EAQlE;QAAC,EAAD;QAAK,EAAL;KARkE,CAnEqB,sEAmEnFuV,2BAnEmF,GAAA,sBAAA,CAAA,EAAA,EAmEtDhB,6BAnEsD,GAAA,sBAAA,CAAA,EAAA;QA4EpFiB,sCACJC,oEAAoE;mBACvDhC,aADuD;+CAE3BY,8CACrCZ,aADqC,EAErCc,6BAFqC;KAFzC,CADF;;;;;;;;;QAiBMmB,qCAAqCC,6BACzCH,mCADyC,EAEzCD,2BAFyC,EAGzCH,iBAHyC,CAA3C;WAMO;8BACiBre,MAAM4E,IAAN,CACpB+Z,mCAAmCT,MAAnC,EADoB,CADjB;;KAAP;;AAQF,SAASQ,mEAAT,CAAA,KAAA,EAGG;QAFDrI,SAEC,GAAA,MAFDA,SAEC,EADDwI,qCACC,GAAA,MADDA,qCACC;WACMxI,UAAUlV,GAAV,CAAc,SAACwX,IAAD,EAAU;YACvBmG,aAAanG,KAAKnI,EAAxB;;YAEMuO,uCACJF,sCACGhR,MADH,CACU,SAAA,KAAA;gBAAE2C,EAAF,GAAA,MAAEA,EAAF;mBAAUA,OAAOsO,UAAjB;SADV,EAEG3d,GAFH,CAEO,SAAA,KAAA;gBAAE+a,kBAAF,GAAA,MAAEA,kBAAF;mBAA2B;kCACZA,mBAAmB2B,gBADP;qCAET3B,mBAAmBE,mBAAAA;aAFrC;SAFP,CADF;YAQM4C,4BAA4B,CAACrG,KAAKiE,mBAAL,IAA4B,EAA7B,EAAiC9S,MAAjC,CAAwCiV,oCAAxC,CAAlC;YACMxF,SAASlZ,OAAO0C,MAAP,CAAc,CAAA,CAAd,EAAkB4V,IAAlB,EAAwB;iCAChBqG;SADR,CAAf;eAIOzF,MAAP;KAhBK,CAAP;;AAoBF,SAASqF,4BAAT,CAAsCH,mCAAtC,EAA2ED,2BAA3E,EAAwG9G,aAAxG,EAAuH;QAC/GiH,qCAAqC,IAAItB,GAAJ,EAA3C;QAEI,CAACoB,mCAAL,EAA0C;eAASE,kCAAP;;wCAERtV,OAApC,CAA4C,SAAA,KAAA,EAA2B;YAAzBuT,mBAAyB,GAAA,MAAzBA,mBAAyB;YACjE,CAACA,mBAAL,EAA0B;;;4BAENvT,OAApB,CAA4B,SAAC6S,kBAAD,EAAwB;wEACUA,kBAA5D,EAAgFyC,kCAAhF,EAAoHjH,aAApH;SADF;KAHF;gCAQ4BrO,OAA5B,CAAoC,SAAC6S,kBAAD,EAAwB;oEACEA,kBAA5D,EAAgFyC,kCAAhF,EAAoHjH,aAApH;KADF;WAIOiH,kCAAP;;AAGF,SAASM,2DAAT,CAAqE/C,kBAArE,EAAyFyC,kCAAzF,EAA6HjH,aAA7H,EAA4I;QACpIyE,cAAcD,mBAAmBE,mBAAvC;QACMC,aAAaJ,wBAAwBC,kBAAxB,CAAnB;QAEI,CAACG,UAAL,EAAiB;cACT,IAAIxa,KAAJ,CAAA,gFAC0E8D,KAAKC,SAAL,CAC5EsW,kBAD4E,CAD1E,CAAN;;QAOEyC,mCAAmCO,GAAnC,CAAuC7C,WAAWc,WAAX,EAAvC,CAAJ,EAAsE;YAC9DgC,8BACJR,mCAAmClL,GAAnC,CAAuC4I,WAAWc,WAAX,EAAvC,CADF;;YAIIgC,4BAA4Bhf,KAA5B,IAAqC,YAAYgf,4BAA4Bhf,KAAjF,EAAwF;wCAC1DA,KAA5B,GAAoC;wBAC1B,CAACif,OAAOD,4BAA4Bhf,KAA5B,CAAkC2d,MAAzC,IAAmDsB,OAAOlD,mBAAmB2B,gBAAnB,CAAoCC,MAA3C,CAApD,EAAwGuB,OAAxG,CAAgH,CAAhH,CAD0B;8BAEpBF,4BAA4Bhf,KAA5B,CAAkCmf,YAFd;sBAG5BH,4BAA4Bhf,KAA5B,CAAkCuB,IAAAA;aAH1C;;KANJ,MAYO;YACD0a,sBAAsB;wBACZ,qBADY;6BAEPD,YAAYoD,eAFL;8BAGNpD,YAAYqD,gBAHN;wBAIZrD,YAAYoC,UAJA;mBAKjBpC,YAAYhc,KALK;yBAMX,KANW;6BAOP;SAPnB;YAUI,UAAU+b,mBAAmBE,mBAAjC,EAAsD;gBAC9CqD,eAAe/H,cAAcrI,IAAd,CACnB,SAAA,KAAA;oBAAEiN,IAAF,GAAA,MAAEA,IAAF;uBAAYA,KAAKa,WAAL,OAAuBd,WAAWc,WAAX,EAAnC;aADmB,CAArB;gBAII,CAACsC,YAAL,EAAmB;sBACX,IAAI5d,KAAJ,CAAA,mBACawa,UADb,GAAA,wDAC6E1W,KAAKC,SAAL,CAC/E8R,aAD+E,CAD7E,CAAN;;kCAMoBrX,OAAO0C,MAAP,CAAc,CAAA,CAAd,EAAkBqZ,mBAAlB,EAAuC;sBACrDF,mBAAmBE,mBAAnB,CAAuCE,IADc;4BAE/CmD,aAAaC,UAFkC;sBAGrD;oCACY;oCACF,SADE;8BAER;qBAHJ;oCAKY,KALZ;0BAME,QANF;0BAOE;;aAVY,CAAtB;SAZF,MAyBO;kCACiBrf,OAAO0C,MAAP,CAAc,CAAA,CAAd,EAAkBqZ,mBAAlB,EAAuC;uBACpDF,mBAAmBE,mBAAnB,CAAuCG,KADa;sBAErD;oCACY;oCACF,SADE;+BAEP;qBAHL;oCAKY,KALZ;0BAME,QANF;0BAOE;;aATY,CAAtB;;2CAciCa,GAAnC,CAAuCf,WAAWc,WAAX,EAAvC,EAAiEf,mBAAjE;;;ACrWG,SAASuD,cAAT,GAA0B;WACxB;cACC,gBADD;cAEC,QAFD;wBAGW;8BACI,SADJ;4BAEE,SAFF;gBAGV,IAHU;mBAIP,OAJO;mBAKP,SALO;qBAML,SANK;6BAOG,gBAPH;iBAQT,QARS;mBASP,QATO;uBAUH,SAVG;kCAWQ,sBAXR;oBAYN;SAfL;wBAiBW;KAjBlB;;AAqBK,SAASC,eAAT,GAA2B;WACzB;cACC,kBADD;cAEC,QAFD;wBAGW;8BACI,WADJ;iCAEO,UAFP;gBAGV,IAHU;sBAIJ,KAJI;mBAKP,QALO;qBAML;SATN;wBAWW;KAXlB;;AAeF,SAASC,yBAAT,GAAqC;WAC5B;wBACW;6BACG,SADH;iCAEO;SAHlB;wBAKW,KALX;cAMC,QAND;cAOC;KAPR;;AAWK,SAASC,UAAT,CAAoBC,WAApB,EAAiC;;QAEhCxG,SAAS,CAAA,CAAf;QAEK,IAAMtY,GAAX,IAAkB8e,WAAlB,CAA+B;YACzBA,YAAY9c,cAAZ,CAA2BhC,GAA3B,KAAmCA,QAAQ,SAA/C,EAA0D;mBACjDA,GAAP,CAAA,GAAc8e,WAAAA,CAAY9e,GAAZ,CAAd;;;;;QAME+e,sBAAsB,CAAA,CAA5B;QAEID,YAAYhG,OAAhB,EAAyB;YAClB,IAAM9Y,IAAX,IAAkB8e,YAAYhG,OAA9B,CAAuC;gBACjCgG,YAAYhG,OAAZ,CAAoB9W,cAApB,CAAmChC,IAAnC,KAA2CA,SAAQ,OAAvD,EAAgE;oCAC1CA,IAApB,CAAA,GAA2B8e,YAAYhG,OAAZ,CAAoB9Y,IAApB,CAA3B;;;;;WAMCgf,OAAP,GAAiBF,YAAYG,KAA7B;WACOC,gBAAP,GAA0BJ,YAAYK,cAAtC;WACOrG,OAAP,GAAiBiG,mBAAjB;WACOte,IAAP,GAAcie,gBAAd;WAEOpG,MAAP;;AAGK,SAAS8G,sBAAT,CAAgCzD,mBAAhC,EAAqD0D,oBAArD,EAA2E;QAC5E,CAAC1D,mBAAL,EAA0B;eAAS,EAAP;;QAEtBrD,SAAS,EAAf;QAEK,IAAIjX,IAAI,CAAb,EAAgBA,IAAIsa,oBAAoB9c,MAAxC,EAAgDwC,GAAhD,CAAqD;YAC7Cwa,aAAaF,mBAAAA,CAAoBta,CAApB,CAAnB;YACIie,cAAc,IAAlB;YAEK,IAAI1D,IAAI,CAAb,EAAgBA,IAAIyD,qBAAqBxgB,MAAzC,EAAiD+c,GAAjD,CAAsD;gBAChDZ,wBAAwBa,UAAxB,MAAwCN,yBAAyB8D,oBAAAA,CAAqBzD,CAArB,CAAzB,CAA5C,EAA+F;8BAC/EyD,oBAAAA,CAAqBzD,CAArB,CAAd;;;;YAKA,CAAC0D,WAAL,EAAkB;kBACV,IAAI1e,KAAJ,CAAA,kDAA0D8D,KAAKC,SAAL,CAAekX,UAAf,CAA1D,CAAN;;YAGIX,cAAc9b,OAAO0C,MAAP,CAAc,CAAA,CAAd,EAAkBwd,WAAlB,CAApB;eAEO/b,IAAP,CAAY;6BACOsY,WAAWe,gBADlB;iCAEW1B,WAFX;kBAGJ0D;SAHR;;WAOKtG,MAAP;;AAGK,SAASiH,YAAT,CAAsBlK,KAAtB,EAA6BgK,oBAA7B,EAA0E;QAAvBG,aAAuB,GAAA,UAAA,MAAA,GAAA,KAAA,SAAA,CAAA,EAAA,KAAA,YAAA,SAAA,CAAA,EAAA,GAAP,KAAO;QAC3E,CAACnK,KAAD,IAAU,CAACtW,MAAMkB,OAAN,CAAcoV,KAAd,CAAf,EAAqC;eAAS,EAAP;;QAEjCiD,SAAS,EAAf;QAEK,IAAIjX,IAAI,CAAb,EAAgBA,IAAIgU,MAAMxW,MAA1B,EAAkCwC,GAAlC,CAAuC;YAC/BqW,OAAOrC,KAAAA,CAAMhU,CAAN,CAAb;YAEI,CAACqW,IAAD,IAAS,CAACA,KAAKoH,WAAf,IAA8B,CAACpH,KAAKoH,WAAL,CAAiBhG,OAApD,EAA6D;;;YAEvDQ,UAAUuF,WAAWnH,KAAKoH,WAAhB,CAAhB;YAEInD,sBAAsB,EAA1B;YAEI,CAAC6D,aAAL,EAAoB;gBACd;sCACoBJ,uBAAuB1H,KAAKiE,mBAAL,IAA4B,EAAnD,EAAuD0D,oBAAvD,CAAtB;aADF,CAEE,OAAOI,KAAP,EAAc;;wBAENA,KAAR,CAAc,qCAAd,EAAqDA,MAAMxH,OAA3D;sCACsB,EAAtB;;;eAIG1U,IAAP,CAAY;8BACQmU,KAAK1B,UADb;oDAAA;gBAGN0B,KAAKnI,EAHC;sBAIAmI,KAAKC,QAJL;mBAKHD,KAAKoH,WAAL,CAAiBhG,OAAjB,CAAyBwC,KALtB;4BAAA;yBAOG,KAPH;6BAQO,KARP;4BASM5D,KAAKpH,cATX;kBAUJqO;SAVR;;WAcKrG,MAAP;;AAGK,SAASoH,oBAAT,CAA8BC,IAA9B,EAAoC;QACrC,CAACA,IAAL,EAAW;eAAS;YAACN,sBAAsB,EAAvB;YAA2BO,wBAAwB,EAAnD;QAAA,CAAP;;QAETC,wBAAAA,KAAAA,CAAJ;QACIC,wBAAwB,KAA5B;QAEI;gCACsB3C,eAAe;2BACtBwC,KAAKtK,KAAL,IAAc,EADQ;qCAEZsK,KAAKhE,mBAAL,IAA4B,EAFhB;+BAGlBgE,KAAKlJ,aAAL,IAAsB,EAAA;SAHnB,CAAxB;KADF,CAME,OAAOgJ,KAAP,EAAc;;gBAENA,KAAR,CAAc,0BAAd,EAA0CA,MAAMxH,OAAhD;gCACwB,IAAxB;;QAGE8H,cAAAA,KAAAA,CAAJ;QAEID,qBAAJ,EAA2B;;;YAGnBE,4BAA4B,CAACL,KAAKtK,KAAL,IAAc,EAAf,EAAmBnV,GAAnB,CAAuB,SAACwX,IAAD,EAAU;gBAC3DuI,WAAW7gB,OAAO0C,MAAP,CAAc,CAAA,CAAd,EAAkB4V,IAAlB,CAAjB;qBAESiE,mBAAT,GAA+B,EAA/B;mBAEOsE,QAAP;SALgC,CAAlC;sBAOcV,aAAaS,yBAAb,EAAwC,EAAxC,EAA4C,IAA5C,CAAd;KAVF,MAWO;;sBAEST,aACZM,sBAAsBrC,mCAAtB,IAA6D,EADjD,EAEZqC,sBAAsBR,oBAAtB,IAA8C,EAFlC,CAAd;;WAMK;8BACiBS,wBAAwB,EAAxB,GAA+BD,yBAAyBA,sBAAsBR,oBAAhD,IAAyE,EADxH;gCAEmBU;KAF1B;;ACjMF,8GAAA;AACA,IAAMG,qBAAqB;iBACZ,IADY;WAElB,IAFkB;oBAGT,IAHS;WAIlB,KAJkB;sBAKP,IALO;kBAMX,IANW;eAOd,KAPc;mBAQV;CARjB;AAWO,SAASC,cAAT,CAAwBR,IAAxB,EAA8B;QAC/B,CAACA,IAAL,EAAW;eAAS,IAAP;;QAEPrH,SAASoH,qBAAqBC,IAArB,CAAf;QACMN,uBAAuB/G,OAAO+G,oBAApC;QACMO,yBAAyBtH,OAAOsH,sBAAtC;QAEMjK,gBAAgB;qBACPgK,KAAKhK,aAAL,IAAsBgK,KAAKhK,aAAL,CAAmBM,WAAAA;KADxD;QAIIP,QAAQ,IAAZ;QAEIiK,KAAKhK,aAAL,IAAsBgK,KAAKhK,aAAL,CAAmBD,KAA7C,EAAoD;gBAC1CiK,KAAKhK,aAAL,CAAmBD,KAA3B;;QAGEE,kBAAkB,IAAtB;QAEI+J,KAAKhK,aAAL,IACFgK,KAAKhK,aAAL,CAAmBE,0BADjB,IAEF8J,KAAKhK,aAAL,CAAmBE,0BAAnB,CAA8ChX,MAFhD,EAEwD;0BACpC8gB,KAAKhK,aAAL,CAAmBE,0BAAnB,CAA8C,CAA9C,CAAlB;;QAGEwI,eAAe,IAAnB;QACIrB,cAAc,IAAlB;QACIoD,iBAAiB,IAArB;QACIC,kBAAkB,IAAtB;QACIC,uBAAuB,IAA3B;QAEIX,KAAK5C,IAAT,EAAe;YACT4C,KAAK5C,IAAL,CAAUC,WAAd,EAA2B;2BACV2C,KAAK5C,IAAL,CAAUC,WAAV,CAAsBqB,YAArC;0BACcsB,KAAK5C,IAAL,CAAUC,WAAxB;;YAEE2C,KAAK5C,IAAL,CAAUqD,cAAd,EAA8B;6BACXT,KAAK5C,IAAL,CAAUqD,cAA3B;;YAEET,KAAK5C,IAAL,CAAUsD,eAAd,EAA+B;8BACXV,KAAK5C,IAAL,CAAUsD,eAA5B;;YAEEV,KAAK5C,IAAL,CAAUuD,oBAAd,EAAoC;mCACXX,KAAK5C,IAAL,CAAUuD,oBAAjC;;;QAIEC,mBAAmBZ,KAAKY,gBAAL,IAAyB,EAAlD;QACIC,aAAa,IAAjB;;;QAIIxD,WAAJ,EAAiB;qBACFyD,oBAAoBd,IAApB,EAA0B3C,WAA1B,CAAb;;QAGE0D,gBAAgB,IAApB;;;QAIIF,UAAJ,EAAgB;wBACEG,uBAAuBH,UAAvB,EAAmCH,eAAnC,EAAoDD,cAApD,CAAhB;;QAGEQ,SAASjB,KAAKkB,WAAlB;QAEID,MAAJ,EAAY;YACN;gBACI5O,MAAM,IAAI8O,GAAJ,CAAQF,MAAR,CAAZ;gBAEI,CAAC5O,IAAI+O,YAAJ,CAAiB9C,GAAjB,CAAqB,KAArB,CAAD,IAAgCjM,IAAI+O,YAAJ,CAAiBvO,GAAjB,CAAqB,KAArB,MAAgC,GAApE,EAAyE;oBACnEuO,YAAJ,CAAiB5E,GAAjB,CAAqB,KAArB,EAA4B,GAA5B;yBACSnK,IAAI1S,QAAJ,EAAT;;SALJ,CAOE,OAAOmgB,KAAP,EAAc;;;;;QAMZuB,kBAAkB5hB,OAAO0C,MAAP,CAAc;0CAAA;oCAAA;mBAGzB6d,KAAKsB,SAHoB;kCAAA;0BAKlBtB,KAAK3J,UALa;kDAAA;oBAAA;YAQhC2J,KAAKpQ,EAR2B;mBASzBqQ,sBATyB;gCAUZU,oBAVY;cAW9BX,KAAKlK,IAXyB;oBAYxBuH,WAZwB;sBAatBA,WAbsB;wCAAA;oCAAA;yBAgBnB0D,aAhBmB;8BAAA;sBAkBtBF,UAlBsB;kBAmB1BJ,kBAAkBc,sBAAsB7C,YAAtB,EAAoCrB,WAApC,CAnBQ;oBAoBxBoD,kBAAkBc,sBAAsB7C,YAAtB,EAAoCrB,WAApC,CApBM;mBAqBzB2C,KAAKwB,SArBoB;;KAAd,EAuBrBjB,kBAvBqB,CAAxB;yCAyBqCc,eAArC;WAEOA,eAAP;;;;;;;;;;;;;IAeF,SAASI,oCAAT,CAA8CC,QAA9C,EAAwD;;;QAIlDA,SAAShC,oBAAb,EAAmC;YAC5B,IAAIhe,IAAI,CAAb,EAAgBA,IAAIggB,SAAShC,oBAAT,CAA8BxgB,MAAlD,EAA0DwC,GAA1D,CAA+D;gBACzD,OAAOggB,SAAShC,oBAAT,CAA8Bhe,CAA9B,CAAA,CAAiCnC,KAAjC,CAAuCoiB,UAA9C,KAA6D,WAAjE,EAA8E;;;gBAIxEC,4BAA4BF,SAAShC,oBAAT,CAA8Bhe,CAA9B,CAAA,CAAiCnC,KAAjC,CAAuC2d,MAAvC,CAA8Cvd,QAA9C,EAAlC;gBAEI,CAACkiB,gCAAgCD,yBAAhC,CAAL,EAAiE;;;qBAIxDlC,oBAAT,CAA8Bhe,CAA9B,CAAA,CAAiCnC,KAAjC,CAAuC2d,MAAvC,GAAgD4E,yBAAyBF,yBAAzB,CAAhD;;;QAIAF,SAASjM,SAAb,EAAwB;YACjB,IAAI/T,KAAI,CAAb,EAAgBA,KAAIggB,SAASjM,SAAT,CAAmBvW,MAAvC,EAA+CwC,IAA/C,CAAoD;gBAC7C,IAAIua,IAAI,CAAb,EAAgBA,IAAIyF,SAASjM,SAAT,CAAmB/T,EAAnB,CAAA,CAAsBsa,mBAAtB,CAA0C9c,MAA9D,EAAsE+c,GAAtE,CAA2E;oBACnET,sBAAsBkG,SAASjM,SAAT,CAAmB/T,EAAnB,CAAA,CAAsBsa,mBAAtB,CAA0CC,CAA1C,CAAA,CAA6CT,mBAAzE;oBAEI,OAAOA,oBAAoBjc,KAApB,CAA0BoiB,UAAjC,KAAgD,WAApD,EAAiE;;;oBAI3DC,6BAA4BpG,oBAAoBjc,KAApB,CAA0B2d,MAA1B,CAAiCvd,QAAjC,EAAlC;oBAEI,CAACkiB,gCAAgCD,0BAAhC,CAAL,EAAiE;;;oCAI7CriB,KAApB,CAA0B2d,MAA1B,GAAmC4E,yBAAyBF,0BAAzB,CAAnC;;;;QAKFF,SAASX,aAAb,EAA4B;YACtBc,gCAAgCH,SAASX,aAAT,CAAuB7D,MAAvD,CAAJ,EAAoE;qBACzD6D,aAAT,CAAuB7D,MAAvB,GAAgC4E,yBAAyBJ,SAASX,aAAT,CAAuB7D,MAAhD,CAAhC;qBACS6E,eAAT,CAAyB7E,MAAzB,GAAkC4E,yBAAyBJ,SAASK,eAAT,CAAyB7E,MAAlD,CAAlC;;;QAIAwE,SAASb,UAAb,EAAyB;YACnBgB,gCAAgCH,SAASb,UAAT,CAAoB3D,MAApD,CAAJ,EAAiE;qBACtD2D,UAAT,CAAoB3D,MAApB,GAA6B4E,yBAAyBJ,SAASb,UAAT,CAAoB3D,MAA7C,CAA7B;qBACS8E,YAAT,CAAsB9E,MAAtB,GAA+B4E,yBAAyBJ,SAASM,YAAT,CAAsB9E,MAA/C,CAA/B;;;;;;;;;;IAYN,SAAS2E,+BAAT,CAAyC3E,MAAzC,EAAiD;QAC3C,CAACA,MAAD,IAAW,CAACA,OAAOvd,QAAP,GAAkBsiB,QAAlB,CAA2B,GAA3B,CAAhB,EAAiD;eACxC,KAAP;;QAGIC,mBAAmBhF,OAAOvd,QAAP,GAAkB6Y,KAAlB,CAAwB,GAAxB,CAAA,CAA6B,CAA7B,CAAzB;QAEI0J,iBAAiBhjB,MAAjB,KAA4B,CAA5B,IAAiCgjB,gBAAAA,CAAiB,CAAjB,CAAA,KAAwB,GAA7D,EAAkE;eACzD,IAAP;;WAGK,KAAP;;AAGF,SAASJ,wBAAT,CAAkCK,YAAlC,EAAgD;WACvCC,WAAWD,YAAX,EAAyB1D,OAAzB,CAAiC,CAAjC,CAAP;;AAGF,SAAS8C,qBAAT,CAA+B7C,YAA/B,EAA6CrB,WAA7C,EAA0D;WACjD;gBACG,KADH;kCAAA;cAGCA,eAAeA,YAAYvc,IAAAA;KAHnC;;AAOF,SAASggB,mBAAT,CAA6Bd,IAA7B,EAAmC3C,WAAnC,EAAgD;QAC1C,CAAC2C,KAAKY,gBAAN,IAA0B,CAACZ,KAAKY,gBAAL,CAAsB1hB,MAArD,EAA6D;eACpDme,WAAP;;;QAIEgF,gBAAgB,CAApB;QAEK,IAAI3gB,IAAI,CAAb,EAAgBA,IAAIse,KAAKY,gBAAL,CAAsB1hB,MAA1C,EAAkDwC,GAAlD,CAAuD;yBACpC0gB,WAAWpC,KAAKY,gBAAL,CAAsBlf,CAAtB,CAAA,CAAyB4gB,qBAAzB,CAA+CpF,MAA1D,CAAjB;;WAGK;gBACG,CAACkF,WAAW/E,YAAYH,MAAvB,IAAiCmF,aAAlC,EAAiD5D,OAAjD,CAAyD,CAAzD,CADH;sBAESpB,YAAYqB,YAFrB;cAGCrB,YAAYvc,IAAAA;KAHpB;;AAOF,SAASkgB,sBAAT,CAAgCH,UAAhC,EAA4CH,eAA5C,EAA6DD,cAA7D,EAA6E;QACrE8B,aAAa7B,kBAAkBA,gBAAgBxD,MAAlC,GAA2C,CAA9D;QACMsF,YAAY/B,iBAAiBA,eAAevD,MAAhC,GAAyC,CAA3D;WAEO;gBACG,CAACkF,WAAWvB,WAAW3D,MAAtB,IAAgCkF,WAAWG,UAAX,CAAhC,GAAyDH,WAAWI,SAAX,CAA1D,EAAiF/D,OAAjF,CAAyF,CAAzF,CADH;sBAESoC,WAAWnC,YAFpB;cAGCmC,WAAW/f,IAAAA;KAHnB;;ACvPF,IAAM2hB,mCAAmC;mCACR,eADQ;sCAEL,eAFK;gCAGX,eAHW;mDAIQ,eAJR;+BAKZ,SALY;+BAMZ,UANY;aAO9B,SAP8B;8BAQb,SARa;4BASf,SATe;6BAUd,SAVc;uBAWpB,SAXoB;8BAYb,qBAZa;wBAanB,SAbmB;qBActB,SAdsB;gCAeX,SAfW;kCAgBT,qBAhBS;mCAiBR,kCAjBQ;eAkB5B,WAlB4B;sBAmBrB,qBAnBqB;qBAoBtB,0BApBsB;mCAqBR,SArBQ;2BAsBhB,SAtBgB;kBAuBzB,SAvByB;mBAwBxB,UAxBwB;kCAyBT,eAzBS;wBA0BnB,6BA1BmB;+BA2BZ,SA3BY;uBA4BpB,SA5BoB;4BA6Bf;CA7B1B;AAgCA,IAAMC,qCAAqC;kCACX,qBADW;8BAEf,qBAFe;qCAGR;CAHnC;AAMA,SAASC,gBAAT,CAA0BC,UAA1B,EAAsC;WAC7BA,WAAWriB,GAAX,CAAe,SAAA,IAAA;YAAEmb,IAAF,GAAA,KAAEA,IAAF,EAAQ5S,KAAR,GAAA,KAAQA,KAAR,EAAewP,OAAf,GAAA,KAAeA,OAAf;eAA6B;;kBAE3CoD,OAAO+G,gCAAAA,CAAiC/G,IAAjC,CAAP,GAAgD7a,SAFL;wBAAA;;SAA7B;KAAf,CAAP;;AAQF,SAASgiB,cAAT,CAAwBC,QAAxB,EAAkC;WACzBA,SAASviB,GAAT,CAAa,SAAA,KAAA;YAAEmb,IAAF,GAAA,MAAEA,IAAF,EAAQpD,OAAR,GAAA,MAAQA,OAAR;eAAsB;;kBAElCoD,OAAOgH,kCAAAA,CAAmChH,IAAnC,CAAP,GAAkD7a,SAFhB;;SAAtB;KAAb,CAAP;;AAOa,SAASkiB,wBAAT,CAAkCH,UAAlC,EAA8CE,QAA9C,EAAwD;QAC/DE,gBAAgBJ,cAAcA,WAAW1jB,MAA/C;QACM+jB,cAAcH,YAAYA,SAAS5jB,MAAzC;QAEI,CAAC8jB,aAAD,IAAkB,CAACC,WAAvB,EAAoC;eAC3B,EAAP;;QAGIC,qBAAqBF,gBAAgBL,iBAAiBC,UAAjB,CAAhB,GAA+C,EAA1E;QACMO,mBAAmBF,cAAcJ,eAAeC,QAAf,CAAd,GAAyC,EAAlE;yCAEWI,kBAAX,GAAA,oBAAkCC,gBAAlC;;AC/Da,SAASC,kBAAT,CAA4BC,eAA5B,EAA6CnL,MAA7C,EAAqD;WAC3D,SAAA,IAAA,EAA0C;6BAAhCrE,IAAgC,EAAhCA,IAAgC,GAAA,cAAA,YAAzB,CAAA,CAAyB,GAAA,WAArB4E,MAAqB,GAAA,KAArBA,MAAqB,oBAAb3E,KAAa,EAAbA,KAAa,GAAA,eAAA,YAAL,CAAA,CAAK,GAAA;YACzCwP,WAAWzP,IAAAA,CAAKwP,eAAL,CAAjB;YACME,YAAYzP,KAAAA,CAAMuP,eAAN,CAAlB;YAEIC,YAAYA,SAAStD,IAAzB,EAA+B;mBACtB9H,OAAO9D,aAAP,CAAqBmP,UAAUvD,IAAV,CAAetK,KAApC,EAA2C;gBAACpB,UAAU,GAAX;YAAA,CAA3C,EAA4D5B,IAA5D,CAAiE,SAACgD,KAAD,EAAW;0BACvEsK,IAAV,CAAe7S,KAAf,CAAqBuI,KAArB,GAA6BA,KAA7B;oBACMwN,qBAAqBH,yBAAyBO,SAASV,UAAlC,EAA8CU,SAASR,QAAvD,CAA3B;oBAEI;2BACKrjB,OAAO0C,MAAP,CAAc,CAAA,CAAd,EACLqe,eAAe+C,UAAUvD,IAAzB,EAA+BqD,eAA/B,CADK,EAEL;wBAACT,YAAYM,kBAAb;wBAAiCzK,QAAQ8K,UAAUvD,IAAV,CAAevH,MAAxD;oBAAA,CAFK,CAAP;iBADF,CAKE,OAAOqH,KAAP,EAAc;2BACPrL,QAAQiE,MAAR,CAAeoH,KAAf,CAAP;;aAVG,CAAP;;YAeErH,UAAUA,OAAOvZ,MAArB,EAA6B;mBACpBuV,QAAQiE,MAAR,CAAe,IAAIzX,KAAJ,CAAU8D,KAAKC,SAAL,CAAeyT,MAAf,CAAV,CAAf,CAAP;;YAGE6K,YAAAA,CAAaA,SAASV,UAAT,CAAoB1jB,MAApB,IAA8BokB,SAASR,QAAT,CAAkB5jB,MAA7D,CAAJ,EAA0E;gBAClEgkB,qBAAqBH,yBAAyBO,SAASV,UAAlC,EAA8CU,SAASR,QAAvD,CAA3B;mBAEOrO,QAAQiE,MAAR,CAAewK,kBAAf,CAAP;;eAGKzO,QAAQiE,MAAR,CAAe,IAAIzX,KAAJ,CAAA,SAAiBoiB,eAAjB,GAAA,4CAAf,CAAP;KA9BF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACYF;;;QAIMG,mBAAAA,SAAAA,SAAAA;;;;;;;;;;;;;;;;;;;+BAaE5T,EAAAA,EAAI;;uBACD,IAAA,CAAKuI,aAAL,CACJ5E,IADI,CACCkQ,QADD,EACgB;oBAAC7T,IAAAA,EAAD;gBAAA,CADhB,EAEJ8C,IAFI,CAEC,SAAA,IAAA,EAAmB;wBAAjBoB,KAAiB,GAAA,KAAjBA,KAAiB,EAAVD,IAAU,GAAA,KAAVA,IAAU;2BAChB,IAAIY,OAAJ,CAAY,SAACC,OAAD,EAAUgE,MAAV,EAAqB;4BAClC;gCACIsH,OAAOnM,KAAKmM,IAAL,IAAanM,KAAK7D,IAA/B;gCAEI,CAACgQ,IAAL,EAAW;uCACFtL,QAAQ,IAAR,CAAP;;mCAGK,OAAKyD,aAAL,CACJ/D,aADI,CACUN,MAAMkM,IAAN,CAAWtK,KADrB,EAC4B;gCAACpB,UAAU,GAAX;4BAAA,CAD5B,EAEJ5B,IAFI,CAEC,SAACgD,KAAD,EAAW;sCACTsK,IAAN,CAAW7S,KAAX,CAAiBuI,KAAjB,GAAyBA,KAAzB;uCAEOhB,QAAQ8L,eAAe1M,MAAMkM,IAArB,CAAR,CAAP;6BALG,CAAP;yBAPF,CAcE,OAAOF,KAAP,EAAc;gCACVA,KAAJ,EAAW;uCACFA,KAAP;6BADF,MAEO;uCACE;oCAAC;wCAACxH,SAAS,gCAAV;oCAAA,CAAD;iCAAP;;;+BAIG5D,QAAQ,IAAR,CAAP;qBAvBK,CAAP;iBAHG,CAAP;;;;;qCAsDa;oBAARhT,CAAQ,GAAA,UAAA,MAAA,GAAA,KAAA,SAAA,CAAA,EAAA,KAAA,YAAA,SAAA,CAAA,EAAA,GAAJ,CAAA,CAAI;oBACP4T,QAAQ,IAAA,CAAK8C,WAAL,CAAiB3V,MAAjB,CAAwBf,CAAxB,CAAd;uBAEO,IAAA,CAAKyW,aAAL,CACJ5E,IADI,CACCmQ,QADD,EACqB;oBAACpO,OAAAA,KAAD;gBAAA,CADrB,EAEJ5C,IAFI,CAEC0Q,mBAAmB,YAAnB,EAAiC,IAAA,CAAKjL,aAAtC,CAFD,CAAP;;;;;6CAsBe3B,UAAAA,EAAwB;;oBAAZlB,KAAY,GAAA,UAAA,MAAA,GAAA,KAAA,SAAA,CAAA,EAAA,KAAA,YAAA,SAAA,CAAA,EAAA,GAAJ,CAAA,CAAI;4CAErC,IAAA,CAAK8C,WAAL,CAAiBuL,gBAAjB,CAAkCnN,UAAlC,EAA8ClB,KAA9C,CAFqC,EAChCmB,yBADgC,GAAA,sBAChCA,yBADgC,EACLC,mBADK,GAAA,sBACLA,mBADK;oBAGnCkN,UAAUnP,QAAQC,OAAR,EAAd;yBAESmP,UAAT,GAAsB;2BACb,IAAA,CAAK1L,aAAL,CACJ5E,IADI,CACCuQ,QADD,EACyBpN,mBADzB,EAEJhE,IAFI,CAEC0Q,mBAAmB,gBAAnB,EAAqC,IAAA,CAAKjL,aAA1C,CAFD,CAAP;;yBAKOwL,gBAAT,GAA4B;2BACnB,IAAA,CAAKxL,aAAL,CACJ5E,IADI,CACCwQ,QADD,EAC+BtN,yBAD/B,EAEJ/D,IAFI,CAEC0Q,mBAAmB,sBAAnB,EAA2C,IAAA,CAAKjL,aAAhD,CAFD,CAAP;;oBAKE,OAAOzB,oBAAoBZ,IAA3B,KAAoC,WAAxC,EAAqD;8BACzC8N,QAAQlR,IAAR,CAAa;+BAAMmR,WAAWjkB,IAAX,CAAA,OAAN;qBAAb,CAAV;;oBAGE6W,0BAA0BJ,UAA1B,CAAqCnX,MAAzC,EAAiD;8BACrC0kB,QAAQlR,IAAR,CAAa;+BAAMiR,iBAAiB/jB,IAAjB,CAAA,OAAN;qBAAb,CAAV;;uBAGKgkB,OAAP;;;;;wCAkBUpN,UAAAA,EAAYT,KAAAA,EAAO;oBACvBtL,YAAY,IAAA,CAAK2N,WAAL,CAAiB4L,WAAjB,CAA6BxN,UAA7B,EAAyCT,KAAzC,CAAlB;uBAEO,IAAA,CAAKoC,aAAL,CACJ5E,IADI,CACC0Q,QADD,EACkCxZ,SADlC,EAEJiI,IAFI,CAEC0Q,mBAAmB,yBAAnB,EAA8C,IAAA,CAAKjL,aAAnD,CAFD,CAAP;;;;;yCAoBW3B,UAAAA,EAA4B;oBAAhBf,SAAgB,GAAA,UAAA,MAAA,GAAA,KAAA,SAAA,CAAA,EAAA,KAAA,YAAA,SAAA,CAAA,EAAA,GAAJ,EAAI;oBACjChL,YAAY,IAAA,CAAK2N,WAAL,CAAiB8L,YAAjB,CAA8B1N,UAA9B,EAA0Cf,SAA1C,CAAlB;uBAEO,IAAA,CAAK0C,aAAL,CACJ5E,IADI,CACC4Q,QADD,EACuB1Z,SADvB,EAEJiI,IAFI,CAEC0Q,mBAAmB,cAAnB,EAAmC,IAAA,CAAKjL,aAAxC,CAFD,CAAP;;;;;wCAoBU3B,UAAAA,EAAYqI,YAAAA,EAAc;;;;;;;uBAO7B,IAAA,CAAK1G,aAAL,CAAmB5E,IAAnB,CAAwBkQ,QAAxB,EAAuC;oBAAC7T,IAAI4G,UAAL;gBAAA,CAAvC,EAAyD9D,IAAzD,CAA8D,SAAA,KAAA,EAAmB;wBAAjBoB,KAAiB,GAAA,MAAjBA,KAAiB,EAAVD,IAAU,GAAA,MAAVA,IAAU;2BAC/E,IAAIY,OAAJ,CAAY,SAACC,OAAD,EAAUgE,MAAV,EAAqB;4BAClC;gCACIsH,OAAOnM,KAAKmM,IAAL,IAAanM,KAAK7D,IAA/B;gCAEI,CAACgQ,IAAL,EAAW;uCACFtL,QAAQ,IAAR,CAAP;;mCAGK,OAAKyD,aAAL,CACJ/D,aADI,CACUN,MAAMkM,IAAN,CAAWtK,KADrB,EAC4B;gCAACpB,UAAU,GAAX;4BAAA,CAD5B,EAEJ5B,IAFI,CAEC,SAACgD,KAAD,EAAW;sCACTsK,IAAN,CAAW7S,KAAX,CAAiBuI,KAAjB,GAAyBA,KAAzB;uCAEOhB,QAAQZ,MAAMkM,IAAd,CAAP;6BALG,CAAP;yBAPF,CAcE,OAAOF,KAAP,EAAc;gCACVA,KAAJ,EAAW;uCACFA,KAAP;6BADF,MAEO;uCACE;oCAAC;wCAACxH,SAAS,gCAAV;oCAAA,CAAD;iCAAP;;;+BAIG5D,QAAQ,IAAR,CAAP;qBAvBK,CAAP;iBADK,EA0BJhC,IA1BI,CA0BC,SAACgP,QAAD,EAAc;wBACd0C,gBAAgB1C,SAAS5K,aAAT,CAAuBvW,GAAvB,CACpB,SAACmb,IAAD;+BAAUA,KAAKA,IAAf;qBADoB,CAAtB;wBAIMjR,YAAY,OAAK2N,WAAL,CAAiBiM,WAAjB,CAChB7N,UADgB,EAEhB4N,cAAclb,MAAd,CAAqB2V,YAArB,CAFgB,CAAlB;2BAKO,OAAK1G,aAAL,CACJ5E,IADI,CACC+Q,QADD,EACkC7Z,SADlC,EAEJiI,IAFI,CAGH0Q,mBAAmB,yBAAnB,EAA8C,OAAKjL,aAAnD,CAHG,CAAP;iBApCK,CAAP;;;;;2CAyDa3B,UAAAA,EAAY;oBACnB/L,YAAY,IAAA,CAAK2N,WAAL,CAAiBmM,cAAjB,CAAgC/N,UAAhC,CAAlB;uBAEO,IAAA,CAAK2B,aAAL,CACJ5E,IADI,CACC+Q,QADD,EACkC7Z,SADlC,EAEJiI,IAFI,CAEC0Q,mBAAmB,yBAAnB,EAA8C,IAAA,CAAKjL,aAAnD,CAFD,CAAP;;;;;yCAoBW3B,UAAAA,EAAYQ,aAAAA,EAAe;oBAChCvM,YAAY,IAAA,CAAK2N,WAAL,CAAiBoM,YAAjB,CAA8BhO,UAA9B,EAA0CQ,aAA1C,CAAlB;uBAEO,IAAA,CAAKmB,aAAL,CACJ5E,IADI,CACCkR,QADD,EACkCha,SADlC,EAEJiI,IAFI,CAEC0Q,mBAAmB,yBAAnB,EAA8C,IAAA,CAAKjL,aAAnD,CAFD,CAAP;;;;;2CAoBa3B,UAAAA,EAAYS,iBAAAA,EAAmB;oBACtCxM,YAAY,IAAA,CAAK2N,WAAL,CAAiBsM,cAAjB,CAChBlO,UADgB,EAEhBS,iBAFgB,CAAlB;uBAKO,IAAA,CAAKkB,aAAL,CACJ5E,IADI,CACCoR,QADD,EACkCla,SADlC,EAEJiI,IAFI,CAEC0Q,mBAAmB,yBAAnB,EAA8C,IAAA,CAAKjL,aAAnD,CAFD,CAAP;;;;;4CAoBc3B,UAAAA,EAA0B;oBAAdW,OAAc,GAAA,UAAA,MAAA,GAAA,KAAA,SAAA,CAAA,EAAA,KAAA,YAAA,SAAA,CAAA,EAAA,GAAJ,EAAI;oBAClC1M,YAAY,IAAA,CAAK2N,WAAL,CAAiBwM,eAAjB,CAAiCpO,UAAjC,EAA6CW,OAA7C,CAAlB;uBAEO,IAAA,CAAKgB,aAAL,CACJ5E,IADI,CACCsR,QADD,EAC0Bpa,SAD1B,EAEJiI,IAFI,CAEC0Q,mBAAmB,iBAAnB,EAAsC,IAAA,CAAKjL,aAA3C,CAFD,CAAP;;;;;6CAoBe3B,UAAAA,EAAYf,SAAAA,EAAW;;uBAC/B,IAAA,CAAKhD,KAAL,CAAW+D,UAAX,EACJ9D,IADI,CACC,SAACgP,QAAD,EAAc;wBACZvK,UAAUuK,SAASjM,SAAT,CAAmBlV,GAAnB,CAAuB,SAACoV,QAAD;+BAAcA,SAAS/F,EAAvB;qBAAvB,CAAhB;2BAEO,OAAKgV,eAAL,CAAqBpO,UAArB,EAAiCW,OAAjC,CAAP;iBAJG,EAMJzE,IANI,CAMC,YAAM;2BACH,OAAKwR,YAAL,CAAkB1N,UAAlB,EAA8Bf,SAA9B,CAAP;iBAPG,CAAP;;;;;4CAgCce,UAAAA,EAAYf,SAAAA,EAAW;oBAC/BhL,YAAY,IAAA,CAAK2N,WAAL,CAAiB0M,eAAjB,CAAiCtO,UAAjC,EAA6Cf,SAA7C,CAAlB;uBAEO,IAAA,CAAK0C,aAAL,CACJ5E,IADI,CACCwR,QADD,EAC0Bta,SAD1B,EAEJiI,IAFI,CAEC0Q,mBAAmB,iBAAnB,EAAsC,IAAA,CAAKjL,aAA3C,CAFD,CAAP;;;;;kDA+BoB3B,UAAAA,EAAYP,eAAAA,EAAiB;oBAC3CxL,YAAY,IAAA,CAAK2N,WAAL,CAAiB4M,qBAAjB,CAChBxO,UADgB,EAEhBP,eAFgB,CAAlB;uBAKO,IAAA,CAAKkC,aAAL,CACJ5E,IADI,CACC0Q,QADD,EACkCxZ,SADlC,EAEJiI,IAFI,CAEC0Q,mBAAmB,yBAAnB,EAA8C,IAAA,CAAKjL,aAAnD,CAFD,CAAP;;;;;EA9Z2BF;ACpB/B;;IAGA,IAAA,eAAe;;;;;;;;;;;;;;;kBAAA,EAAA,SAAA,aAiBAgN,KAjBA,EAAA,IAAA,EAiB8B;YAAtBC,QAAsB,GAAA,KAAtBA,QAAsB,EAAZC,SAAY,GAAA,KAAZA,SAAY;YACnCC,WAAWH,MAAMI,GAAN,CAAU7M,KAAV,CAAgB,GAAhB,CAAjB;YACM8M,WAAWF,QAAAA,CAAS,CAAT,CAAjB;YACM/R,QAAQ+R,QAAAA,CAAS,CAAT,CAAA,GAAA,MAAkBA,QAAAA,CAAS,CAAT,CAAlB,GAAkC,EAAhD;;YAGMG,cAAcD,SAAS9M,KAAT,CAAe,GAAf,CAApB;;YAGMgN,iBAAiBD,YAAYrmB,MAAZ,GAAqB,CAA5C;oBAEYsmB,cAAZ,CAAA,GAAiCD,WAAAA,CAAYC,cAAZ,CAAjC,GAAA,MAAgEN,QAAhE,GAAA,MAA4EC,SAA5E;oBAEUI,YAAYxmB,IAAZ,CAAiB,GAAjB,CAAV,GAAkCsU,KAAlC;;CA9BJ;ACAA;;;QAIMoS,gBAAAA,SAAAA,SAAAA;;;;;;;;;mCACU;uBACLC,YAAP;;;;;EAFwBzN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACE5B,UAAA;AAGA;;;;;;;;;QAUMhF,SAAAA;;;;;;oCAKe0S,MAAAA,EAAQC,aAAAA,EAAe;oBAClCC,YAAY,IAAI9Q,MAAJ,CAAW4Q,MAAX,CAAlB;oBACMzN,SAAS,IAAIjF,MAAJ,CAAW4S,SAAX,EAAsBC,QAAtB,EAAuCF,aAAvC,CAAf;uBAEOD,MAAP,GAAgBE,SAAhB;uBAEO3N,MAAP;;;;oBAOUyN,MAAZ,EAAyE;YAArDI,kBAAqD,GAAA,UAAA,MAAA,GAAA,KAAA,SAAA,CAAA,EAAA,KAAA,YAAA,SAAA,CAAA,EAAA,GAAhCD,QAAgC;YAAfF,aAAe,GAAA,SAAA,CAAA,EAAA;;YACjEvT,MAAAA,aAAiBsT,OAAOK,MAAxB,GAAA,sBAAN;YAEMxT,UAAU;6BACG,YADH;6BAEGyT,OAFH;iDAGuBN,OAAOO,qBAAAA;SAH9C;YAMIP,OAAOvjB,MAAX,EAAmB;oBACT,sBAAR,CAAA,GAAkCujB,OAAOvjB,MAAzC;;YAGI+jB,iBAAiBR,OAAOvQ,QAAP,GAAkBuQ,OAAOvQ,QAAzB,GAAoC,GAA3D;gBAEQ,iBAAR,CAAA,GAA6B+Q,cAA7B;YAEIP,aAAJ,EAAmB;oBACT,cAAR,CAAA,GAA0B,kBAA1B;oBACQQ,MAAR,GAAiB,kBAAjB;iBAEKjO,aAAL,GAAqB,IAAI4N,kBAAJ,CAAuBhlB,KAAvB,EAA8B;yBACxC,SAASuR,OAAT,CAAiBC,aAAjB,EAAgC;2BAChCqT,cAAcvT,GAAd,EAAmB;8BAClBtN,KAAKC,SAAL,CAAeuN,aAAf,CADkB;gCAEhB,MAFgB;8BAGlB,MAHkB;;qBAAnB,EAKJG,IALI,CAKC,SAACC,QAAD;+BAAcA,SAASG,IAAT,EAAd;qBALD,CAAP;;aAFiB,CAArB;SAJF,MAcO;iBACAqF,aAAL,GAAqB,IAAI4N,kBAAJ,CAAuBhlB,KAAvB,EAA8B;wBAAA;gCAEjC;oBAACyR,SAAAA,OAAD;gBAAA;aAFG,CAArB;;aAMG2G,OAAL,GAAe,IAAIY,eAAJ,CAAoB,IAAA,CAAK5B,aAAzB,CAAf;aACKuB,UAAL,GAAkB,IAAIiB,kBAAJ,CAAuB,IAAA,CAAKxC,aAA5B,CAAlB;aACKkO,IAAL,GAAY,IAAInL,YAAJ,CAAiB,IAAA,CAAK/C,aAAtB,CAAZ;aACKuJ,QAAL,GAAgB,IAAI8B,gBAAJ,CAAqB,IAAA,CAAKrL,aAA1B,CAAhB;aACK8M,KAAL,GAAa,IAAIQ,aAAJ,CAAkB,IAAA,CAAKtN,aAAvB,CAAb;;;;;;;;;;;;;;;0CAcYmO,MAAAA,EAAQ;uBACb,IAAA,CAAKnO,aAAL,CAAmBpE,aAAnB,CAAiCuS,MAAjC,CAAP", "debugId": null}}]}